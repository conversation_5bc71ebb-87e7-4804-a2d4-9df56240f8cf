
interface WorkExperience {
  company: string;
  position: string;
  duration?: string;
  description?: string;
}

interface Education {
  institution: string;
  degree: string;
  year?: string;
}

interface ContactInfo {
  email?: string;
  name?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
}

interface ExtractedResumeData {
  text: string;
  contactInfo: ContactInfo;
  workExperience?: WorkExperience[];
  education?: Education[];
  skills?: string[];
}

// Enhanced function to extract contact information
export const extractContactInfo = (text: string): ContactInfo => {
  const info: ContactInfo = {};
  
  // Extract email with better regex
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
  const emails = text.match(emailRegex);
  if (emails && emails.length > 0) {
    info.email = emails[0].toLowerCase();
  }
  
  // Extract phone number (supports various formats)
  const phoneRegex = /(?:\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}/g;
  const phones = text.match(phoneRegex);
  if (phones && phones.length > 0) {
    // Filter out years and other non-phone numbers
    const validPhone = phones.find(phone => 
      phone.replace(/\D/g, '').length >= 10 && 
      !phone.match(/^\d{4}$/) // Not a year
    );
    if (validPhone) {
      info.phone = validPhone.trim();
    }
  }
  
  // Extract LinkedIn profile
  const linkedinRegex = /(?:https?:\/\/)?(?:www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+/gi;
  const linkedinMatch = text.match(linkedinRegex);
  if (linkedinMatch) {
    info.linkedin = linkedinMatch[0];
  }
  
  // Extract location (common patterns)
  const locationPatterns = [
    /(?:Address|Location|Based in|Located in)[\s:]+([A-Z][a-z]+(?:,?\s+[A-Z]{2})?(?:,?\s+[A-Z][a-z]+)*)/gi,
    /([A-Z][a-z]+,\s*[A-Z]{2}(?:\s+\d{5})?)/g, // City, State format
    /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*,\s*[A-Z][a-z]+)/g // City, Country format
  ];
  
  for (const pattern of locationPatterns) {
    const locationMatch = text.match(pattern);
    if (locationMatch) {
      info.location = locationMatch[0].replace(/(?:Address|Location|Based in|Located in)[\s:]+/gi, '').trim();
      break;
    }
  }
  
  // Extract name (improved logic)
  const lines = text.split('\n').filter(line => line.trim().length > 0);
  
  for (let i = 0; i < Math.min(lines.length, 15); i++) {
    const line = lines[i].trim();
    
    // Skip lines that are too long or contain common resume keywords
    if (line.length > 60) continue;
    
    const skipKeywords = [
      'resume', 'cv', 'curriculum', 'vitae', 'experience', 'education', 
      'skills', 'objective', 'summary', 'phone', 'email', 'address',
      'linkedin', 'github', 'portfolio', 'website'
    ];
    
    if (skipKeywords.some(keyword => line.toLowerCase().includes(keyword))) continue;
    
    // Check if line looks like a name
    const words = line.split(/\s+/);
    if (words.length >= 2 && words.length <= 4) {
      const isName = words.every(word => 
        /^[A-Z][a-z]*$/.test(word) || // Capitalized word
        /^[A-Z]\.$/.test(word) || // Initial
        /^(de|van|von|della|di|da|del|la|le|jr|sr|ii|iii)$/i.test(word) // Name particles
      );
      
      if (isName && !line.match(/\d/) && line.length > 4) {
        info.name = line;
        break;
      }
    }
  }
  
  console.log("Extracted contact info:", info);
  return info;
};

export const extractSections = (text: string): Record<string, string> => {
  const sections: Record<string, string> = {};
  
  // Common section headers with variations
  const sectionPatterns = [
    { key: 'experience', patterns: ['experience', 'work experience', 'employment', 'professional experience', 'career history'] },
    { key: 'education', patterns: ['education', 'academic background', 'qualifications', 'academic qualifications'] },
    { key: 'skills', patterns: ['skills', 'technical skills', 'competencies', 'expertise', 'core competencies'] },
    { key: 'projects', patterns: ['projects', 'achievements', 'accomplishments'] },
    { key: 'certifications', patterns: ['certifications', 'certificates', 'licenses'] }
  ];
  
  const lines = text.split('\n');
  let currentSection = '';
  let currentContent = '';
  
  for (const line of lines) {
    const lowerLine = line.toLowerCase().trim();
    
    // Check if this line is a section header
    let matchedSection = '';
    for (const section of sectionPatterns) {
      if (section.patterns.some(pattern => 
        lowerLine === pattern || 
        (lowerLine.includes(pattern) && lowerLine.length < 50)
      )) {
        matchedSection = section.key;
        break;
      }
    }
    
    if (matchedSection) {
      // Save previous section
      if (currentSection) {
        sections[currentSection] = currentContent.trim();
      }
      currentSection = matchedSection;
      currentContent = '';
    } else if (currentSection) {
      currentContent += line + '\n';
    }
  }
  
  // Save last section
  if (currentSection) {
    sections[currentSection] = currentContent.trim();
  }
  
  return sections;
};

export const extractWorkExperience = (experienceText: string): WorkExperience[] => {
  const experiences: WorkExperience[] = [];
  
  // Split by common separators and analyze each block
  const blocks = experienceText.split(/\n\s*\n/);
  
  for (const block of blocks) {
    if (block.trim().length < 20) continue; // Skip short blocks
    
    const lines = block.split('\n').map(line => line.trim()).filter(line => line);
    if (lines.length === 0) continue;
    
    // Try to identify job title, company, and dates
    let position = '';
    let company = '';
    let duration = '';
    let description = '';
    
    // Look for patterns like "Job Title at Company (Date)"
    const titleCompanyPattern = /^(.+?)\s+(?:at|@|-)\s+(.+?)(?:\s*\(([^)]+)\))?$/;
    const firstLineMatch = lines[0].match(titleCompanyPattern);
    
    if (firstLineMatch) {
      position = firstLineMatch[1].trim();
      company = firstLineMatch[2].trim();
      duration = firstLineMatch[3]?.trim() || '';
    } else {
      // Fallback: first line is position, second might be company
      position = lines[0];
      if (lines.length > 1) {
        // Look for company indicators
        const companyLine = lines.find(line => 
          line.includes('Company') || 
          line.includes('Corp') || 
          line.includes('Inc') ||
          line.includes('Ltd') ||
          /^[A-Z][a-zA-Z\s&,.-]+$/.test(line)
        );
        if (companyLine) {
          company = companyLine;
        }
      }
    }
    
    // Look for date patterns
    if (!duration) {
      const datePattern = /(\d{4}|\w+\s+\d{4}|\d{1,2}\/\d{4})/g;
      const dates = block.match(datePattern);
      if (dates && dates.length >= 1) {
        duration = dates.join(' - ');
      }
    }
    
    // Collect description (remaining lines)
    const descriptionLines = lines.slice(1).filter(line => 
      !line.includes(company) && 
      !line.match(/^\d{4}/) && 
      line.length > 10
    );
    description = descriptionLines.join(' ');
    
    if (position && position.length > 2) {
      experiences.push({
        position: position,
        company: company || 'Not specified',
        duration: duration,
        description: description
      });
    }
  }
  
  return experiences;
};

export const extractEducation = (educationText: string): Education[] => {
  const education: Education[] = [];
  
  const lines = educationText.split('\n').map(line => line.trim()).filter(line => line);
  
  for (const line of lines) {
    if (line.length < 10) continue;
    
    // Look for degree patterns
    const degreePatterns = [
      /(?:Bachelor|Master|PhD|Doctorate|Associate|Diploma|Certificate).*?(?:from|at|-)\s*(.+?)(?:\s*\((\d{4})\))?$/i,
      /(.+?)\s+(?:from|at|-)\s*(.+?)(?:\s*\((\d{4})\))?$/
    ];
    
    for (const pattern of degreePatterns) {
      const match = line.match(pattern);
      if (match) {
        const degree = match[1]?.trim() || line;
        const institution = match[2]?.trim() || '';
        const year = match[3]?.trim() || '';
        
        education.push({
          degree: degree,
          institution: institution,
          year: year
        });
        break;
      }
    }
  }
  
  return education;
};

export const extractSkills = (text: string): string[] => {
  const skills: Set<string> = new Set();
  
  // Common technical skills to look for
  const techSkills = [
    'JavaScript', 'TypeScript', 'Python', 'Java', 'C++', 'C#', 'Ruby', 'Go', 'Rust', 'PHP',
    'React', 'Angular', 'Vue', 'Node.js', 'Express', 'Django', 'Flask', 'Spring', 'Laravel',
    'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'CI/CD',
    'SQL', 'NoSQL', 'MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'Oracle',
    'Machine Learning', 'AI', 'Deep Learning', 'TensorFlow', 'PyTorch',
    'ERP', 'SAP', 'Salesforce', 'CRM', 'Project Management', 'Agile', 'Scrum'
  ];
  
  // Look for skills in the text (case insensitive)
  techSkills.forEach(skill => {
    if (text.toLowerCase().includes(skill.toLowerCase())) {
      skills.add(skill);
    }
  });
  
  // Look for skills in bullet points or comma-separated lists
  const skillsPattern = /(?:skills|technologies|tools|expertise|competencies)[\s:]*([^\n]+)/gi;
  let match;
  while ((match = skillsPattern.exec(text)) !== null) {
    const skillsList = match[1].split(/[,;•\|]/);
    skillsList.forEach(skill => {
      const trimmed = skill.trim();
      if (trimmed.length > 2 && trimmed.length < 30 && /[a-zA-Z]/.test(trimmed)) {
        skills.add(trimmed);
      }
    });
  }
  
  return Array.from(skills);
};

export const extractDetailedResumeInfo = (text: string): ExtractedResumeData => {
  // Clean the text first
  const cleanedText = text.replace(/[^\x20-\x7E\n\r\t]/g, ' ').replace(/\s+/g, ' ');
  
  // Extract contact information
  const contactInfo = extractContactInfo(cleanedText);
  
  // Split text into sections
  const sections = extractSections(cleanedText);
  
  return {
    text: cleanedText,
    contactInfo,
    workExperience: extractWorkExperience(sections.experience || ''),
    education: extractEducation(sections.education || ''),
    skills: extractSkills(sections.skills || cleanedText)
  };
};
