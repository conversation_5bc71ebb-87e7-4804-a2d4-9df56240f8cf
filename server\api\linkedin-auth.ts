import { Router } from 'express';
import { linkedinService } from '../services/linkedinService';

const router = Router();

// Get LinkedIn OAuth URL for authentication
router.get('/auth-url', (req, res) => {
  try {
    const clientId = process.env.LINKEDIN_CLIENT_ID;
    const redirectUri = `${req.protocol}://${req.get('host')}/api/linkedin/callback`;
    
    // LinkedIn OAuth 2.0 scopes for talent solutions
    const scopes = [
      'r_liteprofile',
      'r_emailaddress',
      'w_member_social',
      'r_organization_social',
      'rw_organization_admin'
    ].join('%20');
    
    const state = Math.random().toString(36).substring(7);
    req.session.linkedinState = state;
    
    const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&state=${state}&scope=${scopes}`;
    
    res.json({ authUrl });
  } catch (error) {
    console.error('LinkedIn auth URL generation failed:', error);
    res.status(500).json({ error: 'Failed to generate LinkedIn auth URL' });
  }
});

// Handle LinkedIn OAuth callback
router.get('/callback', async (req, res) => {
  try {
    const { code, state } = req.query;
    
    if (!code) {
      return res.status(400).json({ error: 'Authorization code not provided' });
    }
    
    if (state !== req.session.linkedinState) {
      return res.status(400).json({ error: 'Invalid state parameter' });
    }
    
    const clientId = process.env.LINKEDIN_CLIENT_ID;
    const clientSecret = process.env.LINKEDIN_CLIENT_SECRET;
    const redirectUri = `${req.protocol}://${req.get('host')}/api/linkedin/callback`;
    
    // Exchange authorization code for access token
    const tokenResponse = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code as string,
        redirect_uri: redirectUri,
        client_id: clientId!,
        client_secret: clientSecret!,
      }),
    });
    
    const tokenData = await tokenResponse.json();
    
    if (tokenData.error) {
      console.error('LinkedIn token exchange failed:', tokenData);
      return res.status(400).json({ error: 'Failed to exchange code for token' });
    }
    
    // Store the access token (in production, store in database)
    req.session.linkedinAccessToken = tokenData.access_token;
    
    // Redirect to frontend success page
    res.redirect('/candidate-search?linkedin=connected');
  } catch (error) {
    console.error('LinkedIn callback error:', error);
    res.status(500).json({ error: 'LinkedIn authentication failed' });
  }
});

// Check LinkedIn connection status
router.get('/status', (req, res) => {
  const isConnected = !!req.session.linkedinAccessToken || linkedinService.isConfigured();
  res.json({ 
    connected: isConnected,
    hasCredentials: !!process.env.LINKEDIN_CLIENT_ID && !!process.env.LINKEDIN_CLIENT_SECRET
  });
});

// Test LinkedIn API connection
router.get('/test', async (req, res) => {
  try {
    if (!linkedinService.isConfigured()) {
      return res.status(400).json({ error: 'LinkedIn not configured' });
    }
    
    // Test basic API call
    const testResult = await linkedinService.searchPeople({
      keywords: 'software engineer',
      count: 1
    });
    
    res.json({ 
      success: true, 
      message: 'LinkedIn API connection successful',
      sampleResultCount: testResult.length
    });
  } catch (error) {
    console.error('LinkedIn test failed:', error);
    res.status(500).json({ 
      error: 'LinkedIn API test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as linkedinAuthRouter };