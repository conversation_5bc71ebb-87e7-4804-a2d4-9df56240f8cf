import { Router } from 'express';
import { db } from '../db';
import { voiceCalls, candidates, jobPostings } from '../../shared/schema';
import { eq } from 'drizzle-orm';
import { processCallSummaryAsync } from '../services/conversationSummarizationService';
// import { enhancedVoiceCallManager } from '../services/enhancedVoiceCallManager'; // TODO: Restore when needed

const router = Router();

// Webhook for when ElevenLabs conversation is initiated
router.post('/conversation-initiation', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎯 ElevenLabs Conversation Initiation Webhook received - conversation_id:', req.body.conversation_id);
    }
    
    const { 
      conversation_id, 
      agent_id, 
      call_metadata 
    } = req.body;

    // Extract call information from metadata
    const callId = call_metadata?.call_id;
    
    if (callId) {
      // 🎯 CRITICAL FIX: Store conversation_id in dedicated column for reliable webhook matching
      await db.update(voiceCalls)
        .set({
          status: 'in_progress',
          startedAt: new Date(),
          elevenlabsConversationId: conversation_id, // Store in dedicated column
          conversationNotes: JSON.stringify({
            conversationId: conversation_id,
            agentId: agent_id,
            platform: 'elevenlabs_conversational_ai'
          })
        })
        .where(eq(voiceCalls.id, callId));
      
      console.log(`✅ Updated call ${callId} with conversation ID ${conversation_id} in dedicated column`);
    }

    // Respond with conversation context for ElevenLabs agent
    res.json({
      success: true,
      agent_override: {
        // Provide dynamic context to the agent
        prompt_variables: {
          current_time: new Date().toLocaleTimeString(),
          conversation_id: conversation_id
        }
      }
    });

  } catch (error: any) {
    console.error('❌ Conversation initiation webhook error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Webhook for when ElevenLabs conversation ends
router.post('/conversation-ended', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🏁 ElevenLabs Conversation Ended Webhook received - conversation_id:', req.body.conversation_id);
    }
    
    // Handle both legacy and new ElevenLabs payload formats
    const data = req.body.data || req.body; // New format has nested data
    const {
      conversation_id,
      agent_id,
      transcript: rawTranscript,
      metadata,
      analysis
    } = data;

    // Extract duration from correct location
    const durationSeconds = metadata?.call_duration_secs || req.body.duration_ms / 1000 || 0;
    const endReason = metadata?.termination_reason || req.body.end_reason || 'unknown';
    
    // Parse transcript from array format or use summary
    let processedTranscript = '';
    if (analysis?.transcript_summary) {
      // Use the summarized transcript if available - it's cleaner
      processedTranscript = analysis.transcript_summary;
    } else if (Array.isArray(rawTranscript)) {
      // Parse transcript array format
      processedTranscript = rawTranscript
        .map(item => {
          if (typeof item === 'object' && item.message) {
            return `${item.role || 'Speaker'}: ${item.message}`;
          }
          return typeof item === 'string' ? item : JSON.stringify(item);
        })
        .join('\n');
    } else if (typeof rawTranscript === 'string') {
      processedTranscript = rawTranscript;
    }

    // 🎯 RELIABLE WEBHOOK MATCHING: Use dedicated conversation ID column
    let callRecord = null;
    if (conversation_id) {
      // Primary lookup: Use dedicated elevenlabsConversationId column
      const callRecords = await db.select()
        .from(voiceCalls)
        .where(eq(voiceCalls.elevenlabsConversationId, conversation_id))
        .limit(1);
      
      if (callRecords.length > 0) {
        callRecord = callRecords[0];
        console.log(`✅ Found call ${callRecord.id} by conversation ID ${conversation_id}`);
      } else {
        // 🔄 FALLBACK: For calls created before the schema update
        console.log(`⚠️ Primary lookup failed, trying JSON fallback for conversation ID: ${conversation_id}`);
        
        const inProgressCalls = await db.select()
          .from(voiceCalls)
          .where(eq(voiceCalls.status, 'in_progress'))
          .limit(20); // Limit to prevent performance issues
        
        for (const call of inProgressCalls) {
          try {
            const notes = call.conversationNotes ? JSON.parse(call.conversationNotes) : {};
            if (notes.conversationId === conversation_id) {
              callRecord = call;
              console.log(`✅ Found call ${call.id} via JSON fallback`);
              
              // 🔧 MIGRATION: Update to use dedicated column for future reliability
              await db.update(voiceCalls)
                .set({ elevenlabsConversationId: conversation_id })
                .where(eq(voiceCalls.id, call.id));
              
              break;
            }
          } catch (e) {
            // Skip invalid JSON
          }
        }
      }
    }

    const callId = callRecord?.id;

    if (callId && callRecord) {
      // TODO: Restore enhancedVoiceCallManager when implemented
      console.log(`🔄 Webhook finalizing call ${callId} directly`);

      // Safely truncate transcript for database storage
      const safeTranscript = processedTranscript.length > 10000
        ? processedTranscript.substring(0, 10000) + '... [truncated]'
        : processedTranscript;

      // Store detailed conversation notes for analysis
      const conversationNotes = {
        conversationId: conversation_id,
        agentId: agent_id,
        transcript: safeTranscript.substring(0, 5000), // Limit size for DB
        endReason: endReason,
        platform: 'elevenlabs_conversational_ai',
        candidateExperience: extractCandidateExperience(processedTranscript),
        availabilityDiscussed: extractAvailability(processedTranscript),
        followUpRequired: extractFollowUpNeeded(processedTranscript),
        metadata: metadata ? {
          cost: metadata.cost,
          main_language: metadata.main_language,
          termination_reason: metadata.termination_reason
        } : null
      };

      try {
        // Update conversation notes and finalize call directly
        await db.update(voiceCalls)
          .set({
            status: 'completed',
            transcription: safeTranscript,
            durationSeconds: Math.round(durationSeconds),
            endedAt: new Date(),
            conversationNotes: JSON.stringify(conversationNotes),
            candidateExperience: extractCandidateExperience(processedTranscript),
            availabilityDiscussed: extractAvailability(processedTranscript),
            followUpRequired: extractFollowUpNeeded(processedTranscript)
          })
          .where(eq(voiceCalls.id, callId));

        // TODO: Restore when enhancedVoiceCallManager is implemented
        // await enhancedVoiceCallManager.finalizeCall(callId, {
        //   status: 'completed',
        //   transcription: safeTranscript,
        //   durationSeconds: Math.round(durationSeconds),
        //   endReason: endReason
        // });

        console.log(`✅ Call ${callId} finalized via webhook - Duration: ${Math.round(durationSeconds)}s`);
      } catch (finalizationError) {
        console.error(`❌ Failed to finalize call ${callId}:`, finalizationError);
        
        // Fallback: Direct database update and manual summarization trigger
        try {
          await db.update(voiceCalls)
            .set({
              status: 'completed',
              endedAt: new Date(),
              durationSeconds: Math.round(durationSeconds),
              transcription: safeTranscript
            })
            .where(eq(voiceCalls.id, callId));

          // Manual summarization trigger as fallback
          if (callRecord.organizationId && safeTranscript) {
            processCallSummaryAsync(callId, callRecord.organizationId, safeTranscript);
          }
        } catch (fallbackError) {
          console.error(`❌ Fallback finalization failed for call ${callId}:`, fallbackError);
        }
      }
    } else {
      console.log(`⚠️ Call not found for conversation ID: ${conversation_id}`);
      
      // 🔍 DEBUGGING: List recent in-progress calls to help diagnose webhook issues
      if (process.env.NODE_ENV === 'development') {
        try {
          const recentCalls = await db.select({
            id: voiceCalls.id,
            status: voiceCalls.status,
            conversationNotes: voiceCalls.conversationNotes,
            createdAt: voiceCalls.createdAt
          })
          .from(voiceCalls)
          .where(eq(voiceCalls.status, 'in_progress'))
          .limit(5);
          
          console.log('🔍 Recent in-progress calls for debugging:', recentCalls.map(c => ({
            id: c.id,
            status: c.status,
            hasConversationNotes: !!c.conversationNotes,
            age: Date.now() - new Date(c.createdAt).getTime()
          })));
        } catch (debugError) {
          console.error('Debug query failed:', debugError);
        }
      }
    }

    res.json({ success: true });

  } catch (error: any) {
    console.error('❌ Conversation ended webhook error:', error);
    res.status(500).json({ 
      success: false, 
      error: error.message 
    });
  }
});

// Helper function to extract candidate experience from transcript
function extractCandidateExperience(transcript: string): string | null {
  if (!transcript) return null;
  
  // Look for experience indicators
  const experienceKeywords = ['years', 'experience', 'worked', 'background', 'previous'];
  const sentences = transcript.split(/[.!?]+/);
  
  for (const sentence of sentences) {
    if (experienceKeywords.some(keyword => sentence.toLowerCase().includes(keyword))) {
      return sentence.trim();
    }
  }
  
  return null;
}

// Helper function to extract availability from transcript
function extractAvailability(transcript: string): string | null {
  if (!transcript) return null;
  
  const availabilityKeywords = ['available', 'schedule', 'time', 'when', 'calendar'];
  const sentences = transcript.split(/[.!?]+/);
  
  for (const sentence of sentences) {
    if (availabilityKeywords.some(keyword => sentence.toLowerCase().includes(keyword))) {
      return sentence.trim();
    }
  }
  
  return null;
}

// Helper function to determine if follow-up is needed
function extractFollowUpNeeded(transcript: string): boolean {
  if (!transcript) return true;
  
  const positiveIndicators = ['interested', 'yes', 'sounds good', 'schedule', 'interview'];
  const negativeIndicators = ['not interested', 'no thank', 'busy', 'not looking'];
  
  const transcriptLower = transcript.toLowerCase();
  
  const hasPositive = positiveIndicators.some(indicator => transcriptLower.includes(indicator));
  const hasNegative = negativeIndicators.some(indicator => transcriptLower.includes(indicator));
  
  return hasPositive && !hasNegative;
}

// Health check endpoint for ElevenLabs webhook validation
router.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    service: 'elevenlabs-webhooks',
    timestamp: new Date().toISOString()
  });
});

export default router;