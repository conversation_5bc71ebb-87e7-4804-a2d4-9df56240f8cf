# Test Environment Configuration
NODE_ENV=test
PORT=5001

# Test Database
DATABASE_URL=postgresql://test:test@localhost:5432/hrms_test
TEST_DATABASE_URL=postgresql://test:test@localhost:5432/hrms_test

# JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-testing-only

# External Service API Keys (Test/Mock)
ELEVENLABS_API_KEY=test-elevenlabs-api-key
ELEVENLABS_AGENT_ID=test-agent-id
ZOOM_SDK_KEY=test-zoom-sdk-key
ZOOM_SDK_SECRET=test-zoom-sdk-secret
OPENAI_API_KEY=test-openai-api-key

# Gmail/Google OAuth (Test)
GOOGLE_CLIENT_ID=test-google-client-id
GOOGLE_CLIENT_SECRET=test-google-client-secret
GMAIL_FROM_EMAIL=<EMAIL>

# Application URLs
BASE_URL=http://localhost:5001
FRONTEND_URL=http://localhost:3001

# Bot Configuration
BOT_AUTH_TOKEN=test-bot-auth-token

# Disable external services in tests
DISABLE_EXTERNAL_SERVICES=true
DISABLE_EMAIL_SENDING=true
DISABLE_ZOOM_WEBHOOKS=true

# Test-specific settings
TEST_TIMEOUT=30000
MOCK_EXTERNAL_APIS=true
