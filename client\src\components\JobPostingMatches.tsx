import React, { useState, useEffect, useCallback, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  MapPin, 
  Edit, 
  Share, 
  Filter,
  ChevronDown,
  MoreHorizontal,
  List,
  ArrowLeft,
  CheckCircle,
  Calendar,
  Eye,
  X
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import CandidateDetailsModal from './CandidateDetailsModal';

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  department?: string;
  location?: string;
  salaryRange?: string;
  employmentType: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  educationRequirement?: string;
  keywords?: string[];
  sourcePlatform?: string;
  sourceUrl?: string;
  isActive: boolean;
  candidateCount?: number;
  createdAt: string;
  updatedAt: string;
  lastSynced?: string;
  aiGeneratedSummary?: string;
}

interface Candidate {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  location?: string;
  skills: string[];
  experience?: string;
  experienceYears?: number;
  education?: string;
  status: 'pending_review' | 'approved_for_interview' | 'rejected' | 'dismissed' | 'hired';
  resumeUrl?: string;
  overallScore?: number;
  matchScore?: number;
  availability?: string;
  notes?: string;
  appliedDate?: string;
  lastContacted?: string;
  interviewDate?: string;
  summary?: string;
  createdAt: string;
  updatedAt: string;
}

const getAvatarColor = (name: string) => {
  const colors = [
    'bg-orange-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 
    'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
  ];
  const index = name.length % colors.length;
  return colors[index];
};

const getInitials = (name: string) => {
  return name.split(' ').map(n => n[0]).join('').toUpperCase();
};

const getMatchColor = (score: number) => {
  if (score >= 80) return 'text-green-500';
  if (score >= 70) return 'text-yellow-500';
  return 'text-red-500';
};

export default function JobPostingMatches({ jobId, onBack }: { jobId: string; onBack?: () => void }) {
  const [jobPosting, setJobPosting] = useState<JobPosting | null>(null);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [activeTab, setActiveTab] = useState('all-matches');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Fetch job posting details
  const fetchJobPosting = useCallback(async () => {
    console.log('🔄 [JobMatches] Fetching job details for:', jobId);
    try {
      const response = await fetch(`/api/job-postings/${jobId}`);
      if (response.ok) {
        const data = await response.json();
        setJobPosting(data);
        console.log('✅ [JobMatches] Job details loaded:', data.title);
      }
    } catch (error) {
      console.error('Error fetching job posting:', error);
    }
  }, [jobId]);

  // Fetch candidates for job
  const fetchCandidates = useCallback(async () => {
    console.log('🔄 [JobMatches] Fetching candidates for job:', jobId);
    try {
      const response = await fetch(`/api/candidates/match-job/${jobId}`, {
        method: 'POST'
      });
      if (response.ok) {
        const data = await response.json();
        console.log('✅ [JobMatches] Candidates loaded:', data.length, 'for job:', jobId);
        setCandidates(data);
      } else {
        console.error('Failed to fetch candidates:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
    }
  }, [jobId]);

  // Handle candidate actions
  const handleCandidateAction = async (candidateId: string, action: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/candidates/${candidateId}/${action}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (response.ok) {
        toast({
          title: "Success",
          description: `Candidate ${action}d successfully`,
        });
        
        // Refresh candidates
        await fetchCandidates();
      } else {
        toast({
          title: "Error",
          description: `Failed to ${action} candidate`,
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error(`Error ${action}ing candidate:`, error);
      toast({
        title: "Error",
        description: `Failed to ${action} candidate`,
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle scheduling
  const handleSchedule = (candidateId: string) => {
    // Navigate to scheduling page
    window.location.href = '/scheduling';
  };

  // 🎯 ONE-TIME INIT GUARD - Prevents React StrictMode duplicate calls per jobId
  const didInitJob = useRef<string | null>(null);
  useEffect(() => {
    if (didInitJob.current === jobId) {
      console.log('🚫 [JobMatches] Init already done for job:', jobId, 'skipping duplicate call');
      return;
    }
    didInitJob.current = jobId;
    console.log('🔄 [JobMatches] First time init for job:', jobId);
    fetchJobPosting();
    fetchCandidates();
  }, [jobId, fetchJobPosting, fetchCandidates]);

  // 🎯 REMOVED INFINITE LOOP - Event-driven updates are more efficient
  // The candidateAdded event listener below will handle refreshes when needed

  // Listen for candidate addition events
  useEffect(() => {
    const handleCandidateAdded = (event: CustomEvent) => {
      const { jobId: addedJobId } = event.detail;
      // Refresh candidates if added to current job or if no specific job
      if (addedJobId === jobId || !addedJobId) {
        console.log('🔄 [JobMatches] Candidate added event - refreshing candidates');
        fetchCandidates();
      }
    };

    window.addEventListener('candidateAdded', handleCandidateAdded as EventListener);
    return () => window.removeEventListener('candidateAdded', handleCandidateAdded as EventListener);
  }, [jobId, fetchCandidates]);

  const filteredCandidates = candidates.filter(candidate => {
    switch (activeTab) {
      case 'approved':
        return candidate.status === 'approved_for_interview';
      case 'scheduled':
        return candidate.status === 'interview_scheduled';
      case 'hired':
        return candidate.status === 'hired';
      case 'rejected':
        return candidate.status === 'dismissed' || candidate.status === 'rejected';
      default:
        return true;
    }
  });

  if (!jobPosting) {
    return <div className="flex items-center justify-center h-64">Loading...</div>;
  }

  return (
    <div
      className="min-h-screen overflow-y-auto"
      style={{ backgroundColor: '#FBFAFF' }}
    >
      <div className="px-10 py-8">
        {/* Back Button */}
        {onBack && (
          <Button
            onClick={onBack}
            variant="ghost"
            className="flex items-center gap-2 mb-4 hover:bg-blue-50"
            style={{ color: '#0152FF' }}
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Job Postings
          </Button>
        )}

        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div>
            <div className="flex items-center gap-2 mb-1">
              <h2 className="text-2xl font-semibold" style={{ color: '#0152FF' }}>{jobPosting.title}</h2>
              <Badge className="flex items-center px-3 py-1 rounded-full border border-[#E6E8EC] text-xs font-medium text-[#7F56D9] bg-[#F4F3FF] ml-2">
                <span className="w-2 h-2 rounded-full bg-[#7F56D9] mr-2"></span>
                {jobPosting.isActive ? 'Open' : 'Closed'}
              </Badge>
            </div>
            <div className="flex items-center text-[#777E90] text-sm font-medium">
              <MapPin className="w-4 h-4 mr-2" />
              {jobPosting.location || 'Remote'}
            </div>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              className="flex items-center px-5 py-2 hover:bg-blue-50"
              style={{ borderColor: '#0152FF', color: '#0152FF' }}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Post
            </Button>
            <Button
              variant="outline"
              className="flex items-center px-5 py-2 hover:bg-blue-50"
              style={{ borderColor: '#0152FF', color: '#0152FF' }}
            >
              <Share className="w-4 h-4 mr-2" />
              Share
            </Button>
          </div>
        </div>

        {/* Job Details Card */}
        <Card className="grid grid-cols-3 gap-6 bg-white rounded-2xl border border-[#F1F1F3] p-6 mb-6">
          <div>
            <div className="text-xs font-semibold text-[#777E90] mb-1">Category</div>
            <div className="text-base font-medium text-[#23262F] mb-4">{jobPosting.department || 'Product'}</div>
            <div className="text-xs font-semibold text-[#777E90] mb-1">Experience</div>
            <div className="text-base font-medium text-[#23262F]">{jobPosting.experienceLevel || '3+ years'}</div>
          </div>
          <div>
            <div className="text-xs font-semibold text-[#777E90] mb-1">Availability</div>
            <div className="text-base font-medium text-[#23262F] mb-4">{jobPosting.employmentType}</div>
            <div className="text-xs font-semibold text-[#777E90] mb-1">Salary</div>
            <div className="text-base font-medium text-[#23262F]">{jobPosting.salaryRange || '$80K - $100K'}</div>
          </div>
          <div>
            <div className="text-xs font-semibold text-[#777E90] mb-1">Work Approach</div>
            <div className="text-base font-medium text-[#23262F] mb-4">Onsite</div>
            <div className="text-xs font-semibold text-[#777E90] mb-1">License</div>
            <div className="text-base font-medium text-[#23262F]">Required</div>
          </div>
        </Card>

        {/* Horizontal Filters */}
        <Card className="bg-white rounded-2xl border border-[#F1F1F3] p-6 mb-6">
          <div className="flex items-center gap-8">
            {/* Match Rating */}
            <div className="flex-1">
              <div className="text-xs font-semibold text-[#777E90] mb-3">Match Rating</div>
              <div className="flex flex-wrap gap-2">
                {['90%-100%', '70%-80%', '50%-70%', '<50%'].map((range) => (
                  <label key={range} className="flex items-center px-3 py-1.5 rounded-full border border-[#E6E8EC] cursor-pointer hover:bg-[#F4F3FF] transition-colors">
                    <Checkbox className="mr-2" />
                    <span className="text-sm font-medium text-[#23262F]">{range}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* License */}
            <div className="flex-1">
              <div className="text-xs font-semibold text-[#777E90] mb-3">License</div>
              <div className="flex flex-wrap gap-2">
                {['Yes', 'No'].map((option) => (
                  <label key={option} className="flex items-center px-3 py-1.5 rounded-full border border-[#E6E8EC] cursor-pointer hover:bg-[#F4F3FF] transition-colors">
                    <Checkbox className="mr-2" />
                    <span className="text-sm font-medium text-[#23262F]">{option}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Experience */}
            <div className="flex-1">
              <div className="text-xs font-semibold text-[#777E90] mb-3">Experience</div>
              <div className="flex flex-wrap gap-2">
                {['5-7 years', '3-5 years', '1-3 years'].map((range) => (
                  <label key={range} className="flex items-center px-3 py-1.5 rounded-full border border-[#E6E8EC] cursor-pointer hover:bg-[#F4F3FF] transition-colors">
                    <Checkbox className="mr-2" />
                    <span className="text-sm font-medium text-[#23262F]">{range}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Location */}
            <div className="flex-1">
              <div className="text-xs font-semibold text-[#777E90] mb-3">Location</div>
              <div className="flex flex-wrap gap-2">
                {['Toronto, Canada', 'Newyork, USA', 'Lead, UK'].map((location) => (
                  <label key={location} className="flex items-center px-3 py-1.5 rounded-full border border-[#E6E8EC] cursor-pointer hover:bg-[#F4F3FF] transition-colors">
                    <Checkbox className="mr-2" />
                    <span className="text-sm font-medium text-[#23262F]">{location}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </Card>

        {/* Header with candidate count */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-[#23262F]">
            Matched Candidates
          </h3>
          <Badge variant="secondary" className="text-[#777E90] bg-[#F1F1F3]">
            All Matches ({candidates.length})
          </Badge>
        </div>

        {/* Tabs */}
        <div className="flex items-center border-b border-[#E6E8EC] mb-6">
          {[
            { id: 'all-matches', label: 'All Matches', count: candidates.length },
            { id: 'approved', label: 'Approved', count: candidates.filter(c => c.status === 'approved_for_interview').length },
            { id: 'scheduled', label: 'Scheduled', count: candidates.filter(c => c.status === 'interview_scheduled').length },
            { id: 'hired', label: 'Hired', count: candidates.filter(c => c.status === 'hired').length },
            { id: 'rejected', label: 'Rejected', count: candidates.filter(c => c.status === 'dismissed' || c.status === 'rejected').length }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-4 py-2 font-medium -mb-px ${
                activeTab === tab.id
                  ? 'text-[#7F56D9] font-semibold border-b-2 border-[#7F56D9]'
                  : 'text-[#777E90]'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
          <div className="flex-1"></div>
          <Button variant="ghost" className="flex items-center text-[#777E90] text-sm font-medium px-2">
            <List className="w-4 h-4 mr-2" />
            List View
          </Button>
        </div>

        {/* Candidates Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white rounded-2xl border border-[#F1F1F3]">
            <thead>
              <tr className="text-[#777E90] text-xs font-semibold">
                <th className="text-left px-4 py-3">Candidate Name</th>
                <th className="text-left px-4 py-3">Match</th>
                <th className="text-left px-4 py-3">Date</th>
                <th className="text-left px-4 py-3">Salary</th>
                <th className="text-left px-4 py-3">Experience</th>
                <th className="text-left px-4 py-3">Actions</th>
              </tr>
            </thead>
            <tbody className="text-[#23262F] text-xs font-medium">
              {filteredCandidates.map((candidate) => (
                <tr key={candidate.id} className="border-t border-[#F1F1F3]">
                  <td className="flex items-center gap-2 px-4 py-3">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={candidate.resumeUrl} />
                      <AvatarFallback className={`${getAvatarColor(candidate.fullName)} text-white text-xs`}>
                        {getInitials(candidate.fullName)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">{candidate.fullName}</span>
                  </td>
                  <td className="px-4 py-3">
                    <div className="relative w-8 h-8 flex items-center justify-center">
                      <svg className="absolute top-0 left-0" height="32" width="32">
                        <circle cx="16" cy="16" fill="none" r="14" stroke="#E6E8EC" strokeWidth="3" />
                        <circle 
                          cx="16" 
                          cy="16" 
                          fill="none" 
                          r="14" 
                          stroke={candidate.matchScore && candidate.matchScore >= 80 ? "#58C27D" : "#F6C768"}
                          strokeDasharray="88" 
                          strokeDashoffset="0" 
                          strokeLinecap="round" 
                          strokeWidth="3" 
                        />
                      </svg>
                      <span className={`relative font-semibold text-xs ${getMatchColor(candidate.matchScore || 0)}`}>
                        {candidate.matchScore || 75}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-xs">
                    {new Date(candidate.createdAt).toLocaleDateString('en-GB', {
                      day: 'numeric',
                      month: 'short'
                    })}
                  </td>
                  <td className="px-4 py-3 text-xs">$80K - $100K</td>
                  <td className="px-4 py-3 text-xs">{candidate.experienceYears || 3} years</td>
                  <td className="px-4 py-3">
                    <div className="flex items-center gap-1">
                      {/* Show appropriate action buttons based on candidate status */}
                      {candidate.status === 'pending_review' && (
                        <>
                          <Button
                            onClick={() => handleCandidateAction(candidate.id, 'approve')}
                            variant="ghost"
                            size="sm"
                            className="text-green-600 hover:bg-green-50 text-xs px-1 py-1 h-7"
                            disabled={isLoading}
                            title="Approve for Interview"
                          >
                            <CheckCircle className="w-3 h-3" />
                          </Button>
                          <Button
                            onClick={() => handleCandidateAction(candidate.id, 'reject')}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:bg-red-50 text-xs px-1 py-1 h-7"
                            disabled={isLoading}
                            title="Reject Candidate"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </>
                      )}
                      
                      {candidate.status === 'approved_for_interview' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center gap-1 text-green-600 border-green-200 bg-green-50 text-xs px-2 py-1 h-7"
                            disabled
                          >
                            <CheckCircle className="w-3 h-3" />
                            Approved
                          </Button>
                          <Button
                            onClick={() => handleCandidateAction(candidate.id, 'dismiss')}
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:bg-red-50 text-xs px-1 py-1 h-7"
                            disabled={isLoading}
                            title="Dismiss Candidate"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </>
                      )}
                      
                      {(candidate.status === 'dismissed' || candidate.status === 'rejected') && (
                        <>
                          <Badge variant="destructive" className="text-xs h-6">
                            Rejected
                          </Badge>
                          <Button
                            onClick={() => handleCandidateAction(candidate.id, 'reinstate')}
                            variant="ghost"
                            size="sm"
                            className="text-blue-600 hover:bg-blue-50 text-xs px-1 py-1 h-7"
                            disabled={isLoading}
                            title="Reinstate Candidate"
                          >
                            <CheckCircle className="w-3 h-3" />
                          </Button>
                        </>
                      )}
                      
                      {candidate.status === 'hired' && (
                        <Badge variant="default" className="text-xs bg-blue-100 text-blue-800 h-6">
                          Hired
                        </Badge>
                      )}
                      
                      {/* View Details button - always visible */}
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="text-[#777E90] text-xs px-1 py-1 h-7"
                        onClick={() => setSelectedCandidate(candidate)}
                        title="View Details"
                      >
                        <Eye className="w-3 h-3" />
                      </Button>
                      
                      {/* Schedule button - only for approved candidates */}
                      {candidate.status === 'approved_for_interview' && (
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-[#777E90] text-xs px-1 py-1 h-7"
                          onClick={() => handleSchedule(candidate.id)}
                          title="Schedule Interview"
                        >
                          <Calendar className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredCandidates.length === 0 && (
          <div className="text-center py-8 text-[#777E90]">
            No candidates found for this filter
          </div>
        )}
      </div>

      {/* Candidate Details Modal */}
      {selectedCandidate && (
        <CandidateDetailsModal
          candidate={selectedCandidate}
          isOpen={!!selectedCandidate}
          onClose={() => setSelectedCandidate(null)}
        />
      )}
    </div>
  );
}