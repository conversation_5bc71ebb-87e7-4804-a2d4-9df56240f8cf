
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Clock, User } from "lucide-react";

interface ManualResumeInputProps {
  onSubmit: (data: { 
    text: string; 
    email?: string; 
    name?: string; 
    phone?: string; 
    location?: string; 
    linkedin?: string; 
  }) => void;
  isAnalyzing: boolean;
  initialData?: {
    name?: string;
    email?: string;
    phone?: string;
    location?: string;
    linkedin?: string;
  };
}

const ManualResumeInput: React.FC<ManualResumeInputProps> = ({ 
  onSubmit, 
  isAnalyzing, 
  initialData 
}) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    location: '',
    linkedin: '',
    text: ''
  });

  // Pre-fill form with initial data when component mounts or initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        name: initialData.name || '',
        email: initialData.email || '',
        phone: initialData.phone || '',
        location: initialData.location || '',
        linkedin: initialData.linkedin || ''
      }));
    }
  }, [initialData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.text.trim()) {
      onSubmit({
        text: formData.text,
        email: formData.email || undefined,
        name: formData.name || undefined,
        phone: formData.phone || undefined,
        location: formData.location || undefined,
        linkedin: formData.linkedin || undefined
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Card className="border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-800">
          <User className="w-5 h-5" />
          Manual Resume Input
        </CardTitle>
        <p className="text-sm text-orange-700">
          {initialData ? 
            "Contact information extracted automatically. Please complete the resume content below." :
            "Automated PDF parsing failed. Please fill in the contact information and paste the resume content manually."
          }
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Candidate Name *
              </label>
              <Input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Full Name"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Email *
              </label>
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone
              </label>
              <Input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="Phone Number"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Location
              </label>
              <Input
                type="text"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="City, State"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              LinkedIn Profile
            </label>
            <Input
              type="url"
              value={formData.linkedin}
              onChange={(e) => handleInputChange('linkedin', e.target.value)}
              placeholder="https://linkedin.com/in/profile"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Resume Content *
            </label>
            <Textarea
              value={formData.text}
              onChange={(e) => handleInputChange('text', e.target.value)}
              placeholder="Paste the complete resume content here including work experience, education, skills, etc..."
              rows={10}
              required
            />
          </div>

          <Button 
            type="submit"
            disabled={isAnalyzing || !formData.text.trim() || !formData.name.trim() || !formData.email.trim()}
            className="w-full"
          >
            {isAnalyzing ? (
              <>
                <Clock className="w-4 h-4 mr-2 animate-spin" />
                Analyzing Resume...
              </>
            ) : (
              'Analyze Resume'
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default ManualResumeInput;
