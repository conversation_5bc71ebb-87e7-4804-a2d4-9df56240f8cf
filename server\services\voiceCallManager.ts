import { db } from '../db';
import { voiceCalls, voiceCallNotes, candidates, jobPostings, applications } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import OpenAI from 'openai';
import { elevenLabsService } from './elevenlabsService';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export class VoiceCallManager {
  
  /**
   * Process transcription from Google Speech or other sources
   */
  async processTranscription(callId: string, transcript: string, source: string): Promise<void> {
    try {
      console.log(`Processing transcription for call ${callId} from ${source}: ${transcript}`);

      // Get call details from database using call ID instead of SID
      const call = await db.select()
        .from(voiceCalls)
        .where(eq(voiceCalls.id, callId))
        .limit(1);

      if (call.length === 0) {
        console.error(`Call not found for ID: ${callId}`);
        return;
      }

      const callRecord = call[0];

      // Get candidate and job information for context
      const candidate = await db.select()
        .from(candidates)
        .where(eq(candidates.id, callRecord.candidateId))
        .limit(1);

      // For voice calls, we'll get job from candidate's applications
      const jobApplications = await db.select()
        .from(applications)
        .innerJoin(jobPostings, eq(applications.jobPostingId, jobPostings.id))
        .where(eq(applications.candidateId, callRecord.candidateId))
        .limit(1);

      if (candidate.length === 0) {
        console.error(`Candidate not found for call ${callId}`);
        return;
      }

      // Use the most recent job application or a default context
      const job = jobApplications.length > 0 ? jobApplications[0].job_postings : null;

      // Save transcription note
      await this.addCallNote(callId, `[${source.toUpperCase()}] ${transcript}`, 'transcription');

      // Process with AI if transcript is meaningful
      if (transcript.length > 5) {
        await this.generateAIResponse(callId, transcript, candidate[0], job?.jobPostings || job);
      }

    } catch (error) {
      console.error(`Error processing transcription for call ${callId}:`, error);
    }
  }

  /**
   * Generate AI response based on transcription
   */
  private async generateAIResponse(callId: string, transcript: string, candidate: any, job: any): Promise<void> {
    try {
      // Get conversation history
      const previousNotes = await db.select()
        .from(voiceCallNotes)
        .where(eq(voiceCallNotes.callId, callId))
        .orderBy(voiceCallNotes.createdAt);

      const conversationHistory = previousNotes
        .filter(note => note.noteType === 'transcription' || note.noteType === 'ai_response')
        .map(note => note.content)
        .join('\n');

      // Create AI prompt with context
      const prompt = this.buildConversationPrompt(transcript, conversationHistory, candidate, job);

      // Get AI response
      const response = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: prompt
          },
          {
            role: 'user',
            content: transcript
          }
        ],
        temperature: 0.9,
        max_tokens: 150, // Allow longer responses for detailed job discussions
        frequency_penalty: 0.7,
        presence_penalty: 0.5
      });

      const aiResponse = response.choices[0].message.content?.trim() || '';

      if (aiResponse) {
        // Save AI response
        await this.addCallNote(callId, aiResponse, 'ai_response');

        // Generate audio response using ElevenLabs or fallback
        await this.generateAudioResponse(callId, aiResponse);
      }

    } catch (error) {
      console.error(`Error generating AI response for call ${callId}:`, error);
    }
  }

  /**
   * Generate audio response using ElevenLabs or Twilio
   */
  private async generateAudioResponse(callId: string, text: string): Promise<void> {
    try {
      // Try ElevenLabs first
      const audioUrl = await elevenLabsService.generateSpeech(text, 'sarah');
      
      if (audioUrl) {
        console.log(`Generated ElevenLabs audio for call ${callId}: ${audioUrl}`);
        await this.addCallNote(callId, `Generated ElevenLabs audio: ${audioUrl}`, 'audio_generated');
      } else {
        console.log(`Falling back to Twilio TTS for call ${callId}`);
        await this.addCallNote(callId, `Using Twilio TTS: ${text}`, 'twilio_tts');
      }

    } catch (error) {
      console.error(`Error generating audio response for call ${callId}:`, error);
    }
  }

  /**
   * Build conversation prompt for AI
   */
  private buildConversationPrompt(currentInput: string, history: string, candidate: any, job: any): string {
    const jobTitle = job?.title || 'Software Engineer';
    const jobDepartment = job?.department || 'Engineering';
    const jobLocation = job?.location || 'Remote/Hybrid';
    const jobSalaryRange = job?.salaryRange || '$80,000 - $120,000';
    const jobRequirements = job?.requirements || 'Strong programming skills, experience with modern frameworks, collaborative mindset';
    const jobDescription = job?.detailedDescription || job?.description || 'We are looking for a talented software engineer to join our growing team. You will work on exciting projects, collaborate with cross-functional teams, and have opportunities for professional growth.';
    const jobResponsibilities = job?.responsibilities || 'Develop and maintain high-quality software applications, collaborate with cross-functional teams, participate in code reviews, and contribute to architectural decisions.';
    const jobBenefits = job?.benefits || 'Health insurance, 401k matching, flexible PTO, professional development budget, remote work options';
    const workEnvironment = job?.workEnvironment || 'Collaborative team environment with remote/hybrid options';
    const growthOpportunities = job?.growthOpportunities || 'Career advancement opportunities, mentorship programs, conference attendance';
    
    return `You are Sarah, a warm, funny, and personable HR recruiter at a tech company. You're conducting an engaging phone screening with ${candidate.fullName} for the ${jobTitle} position.

IMPORTANT: You MUST discuss job details when asked. Never end the call when someone asks about the role overview, responsibilities, or job description. This is exactly what candidates want to hear about!

WHEN ASKED ABOUT THE ROLE, SHARE THESE DETAILS ENTHUSIASTICALLY:

CONVERSATION STYLE:
- Be genuinely curious and enthusiastic: "Oh that's fascinating!", "I love that!", "Tell me more!"
- When asked about the role, provide comprehensive details enthusiastically
- Share specific job responsibilities, requirements, and benefits when asked
- Use natural reactions: "That's exactly what we're looking for!", "Perfect fit!"
- Keep responses engaging and informative - 30-60 seconds for role discussions

ENHANCED CONVERSATION STAGES:
1. Greeting: Warm welcome, confirm they can hear you
2. Engaging chat: Ask about their background, what excites them about tech
3. Role connection: Connect their experience to our position
4. Role details: Share position summary, salary range, and key benefits
5. Scheduling focus: "When would work best for your technical interview?"
6. Q&A time: "Do you have any questions about the role or company?"
7. Wrap up: Confirm details and express genuine excitement

JOB OVERVIEW TO DISCUSS:
Position: ${jobTitle} in the ${jobDepartment} department
Location: ${jobLocation}
Salary Range: ${jobSalaryRange}

Role Description: ${jobDescription}

Key Responsibilities: ${jobResponsibilities}

Required Qualifications: ${jobRequirements}

Work Environment: ${workEnvironment}

Growth Opportunities: ${growthOpportunities}

Benefits & Perks: ${jobBenefits}

Company Culture: We're a fast-growing tech company with a collaborative environment, innovative projects, and excellent career growth opportunities.

CANDIDATE CONTEXT:
Name: ${candidate.fullName}
Experience: ${candidate.experienceYears} years
Skills: ${candidate.skills}
Email: ${candidate.email}

CRITICAL CONVERSATION RULES:
- NEVER end the call when asked about job details, role overview, or responsibilities
- ALWAYS provide detailed, enthusiastic responses about the position
- Keep responses conversational but informative (30-60 seconds each)
- Ask follow-up questions to maintain engagement
- If you don't understand something, ask for clarification
- Show genuine excitement about the role and company

CONVERSATION FLOW PRIORITIES:
1. Role overview and responsibilities (THIS IS KEY - discuss in detail!)
2. Candidate's experience and background
3. Technical skills alignment
4. Career goals and growth opportunities
5. Availability and next steps

Previous conversation: ${history}
Candidate just said: "${currentInput}"

Respond as Sarah with genuine enthusiasm about the ${jobTitle} role. If they asked about the position, dive into the details above. Keep the conversation engaging and informative:`;
  }

  /**
   * Add a note to the call using call ID
   */
  async addCallNote(callId: string, content: string, noteType: string): Promise<void> {
    try {
      // Get organization ID from call record
      const call = await db.select()
        .from(voiceCalls)
        .where(eq(voiceCalls.id, callId))
        .limit(1);

      if (call.length === 0) {
        console.error(`Call not found for ID: ${callId}`);
        return;
      }

      await db.insert(voiceCallNotes).values({
        callId: call[0].id,
        organizationId: call[0].organizationId,
        content,
        noteType,
        createdAt: new Date()
      });

      console.log(`Added ${noteType} note to call ${callId}: ${content.substring(0, 100)}...`);

    } catch (error) {
      console.error(`Error adding call note for ${callId}:`, error);
    }
  }

  /**
   * Add a note to the call using Twilio SID (backward compatibility)
   */
  async addCallNoteBySid(callSid: string, content: string, noteType: string): Promise<void> {
    try {
      // Get call ID from Twilio SID
      const call = await db.select()
        .from(voiceCalls)
        .where(eq(voiceCalls.twilioCallSid, callSid))
        .limit(1);

      if (call.length === 0) {
        console.error(`Call not found for SID: ${callSid}`);
        return;
      }

      await this.addCallNote(call[0].id, content, noteType);

    } catch (error) {
      console.error(`Error adding call note for SID ${callSid}:`, error);
    }
  }

  /**
   * Update call status
   */
  async updateCallStatus(callSid: string, status: string): Promise<void> {
    try {
      await db.update(voiceCalls)
        .set({ 
          status: status as any,
          updatedAt: new Date()
        })
        .where(eq(voiceCalls.twilioCallSid, callSid));

      console.log(`Updated call ${callSid} status to: ${status}`);

    } catch (error) {
      console.error(`Error updating call status for ${callSid}:`, error);
    }
  }
}