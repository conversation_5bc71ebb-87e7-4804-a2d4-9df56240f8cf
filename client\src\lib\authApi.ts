// Authentication API utilities with automatic token refresh
import { apiRequest } from './queryClient';

interface LoginRequest {
  email: string;
  password: string;
}

interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: string;
  user: {
    id: string;
    email: string;
    full_name: string;
    role: string;
    organization_id: string;
    organization_name: string;
    is_active: boolean;
    is_approved: boolean;
  };
}

interface RefreshResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: string;
  user: {
    id: string;
    email: string;
    full_name: string;
    role: string;
    organization_id: string;
    organization_name: string;
    is_active: boolean;
    is_approved: boolean;
  };
}

class AuthAPI {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private refreshPromise: Promise<RefreshResponse> | null = null;

  constructor() {
    // Load tokens from localStorage on initialization
    this.loadTokensFromStorage();
  }

  private loadTokensFromStorage() {
    this.accessToken = localStorage.getItem('access_token');
    this.refreshToken = localStorage.getItem('refresh_token');
  }

  private saveTokensToStorage(accessToken: string, refreshToken: string) {
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
  }

  public clearTokensFromStorage() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    this.accessToken = null;
    this.refreshToken = null;
  }

  // 🎯 PUBLIC LOGOUT METHOD for better API design
  public logoutAndClearSession() {
    this.clearTokensFromStorage();
    this.refreshPromise = null;
  }

  public getAccessToken(): string | null {
    return this.accessToken;
  }

  public isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  public async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Login failed');
      }

      const data: LoginResponse = await response.json();
      this.saveTokensToStorage(data.access_token, data.refresh_token);
      return data;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  public async logout(): Promise<void> {
    try {
      // Call logout endpoint if needed
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearTokensFromStorage();
      this.refreshPromise = null;
    }
  }

  public async refreshAccessToken(): Promise<RefreshResponse> {
    // Prevent multiple simultaneous refresh requests
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    this.refreshPromise = this.performTokenRefresh();
    
    try {
      const result = await this.refreshPromise;
      this.refreshPromise = null;
      return result;
    } catch (error) {
      this.refreshPromise = null;
      throw error;
    }
  }

  private async performTokenRefresh(): Promise<RefreshResponse> {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          refresh_token: this.refreshToken,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        
        // If refresh token is invalid, clear everything and throw proper error
        if (error.code === 'INVALID_REFRESH_TOKEN' || response.status === 401) {
          this.clearTokensFromStorage();
          const authError = new Error('Session expired. Please log in again.') as any;
          authError.status = 401;
          authError.name = 'AuthError';
          throw authError;
        }
        
        throw new Error(error.error || 'Token refresh failed');
      }

      const data: RefreshResponse = await response.json();
      this.saveTokensToStorage(data.access_token, data.refresh_token);
      return data;
    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }

  public async validateSession(): Promise<boolean> {
    // 🎯 ENHANCED TOKEN VALIDATION - check localStorage tokens first
    if (!this.accessToken && !this.refreshToken) {
      console.log('No tokens found in localStorage');
      this.clearTokensFromStorage();
      return false;
    }

    if (!this.accessToken) {
      console.log('No access token, but refresh token exists - attempting refresh');
      if (this.refreshToken) {
        try {
          await this.refreshAccessToken();
          return true;
        } catch {
          console.log('Refresh failed during validation');
          this.clearTokensFromStorage();
          return false;
        }
      }
      return false;
    }

    try {
      const response = await fetch('/api/auth/validate', {
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
        },
        credentials: 'include',
      });

      if (response.ok) {
        return true;
      }

      // 🎯 HANDLE 401 PROPERLY - try to refresh token if validation fails
      if (response.status === 401 && this.refreshToken) {
        console.log('Access token invalid, attempting refresh...');
        try {
          await this.refreshAccessToken();
          return true;
        } catch (refreshError) {
          console.log('Refresh failed during validation, clearing tokens');
          this.clearTokensFromStorage();
          return false;
        }
      }

      // 🎯 OTHER ERROR RESPONSES - clear tokens
      console.log('Validation failed with status:', response.status);
      this.clearTokensFromStorage();
      return false;
    } catch (error) {
      console.error('Session validation network error:', error);
      this.clearTokensFromStorage();
      return false;
    }
  }

  // Automatic retry for API requests with token refresh
  public async makeAuthenticatedRequest(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const makeRequest = async (token: string | null): Promise<Response> => {
      const headers = {
        ...options.headers,
        ...(token && { 'Authorization': `Bearer ${token}` }),
      };

      console.log('🔧 makeRequest called with:', {
        url,
        method: options.method,
        hasBody: !!options.body,
        bodyType: typeof options.body,
        headers,
        bodyPreview: options.body ? String(options.body).substring(0, 200) : null
      });

      return fetch(url, {
        ...options,
        headers,
        credentials: 'include',
      });
    };

    // 🎯 ENSURE TOKENS ARE LOADED FROM STORAGE
    if (!this.accessToken && !this.refreshToken) {
      // Try to reload tokens from localStorage before failing
      this.loadTokensFromStorage();
      console.log('🔄 Reloaded tokens from storage. Access:', !!this.accessToken, 'Refresh:', !!this.refreshToken);
    }
    
    // 🎯 CHECK TOKEN EXISTENCE AFTER RELOAD ATTEMPT
    if (!this.accessToken && !this.refreshToken) {
      console.log('❌ No tokens available after storage reload');
      this.clearTokensFromStorage();
      const authError = new Error('Session expired. Please log in again.') as any;
      authError.status = 401;
      authError.name = 'AuthError';
      throw authError;
    }

    // First attempt with current token
    let response = await makeRequest(this.accessToken);

    // 🎯 HANDLE 401 WITH BETTER ERROR MESSAGES
    if (response.status === 401 && this.refreshToken) {
      try {
        console.log('Access token expired, refreshing...');
        await this.refreshAccessToken();
        response = await makeRequest(this.accessToken);
        console.log('Token refresh successful, retrying request');
      } catch (refreshError) {
        console.error('Failed to refresh token:', refreshError);
        // Clear tokens and throw proper error with status
        this.clearTokensFromStorage();
        const authError = new Error('Session expired. Please log in again.') as any;
        authError.status = 401;
        authError.name = 'AuthError';
        throw authError;
      }
    } else if (response.status === 401) {
      // No refresh token available
      console.log('Unauthorized and no refresh token available');
      this.clearTokensFromStorage();
      const authError = new Error('Session expired. Please log in again.') as any;
      authError.status = 401;
      authError.name = 'AuthError';
      throw authError;
    }

    return response;
  }
}

// Export singleton instance
export const authAPI = new AuthAPI();