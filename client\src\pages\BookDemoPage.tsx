import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import AuthLayout from '../components/AuthLayout';

export default function BookDemoPage() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    organization: '',
    phone: '',
    countryCode: '+1'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setIsSubmitted(true);
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4" style={{ backgroundColor: '#F0F4FF' }}>
        <div className="w-full max-w-md text-center bg-white rounded-3xl p-8 shadow-xl">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-[#151518] mb-2">Demo Request Submitted!</h2>
          <p className="text-[#4B4B52] mb-6">
            Thank you for your interest in Steorra. Our team will contact you within 24 hours to schedule your personalized demo.
          </p>
          <button
            onClick={() => navigate('/')}
            className="w-full py-3 px-6 bg-[#0152FF] text-white font-semibold rounded-full hover:bg-[#0142CC] transition-colors"
          >
            Return to Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <AuthLayout>
      {/* Logo */}
      <div className="mb-6">
        <img src="/Login/Vector.svg" alt="Steorra Logo" className="w-10 h-10 mb-4" />
        <h1 className="text-2xl font-bold text-[#151518]">
          Request Your Free Demo
        </h1>
      </div>

      {/* Demo Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Full Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-[#151518] mb-1.5">
            Full name <span className="text-red-500">*</span>
          </label>
          <input
            id="name"
            type="text"
            required
            className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
            placeholder="Enter the full name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
          />
        </div>

        {/* Work Email */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-[#151518] mb-1.5">
            Work email address <span className="text-red-500">*</span>
          </label>
          <input
            id="email"
            type="email"
            required
            className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
            placeholder="Enter the work email address"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
          />
        </div>

        {/* Phone Number */}
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-[#151518] mb-1.5">
            Phone number <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              className="w-16 px-3 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
              value={formData.countryCode}
              onChange={(e) => handleInputChange('countryCode', e.target.value)}
            />
            <input
              id="phone"
              type="tel"
              required
              className="flex-1 px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
              placeholder="Enter the phone number"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
            />
          </div>
        </div>

        {/* Organization Name */}
        <div>
          <label htmlFor="organization" className="block text-sm font-medium text-[#151518] mb-1.5">
            Organization Name <span className="text-red-500">*</span>
          </label>
          <input
            id="organization"
            type="text"
            required
            className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
            placeholder="Enter the organization name"
            value={formData.organization}
            onChange={(e) => handleInputChange('organization', e.target.value)}
          />
        </div>

        {/* Info Text */}
        <p className="text-xs text-[#0152FF] bg-white px-3 py-2.5 rounded-xl border border-[#E5E7EB]">
          Our AI recruitment specialists will contact you within 24 hours to schedule your free, personalized Steorra demo
        </p>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={isSubmitting}
          className="w-full py-2.5 px-6 bg-[#0152FF] text-white font-semibold rounded-full hover:bg-[#0142CC] transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>{isSubmitting ? 'Submitting...' : 'Book My Free Demo Now'}</span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </form>
    </AuthLayout>
  );
}