
import { useState } from 'react';
import { useToast } from "@/hooks/use-toast";
import { EnhancedAnalysisResult } from "@/types/resumeAnalysis";

export const useResumeAnalysis = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<EnhancedAnalysisResult | null>(null);
  const { toast } = useToast();

  const saveCandidateData = async (analysisResult: EnhancedAnalysisResult, resumeText: string) => {
    try {
      // Use server-side API to save candidate
      const response = await fetch('/api/candidates', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          fullName: analysisResult.name || 'Unknown',
          email: analysisResult.email || '',
          currentPosition: analysisResult.recent_roles?.[0]?.title || null,
          currentCompany: analysisResult.recent_roles?.[0]?.company || null,
          experienceYears: typeof analysisResult.experience_years === 'number' 
            ? analysisResult.experience_years 
            : parseInt(String(analysisResult.experience_years)) || 0,
          education: analysisResult.education || null,
          skills: analysisResult.skill_analysis?.matched_skills || [],
          resumeText: resumeText,
          phone: analysisResult.phone || null,
          location: analysisResult.location || null,
          linkedinUrl: analysisResult.linkedin || null,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to save candidate');
      }

      const data = await response.json();
      console.log('Candidate saved successfully:', data.id);
      return data.id;
    } catch (error) {
      console.error('Error saving candidate data:', error);
      return null;
    }
  };

  const analyzeResumeData = async (
    resumeText: string,
    jobDescription: string,
    extractedEmail?: string,
    extractedName?: string,
    extractedPhone?: string,
    extractedLocation?: string,
    extractedLinkedin?: string,
    workExperience?: any[],
    education?: any[],
    skills?: string[]
  ) => {
    setIsAnalyzing(true);
    
    try {
      // Use server-side API for resume analysis
      const response = await fetch('/api/resume/analyze-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          resumeText,
          jobDescription,
          extractContactOnly: false
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Response Error:", errorText);
        throw new Error(`Analysis failed: ${response.status} - ${errorText}`);
      }

      const analysis: EnhancedAnalysisResult = await response.json();
      
      // Use extracted data if available and analysis is missing it, but avoid fake data
      const enhancedAnalysis = {
        ...analysis,
        email: analysis.email && !analysis.email.includes('example.com') ? analysis.email : (extractedEmail || ''),
        name: analysis.name && analysis.name !== 'Not Available' ? analysis.name : (extractedName || 'Unknown Candidate'),
        phone: analysis.phone && analysis.phone !== '************' ? analysis.phone : extractedPhone,
        location: analysis.location && analysis.location !== 'Not Available' ? analysis.location : extractedLocation,
        linkedin: analysis.linkedin && !analysis.linkedin.includes('example') ? analysis.linkedin : extractedLinkedin
      };
      
      console.log("Enhanced analysis result:", enhancedAnalysis);
      
      // Save candidate data via server API
      const candidateId = await saveCandidateData(enhancedAnalysis, resumeText);
      if (candidateId) {
        enhancedAnalysis.candidate_id = candidateId;
      }
      
      setAnalysisResult(enhancedAnalysis);
      
      toast({
        title: "Resume analyzed successfully",
        description: `${enhancedAnalysis.name} - Match Score: ${enhancedAnalysis.match_score}%`,
      });

      return enhancedAnalysis;
    } catch (error) {
      console.error("Error analyzing resume:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to analyze resume";
      
      toast({
        title: "Analysis failed",
        description: errorMessage,
        variant: "destructive",
      });
      
      throw error;
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleDecision = async (decision: 'approve' | 'reject', selectedJobId?: string): Promise<void> => {
    if (!analysisResult || !analysisResult.candidate_id) return;

    try {
      console.log(`Processing ${decision} decision for ${analysisResult.name}`);
      
      // Use server-side API to approve or reject candidate
      const endpoint = decision === 'approve' 
        ? `/api/candidates/${analysisResult.candidate_id}/approve`
        : `/api/candidates/${analysisResult.candidate_id}/reject`;
        
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          jobPostingId: selectedJobId || null,
          notes: `Overall Score: ${analysisResult.overall_score}/10, Match: ${analysisResult.match_score}%`
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        console.error('Error processing decision:', error);
        throw new Error(error.error || 'Failed to process decision');
      }

      toast({
        title: `Candidate ${decision}d`,
        description: `${analysisResult.name} has been ${decision}d and ${decision === 'approve' ? 'moved to next stage' : 'archived'}`,
        variant: decision === 'approve' ? 'default' : 'destructive',
      });
    } catch (error) {
      console.error('Error processing decision:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process decision",
        variant: "destructive",
      });
    }
  };

  return {
    isAnalyzing,
    analysisResult,
    analyzeResumeData,
    handleDecision,
    setAnalysisResult
  };
};
