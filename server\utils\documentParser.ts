/**
 * TypeScript Document Parser
 * Replaces the Python document_parser.py with equivalent TypeScript functionality
 * 
 * This module provides the same four main functions:
 * 1. extractTextFromDocx - Extract text from Word documents
 * 2. extractTextFromPdf - Extract text from PDF documents  
 * 3. extractContactInfo - Extract contact information using regex
 * 4. analyzeWithOpenAI - Use OpenAI for advanced analysis
 */

import * as fs from 'fs';
import * as path from 'path';
import {
  ContactInfo,
  JobAnalysis,
  OpenAIAnalysisResult,
  ParsedResumeData,
  TextExtractionResult,
  DocumentParserError,
  PHONE_PATTERNS,
  EMAIL_PATTERN,
  LINKEDIN_PATTERN,
  LOCATION_PATTERN,
  DEFAULT_OPENAI_CONFIG
} from '../types/documentParser.js';

/**
 * Extract text from Word document (.docx)
 * Equivalent to Python's extract_text_from_docx function
 */
export async function extractTextFromDocx(fileContent: string): Promise<string> {
  try {
    console.log('Extracting text from DOCX document...');

    // Decode base64 content to buffer
    const buffer = Buffer.from(fileContent, 'base64');

    // Use mammoth to extract text from DOCX
    try {
      const mammoth = require('mammoth');
      const result = await mammoth.extractRawText({ buffer });

      if (result.value && result.value.trim()) {
        console.log(`Successfully extracted ${result.value.length} characters from DOCX`);
        return result.value.trim();
      }
    } catch (mammothError) {
      console.warn('Mammoth library not available, falling back to base64 decode:', mammothError.message);
      // Fallback: try to decode as text
      try {
        const text = buffer.toString('utf-8');
        if (text && text.trim()) {
          return text.trim();
        }
      } catch (decodeError) {
        console.warn('Failed to decode DOCX as text:', decodeError.message);
      }
    }

    console.warn('No text extracted from DOCX document');
    return '';

  } catch (error) {
    console.error('Error extracting from DOCX:', error);
    return '';
  }
}

/**
 * Extract text from PDF document
 * Equivalent to Python's extract_text_from_pdf function
 */
export async function extractTextFromPdf(fileContent: string): Promise<string> {
  try {
    console.log('Extracting text from PDF document...');

    // Decode base64 content to buffer
    const buffer = Buffer.from(fileContent, 'base64');

    // Use pdf-parse to extract text from PDF
    try {
      const pdfParse = require('pdf-parse');
      const data = await pdfParse(buffer);

      if (data.text && data.text.trim()) {
        console.log(`Successfully extracted ${data.text.length} characters from PDF`);
        return data.text.trim();
      }
    } catch (pdfError) {
      console.warn('PDF-parse library not available, falling back to base64 decode:', pdfError.message);
      // Fallback: try to decode as text (won't work for binary PDFs, but better than nothing)
      try {
        const text = buffer.toString('utf-8');
        if (text && text.trim()) {
          return text.trim();
        }
      } catch (decodeError) {
        console.warn('Failed to decode PDF as text:', decodeError.message);
      }
    }

    console.warn('No text extracted from PDF document');
    return '';

  } catch (error) {
    console.error('Error extracting from PDF:', error);
    return '';
  }
}

/**
 * Extract contact information using regex patterns
 * Equivalent to Python's extract_contact_info function
 */
export function extractContactInfo(text: string): ContactInfo {
  console.log('Extracting contact information from text...');
  
  const contactInfo: ContactInfo = {
    name: null,
    email: null,
    phone: null,
    location: null,
    linkedin: null
  };

  if (!text || text.trim().length === 0) {
    console.warn('No text provided for contact extraction');
    return contactInfo;
  }

  // Extract email
  const emailMatch = text.match(EMAIL_PATTERN);
  if (emailMatch) {
    contactInfo.email = emailMatch[0];
    console.log('Found email:', contactInfo.email);
  }

  // Extract phone number using multiple patterns
  for (const pattern of PHONE_PATTERNS) {
    const phoneMatch = text.match(pattern);
    if (phoneMatch) {
      contactInfo.phone = phoneMatch[0];
      console.log('Found phone:', contactInfo.phone);
      break;
    }
  }

  // Extract LinkedIn
  const linkedinMatch = text.match(LINKEDIN_PATTERN);
  if (linkedinMatch) {
    contactInfo.linkedin = linkedinMatch[0];
    console.log('Found LinkedIn:', contactInfo.linkedin);
  }

  // Extract name (usually first line or near top)
  const lines = text.split('\n');
  for (let i = 0; i < Math.min(5, lines.length); i++) {
    const line = lines[i].trim();
    if (line && line.split(' ').length <= 4 && line.length > 2) {
      // Skip lines that look like headers or contain special chars
      if (!/[:\|\@\(\)]/.test(line) && !line.toLowerCase().startsWith('email') && 
          !line.toLowerCase().startsWith('phone') && !line.toLowerCase().startsWith('address')) {
        contactInfo.name = line;
        console.log('Found name:', contactInfo.name);
        break;
      }
    }
  }

  // Extract location (look for city, state patterns)
  const locationMatch = text.match(LOCATION_PATTERN);
  if (locationMatch) {
    contactInfo.location = locationMatch[1] || locationMatch[2];
    console.log('Found location:', contactInfo.location);
  }

  return contactInfo;
}

/**
 * Use OpenAI to extract contact information and analyze resume against job description
 * Equivalent to Python's analyze_with_openai function
 */
export async function analyzeWithOpenAI(
  text: string, 
  openaiApiKey: string, 
  jobDescription?: string
): Promise<OpenAIAnalysisResult | null> {
  console.log('Starting OpenAI analysis...');
  
  if (!openaiApiKey || openaiApiKey === 'none') {
    console.warn('No OpenAI API key provided');
    return null;
  }

  const hasJobDescription = jobDescription && jobDescription.trim().length > 0;
  const maxTokens = hasJobDescription ? 1000 : 300;
  
  let prompt: string;
  
  if (hasJobDescription) {
    // Full analysis against job description
    prompt = `Analyze this resume against the job description and provide a comprehensive assessment.

Job Description:
${jobDescription!.substring(0, 1500)}

Resume Text:
${text.substring(0, 2500)}

Provide analysis in this JSON format:
{
  "contactInfo": {
    "name": "Full Name",
    "email": "<EMAIL>", 
    "phone": "phone number",
    "location": "City, State",
    "linkedin": "linkedin URL"
  },
  "analysis": {
    "overall_score": 85,
    "match_score": 78,
    "experience_years": 5,
    "key_skills": ["skill1", "skill2", "skill3"],
    "matched_skills": ["skill1", "skill2"],
    "missing_skills": ["skill3", "skill4"],
    "strengths": [
      "Strong technical background with X years of experience",
      "Proven track record in relevant technologies",
      "Leadership experience managing teams"
    ],
    "concerns": [
      "Limited experience with specific requirement X",
      "No direct experience in industry Y"
    ],
    "recommendation": "HIRE/INTERVIEW/REJECT",
    "detailed_feedback": "This candidate demonstrates strong qualifications for the role. Their experience with [specific technologies/skills] aligns well with the job requirements. Key strengths include [list 2-3 specific strengths from resume]. Areas for development include [list 2-3 areas]. The candidate's background in [relevant experience] makes them a strong fit. Overall assessment: [detailed reasoning for recommendation].",
    "interview_questions": [
      "Can you walk me through your experience with [specific technology from job description]?",
      "How did you handle [specific challenge relevant to the role]?",
      "Tell me about a time you [behavioral question relevant to job requirements]?"
    ]
  }
}

Important: 
- For strengths, provide 3-5 specific, detailed strengths based on the resume content
- For detailed_feedback, write a comprehensive 3-4 sentence assessment explaining the match
- For interview_questions, provide 3 specific questions tailored to both the resume and job requirements
- Base all analysis on actual resume content, not generic responses`;
  } else {
    // Contact info only
    prompt = `Extract contact information from this resume text. Look for:
- Full name (usually at the top)
- Email address
- Phone number
- Location/city
- LinkedIn profile

Resume text:
${text.substring(0, 2000)}

Return only JSON: {"name":"Full Name","email":"<EMAIL>","phone":"phone number","location":"City, State","linkedin":"linkedin URL"}`;
  }

  try {
    console.log('Making OpenAI API request...');
    
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: DEFAULT_OPENAI_CONFIG.model,
        messages: [
          {
            role: 'system',
            content: 'You are an expert HR analyst. Extract information and analyze resumes professionally. Return only valid JSON.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: maxTokens,
        temperature: DEFAULT_OPENAI_CONFIG.temperature
      })
    });

    if (response.ok) {
      const data = await response.json();
      let content = data.choices[0].message.content.trim();
      
      // Clean and extract JSON
      if (content.startsWith('```json')) {
        content = content.replace(/```json/g, '').replace(/```/g, '');
      } else if (content.startsWith('```')) {
        content = content.replace(/```/g, '');
      }
      
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const result = JSON.parse(jsonMatch[0]);
        console.log('OpenAI analysis completed successfully');
        return result;
      }
    } else {
      console.error('OpenAI API request failed:', response.status, response.statusText);
    }
    
  } catch (error) {
    console.error('OpenAI extraction failed:', error);
  }
  
  return null;
}

/**
 * Main document parsing function that replicates Python script functionality
 * Equivalent to Python's main() function
 */
export async function parseDocument(
  fileContent: string,
  fileName: string,
  fileType: string,
  openaiApiKey?: string,
  jobDescription?: string
): Promise<ParsedResumeData> {
  console.log(`Processing ${fileName} (${fileType})`);

  let extractedText = '';

  try {
    // Extract text based on file type
    if (fileType.includes('wordprocessingml') || fileType.includes('msword')) {
      extractedText = await extractTextFromDocx(fileContent);
    } else if (fileType.includes('pdf')) {
      extractedText = await extractTextFromPdf(fileContent);
    } else {
      // For other file types, try to decode as text
      try {
        extractedText = Buffer.from(fileContent, 'base64').toString('utf-8');
      } catch {
        extractedText = '';
      }
    }

    if (!extractedText || extractedText.trim().length === 0) {
      console.warn(`Failed to extract text from ${fileName}`);
      return {
        contactInfo: {
          name: null,
          email: null,
          phone: null,
          location: null,
          linkedin: null
        },
        extractedText: `Failed to extract text from ${fileName}`,
        method: 'Failed extraction',
        hasJobAnalysis: false
      };
    }

    // Try OpenAI extraction first
    const aiResult = await analyzeWithOpenAI(extractedText, openaiApiKey || '', jobDescription);

    if (aiResult) {
      if (jobDescription && aiResult.analysis) {
        // Full analysis with job matching
        return {
          contactInfo: aiResult.contactInfo || {},
          analysis: aiResult.analysis,
          extractedText: extractedText.length > 500 ? extractedText.substring(0, 500) + '...' : extractedText,
          method: 'OpenAI + TypeScript full analysis',
          hasJobAnalysis: true
        };
      } else {
        // Contact info only
        const contactInfo = 'name' in aiResult ? aiResult : aiResult.contactInfo || aiResult;
        return {
          contactInfo: {
            name: contactInfo.name || null,
            email: contactInfo.email || null,
            phone: contactInfo.phone || null,
            location: contactInfo.location || null,
            linkedin: contactInfo.linkedin || null
          },
          extractedText: extractedText.length > 500 ? extractedText.substring(0, 500) + '...' : extractedText,
          method: 'OpenAI + TypeScript parsing',
          hasJobAnalysis: false
        };
      }
    } else {
      // Fallback to regex extraction
      const contactInfo = extractContactInfo(extractedText);
      return {
        contactInfo,
        extractedText: extractedText.length > 500 ? extractedText.substring(0, 500) + '...' : extractedText,
        method: 'TypeScript regex parsing',
        hasJobAnalysis: false
      };
    }

  } catch (error) {
    console.error('Error in document parsing:', error);

    // Fallback to basic extraction
    const contactInfo = extractContactInfo(extractedText || fileContent);
    return {
      contactInfo,
      extractedText: extractedText || `Error processing ${fileName}`,
      method: 'TypeScript error fallback',
      hasJobAnalysis: false
    };
  }
}
