import { Router } from 'express';
import { db } from '../db';
import { interviewsV2, interviewRuns } from '@shared/schema';
import { eq } from 'drizzle-orm';
import crypto from 'crypto';

const router = Router();

interface ZoomWebhookEvent {
  event: string;
  payload: {
    account_id: string;
    object: {
      session_name?: string;
      session_key?: string;
      id?: string;
      uuid?: string;
      start_time?: string;
      end_time?: string;
      duration?: number;
      participant?: {
        user_id?: string;
        user_name?: string;
        join_time?: string;
        leave_time?: string;
      };
    };
  };
  event_ts: number;
}

/**
 * Verify Zoom webhook signature
 */
function verifyZoomWebhook(payload: string, signature: string, timestamp: string): boolean {
  const webhookSecret = process.env.ZOOM_WEBHOOK_SECRET;
  if (!webhookSecret) {
    console.warn('⚠️ ZOOM_WEBHOOK_SECRET not configured, skipping signature verification');
    return true; // Allow in development
  }

  try {
    const message = `v0:${timestamp}:${payload}`;
    const hashForVerify = crypto
      .createHmac('sha256', webhookSecret)
      .update(message, 'utf8')
      .digest('hex');
    
    const expectedSignature = `v0=${hashForVerify}`;
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  } catch (error) {
    console.error('Error verifying Zoom webhook signature:', error);
    return false;
  }
}

/**
 * @swagger
 * /webhooks/zoom:
 *   post:
 *     summary: Zoom Video SDK webhook endpoint
 *     description: Handle Zoom Video SDK events (session started, ended, participant joined/left)
 *     tags: [Webhooks]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               event:
 *                 type: string
 *               payload:
 *                 type: object
 *               event_ts:
 *                 type: number
 *     responses:
 *       200:
 *         description: Webhook processed successfully
 *       400:
 *         description: Invalid webhook signature or payload
 *       500:
 *         description: Webhook processing failed
 */
router.post('/zoom', async (req, res) => {
  try {
    const signature = req.headers['x-zm-signature'] as string;
    const timestamp = req.headers['x-zm-request-timestamp'] as string;
    const payload = JSON.stringify(req.body);

    // Verify webhook signature
    if (signature && timestamp && !verifyZoomWebhook(payload, signature, timestamp)) {
      console.error('❌ Invalid Zoom webhook signature');
      return res.status(400).json({ error: 'Invalid signature' });
    }

    const event: ZoomWebhookEvent = req.body;
    console.log(`📞 Zoom webhook received: ${event.event}`, {
      sessionName: event.payload?.object?.session_name,
      timestamp: new Date(event.event_ts * 1000).toISOString()
    });

    await handleZoomEvent(event);

    res.status(200).json({ success: true });

  } catch (error) {
    console.error('Error processing Zoom webhook:', error);
    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

/**
 * Handle different Zoom webhook events
 */
async function handleZoomEvent(event: ZoomWebhookEvent): Promise<void> {
  const { event: eventType, payload } = event;
  const sessionName = payload?.object?.session_name;

  if (!sessionName) {
    console.warn('⚠️ Zoom webhook missing session_name');
    return;
  }

  try {
    // Find interview by session name
    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(eq(interviewsV2.roomOrMeetingId, sessionName))
      .limit(1);

    if (!interview) {
      console.warn(`⚠️ No interview found for Zoom session: ${sessionName}`);
      return;
    }

    switch (eventType) {
      case 'session.started':
        await handleSessionStarted(interview.id, event);
        break;
      
      case 'session.ended':
        await handleSessionEnded(interview.id, event);
        break;
      
      case 'participant.joined':
        await handleParticipantJoined(interview.id, event);
        break;
      
      case 'participant.left':
        await handleParticipantLeft(interview.id, event);
        break;
      
      case 'recording.completed':
        await handleRecordingCompleted(interview.id, event);
        break;
      
      default:
        console.log(`📞 Unhandled Zoom event: ${eventType}`);
    }

  } catch (error) {
    console.error(`Error handling Zoom event ${eventType}:`, error);
  }
}

/**
 * Handle session started event
 */
async function handleSessionStarted(interviewId: string, event: ZoomWebhookEvent): Promise<void> {
  console.log(`🎥 Zoom session started for interview: ${interviewId}`);

  // Update interview status
  await db
    .update(interviewsV2)
    .set({ status: 'scheduled' })
    .where(eq(interviewsV2.id, interviewId));

  // Create or update interview run
  const startTime = event.payload?.object?.start_time 
    ? new Date(event.payload.object.start_time)
    : new Date();

  await db
    .insert(interviewRuns)
    .values({
      interviewId,
      organizationId: '', // Will be filled by trigger or separate query
      startedAt: startTime,
      status: 'in_progress',
      zoomMeetingId: event.payload?.object?.id || event.payload?.object?.uuid,
      metricsJson: {
        sessionStarted: event.event_ts,
        zoomEventData: event.payload
      }
    })
    .onConflictDoUpdate({
      target: [interviewRuns.interviewId],
      set: {
        startedAt: startTime,
        status: 'in_progress',
        zoomMeetingId: event.payload?.object?.id || event.payload?.object?.uuid
      }
    });
}

/**
 * Handle session ended event
 */
async function handleSessionEnded(interviewId: string, event: ZoomWebhookEvent): Promise<void> {
  console.log(`🎥 Zoom session ended for interview: ${interviewId}`);

  const endTime = event.payload?.object?.end_time 
    ? new Date(event.payload.object.end_time)
    : new Date();

  // Update interview status
  await db
    .update(interviewsV2)
    .set({ status: 'completed' })
    .where(eq(interviewsV2.id, interviewId));

  // Update interview run
  await db
    .update(interviewRuns)
    .set({
      endedAt: endTime,
      status: 'completed',
      metricsJson: {
        sessionEnded: event.event_ts,
        duration: event.payload?.object?.duration,
        zoomEventData: event.payload
      }
    })
    .where(eq(interviewRuns.interviewId, interviewId));
}

/**
 * Handle participant joined event
 */
async function handleParticipantJoined(interviewId: string, event: ZoomWebhookEvent): Promise<void> {
  const participant = event.payload?.object?.participant;
  console.log(`👤 Participant joined interview ${interviewId}:`, participant?.user_name);

  // Update metrics
  const [existingRun] = await db
    .select()
    .from(interviewRuns)
    .where(eq(interviewRuns.interviewId, interviewId))
    .limit(1);

  if (existingRun) {
    const currentMetrics = existingRun.metricsJson as any || {};
    const participants = currentMetrics.participants || [];
    
    participants.push({
      userId: participant?.user_id,
      userName: participant?.user_name,
      joinTime: participant?.join_time || new Date().toISOString(),
      event: 'joined'
    });

    await db
      .update(interviewRuns)
      .set({
        metricsJson: {
          ...currentMetrics,
          participants
        }
      })
      .where(eq(interviewRuns.interviewId, interviewId));
  }
}

/**
 * Handle participant left event
 */
async function handleParticipantLeft(interviewId: string, event: ZoomWebhookEvent): Promise<void> {
  const participant = event.payload?.object?.participant;
  console.log(`👤 Participant left interview ${interviewId}:`, participant?.user_name);

  // Update metrics with leave time
  const [existingRun] = await db
    .select()
    .from(interviewRuns)
    .where(eq(interviewRuns.interviewId, interviewId))
    .limit(1);

  if (existingRun) {
    const currentMetrics = existingRun.metricsJson as any || {};
    const participants = currentMetrics.participants || [];
    
    // Find and update the participant's leave time
    const participantIndex = participants.findIndex(
      (p: any) => p.userId === participant?.user_id
    );
    
    if (participantIndex >= 0) {
      participants[participantIndex].leaveTime = participant?.leave_time || new Date().toISOString();
    }

    await db
      .update(interviewRuns)
      .set({
        metricsJson: {
          ...currentMetrics,
          participants
        }
      })
      .where(eq(interviewRuns.interviewId, interviewId));
  }
}

/**
 * Handle recording completed event
 */
async function handleRecordingCompleted(interviewId: string, event: ZoomWebhookEvent): Promise<void> {
  console.log(`🎬 Recording completed for interview: ${interviewId}`);
  
  // This will be handled by the recording pipeline
  // For now, just log the event
  console.log('Recording data:', event.payload?.object);
}

export default router;
