import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardH<PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { 
  Phone, 
  DollarSign, 
  CheckCircle, 
  XCircle, 
  Info,
  CreditCard,
  Settings
} from 'lucide-react';

export default function TwilioSetup() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch Twilio account info
  const { data: twilioInfo, isLoading, error } = useQuery({
    queryKey: ['/api/twilio-setup/info'],
    queryFn: async () => {
      const response = await fetch('/api/twilio-setup/info', {
        credentials: 'include'
      });
      if (!response.ok) throw new Error('Failed to fetch Twilio info');
      return response.json();
    }
  });

  // Buy phone number mutation
  const buyNumberMutation = useMutation({
    mutationFn: async ({ areaCode }: { areaCode?: string }) => {
      const response = await fetch('/api/twilio-setup/buy-number', {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ areaCode })
      });
      if (!response.ok) throw new Error('Failed to buy phone number');
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Phone Number Purchased",
        description: "Your Twilio phone number has been set up successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/twilio-setup/info'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Purchase Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4" />
            <p className="text-gray-600">Connecting to Twilio...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <XCircle className="w-5 h-5" />
            Twilio Setup Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <Info className="w-4 h-4" />
            <AlertDescription>
              Unable to connect to Twilio. Please check your credentials:
              <br />
              - TWILIO_ACCOUNT_SID: {process.env.TWILIO_ACCOUNT_SID ? 'Set' : 'Missing'}
              <br />
              - TWILIO_AUTH_TOKEN: {process.env.TWILIO_AUTH_TOKEN ? 'Set' : 'Missing'}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Account Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-600" />
            Twilio Account Connected
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Account SID</p>
              <p className="font-mono text-sm">{twilioInfo?.account?.sid}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Account Status</p>
              <Badge className={`${
                twilioInfo?.account?.status === 'active' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {twilioInfo?.account?.status}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-gray-600">Account Type</p>
              <p className="font-medium">{twilioInfo?.account?.type}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Balance</p>
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-green-600" />
                <span className="font-medium">
                  {twilioInfo?.balance?.balance} {twilioInfo?.balance?.currency}
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Phone Numbers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="w-5 h-5 text-blue-600" />
            Phone Numbers
          </CardTitle>
        </CardHeader>
        <CardContent>
          {twilioInfo?.phoneNumbers?.length > 0 ? (
            <div className="space-y-3">
              {twilioInfo.phoneNumbers.map((number: any) => (
                <div key={number.sid} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium">{number.phoneNumber}</p>
                    <p className="text-sm text-gray-600">{number.friendlyName || 'Voice Agent Number'}</p>
                  </div>
                  <div className="flex gap-2">
                    {number.capabilities.voice && (
                      <Badge className="bg-blue-100 text-blue-800">Voice</Badge>
                    )}
                    {number.capabilities.sms && (
                      <Badge className="bg-green-100 text-green-800">SMS</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Phone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">No Phone Numbers</h3>
              <p className="text-gray-600 mb-4">
                You need a Twilio phone number to make voice calls. Purchase one to get started.
              </p>
              <Button
                onClick={() => buyNumberMutation.mutate({})}
                disabled={buyNumberMutation.isPending}
                className="flex items-center gap-2"
              >
                <CreditCard className="w-4 h-4" />
                {buyNumberMutation.isPending ? 'Purchasing...' : 'Buy Phone Number'}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Voice Agent Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5 text-purple-600" />
            Voice Agent Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Alert>
              <CheckCircle className="w-4 h-4" />
              <AlertDescription>
                <strong>Twilio Voice Agent is Ready!</strong>
                <br />
                Your voice agent can now:
                <ul className="mt-2 ml-4 list-disc">
                  <li>Make automated calls to candidates</li>
                  <li>Conduct AI-powered conversations about technical interviews</li>
                  <li>Record calls and generate transcriptions</li>
                  <li>Capture availability information automatically</li>
                  <li>Create follow-up notes and action items</li>
                </ul>
              </AlertDescription>
            </Alert>

            {twilioInfo?.phoneNumbers?.length > 0 && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Ready to Use</h4>
                <p className="text-blue-700 text-sm">
                  Go to any candidate's details page and click the "Voice Agent" tab to start making calls.
                  The system will use <strong>{twilioInfo.phoneNumbers[0].phoneNumber}</strong> as the caller ID.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}