
import React from 'react';
import { Card } from "@/components/ui/card";

interface InterviewQuestionsProps {
  questions: string[];
}

const InterviewQuestions: React.FC<InterviewQuestionsProps> = ({ questions }) => {
  // Provide safe defaults if questions is undefined or not an array
  const safeQuestions = Array.isArray(questions) ? questions : [];

  if (safeQuestions.length === 0) {
    return (
      <div>
        <h4 className="font-medium text-lg mb-2">Tailored Interview Questions</h4>
        <Card className="p-3">
          <p className="text-sm text-gray-500">No interview questions available for this analysis.</p>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <h4 className="font-medium text-lg mb-2">Tailored Interview Questions</h4>
      <div className="space-y-2">
        {safeQuestions.map((question, index) => (
          <Card key={index} className="p-3">
            <p className="text-sm font-medium text-blue-700">Q{index + 1}:</p>
            <p className="text-sm mt-1">{question}</p>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default InterviewQuestions;
