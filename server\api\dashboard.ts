import { Router } from 'express';
import { db } from '../db';
import { 
  jobPostings, 
  candidates, 
  applications, 
  interviews, 
  voiceCalls,
  auditLogs,
  authUsers 
} from '@shared/schema';
import { eq, and, desc, count, sql, gte } from 'drizzle-orm';
import { authenticateToken, AuthenticatedRequest } from '../auth';
import { auditLoggerMiddleware } from '../middleware/auditLogger';

const router = Router();

/**
 * @swagger
 * /api/dashboard/metrics:
 *   get:
 *     summary: Get executive dashboard metrics
 *     description: Fetch key metrics and statistics for the organization dashboard
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: period
 *         in: query
 *         description: Time period for trend data
 *         schema:
 *           type: string
 *           enum: [week, month, quarter, year]
 *           default: month
 *     responses:
 *       200:
 *         description: Dashboard metrics data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 metrics:
 *                   type: object
 *                 trends:
 *                   type: object
 *                 recentActivity:
 *                   type: array
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/metrics', authenticateToken, auditLoggerMiddleware('dashboard', 'view_metrics'), async (req: AuthenticatedRequest, res) => {
  try {
    const organizationId = req.user!.organizationId;
    const { period = 'month' } = req.query;

    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date();
    switch (period) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'quarter':
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default: // month
        startDate.setMonth(now.getMonth() - 1);
    }

    // Get job postings metrics
    const [totalJobPostings] = await db
      .select({ count: count() })
      .from(jobPostings)
      .where(eq(jobPostings.organizationId, organizationId));

    const [activeJobPostings] = await db
      .select({ count: count() })
      .from(jobPostings)
      .where(and(
        eq(jobPostings.organizationId, organizationId),
        eq(jobPostings.isActive, true)
      ));

    const [newJobPostings] = await db
      .select({ count: count() })
      .from(jobPostings)
      .where(and(
        eq(jobPostings.organizationId, organizationId),
        gte(jobPostings.createdAt, startDate)
      ));

    // Get candidates metrics
    const [totalCandidates] = await db
      .select({ count: count() })
      .from(candidates)
      .where(eq(candidates.organizationId, organizationId));

    const [newCandidates] = await db
      .select({ count: count() })
      .from(candidates)
      .where(and(
        eq(candidates.organizationId, organizationId),
        gte(candidates.createdAt, startDate)
      ));

    // Get candidates by status
    const candidatesByStatus = await db
      .select({
        status: candidates.status,
        count: count()
      })
      .from(candidates)
      .where(eq(candidates.organizationId, organizationId))
      .groupBy(candidates.status);

    // Get applications metrics
    const [totalApplications] = await db
      .select({ count: count() })
      .from(applications)
      .leftJoin(candidates, eq(applications.candidateId, candidates.id))
      .where(eq(candidates.organizationId, organizationId));

    const applicationsByStatus = await db
      .select({
        status: applications.status,
        count: count()
      })
      .from(applications)
      .leftJoin(candidates, eq(applications.candidateId, candidates.id))
      .where(eq(candidates.organizationId, organizationId))
      .groupBy(applications.status);

    // Get interviews metrics
    const [totalInterviews] = await db
      .select({ count: count() })
      .from(interviews)
      .leftJoin(candidates, eq(interviews.candidateId, candidates.id))
      .where(eq(candidates.organizationId, organizationId));

    const [scheduledInterviews] = await db
      .select({ count: count() })
      .from(interviews)
      .leftJoin(candidates, eq(interviews.candidateId, candidates.id))
      .where(and(
        eq(candidates.organizationId, organizationId),
        eq(interviews.status, 'scheduled')
      ));

    // Get voice calls metrics
    const [totalVoiceCalls] = await db
      .select({ count: count() })
      .from(voiceCalls)
      .where(eq(voiceCalls.organizationId, organizationId));

    const [completedVoiceCalls] = await db
      .select({ count: count() })
      .from(voiceCalls)
      .where(and(
        eq(voiceCalls.organizationId, organizationId),
        eq(voiceCalls.status, 'completed')
      ));

    // Get trend data for charts (last 7 days)
    const last7Days = new Date();
    last7Days.setDate(now.getDate() - 7);

    const candidateTrends = await db
      .select({
        date: sql<string>`DATE(${candidates.createdAt})`,
        count: count()
      })
      .from(candidates)
      .where(and(
        eq(candidates.organizationId, organizationId),
        gte(candidates.createdAt, last7Days)
      ))
      .groupBy(sql`DATE(${candidates.createdAt})`)
      .orderBy(sql`DATE(${candidates.createdAt})`);

    const applicationTrends = await db
      .select({
        date: sql<string>`DATE(${applications.appliedAt})`,
        count: count()
      })
      .from(applications)
      .leftJoin(candidates, eq(applications.candidateId, candidates.id))
      .where(and(
        eq(candidates.organizationId, organizationId),
        gte(applications.appliedAt, last7Days)
      ))
      .groupBy(sql`DATE(${applications.appliedAt})`)
      .orderBy(sql`DATE(${applications.appliedAt})`);

    // Compile metrics response
    const metrics = {
      jobPostings: {
        total: totalJobPostings.count,
        active: activeJobPostings.count,
        inactive: totalJobPostings.count - activeJobPostings.count,
        new: newJobPostings.count
      },
      candidates: {
        total: totalCandidates.count,
        new: newCandidates.count,
        byStatus: candidatesByStatus.reduce((acc, item) => {
          acc[item.status || 'unknown'] = item.count;
          return acc;
        }, {} as Record<string, number>)
      },
      applications: {
        total: totalApplications.count,
        byStatus: applicationsByStatus.reduce((acc, item) => {
          acc[item.status || 'unknown'] = item.count;
          return acc;
        }, {} as Record<string, number>)
      },
      interviews: {
        total: totalInterviews.count,
        scheduled: scheduledInterviews.count
      },
      voiceCalls: {
        total: totalVoiceCalls.count,
        completed: completedVoiceCalls.count
      }
    };

    const trends = {
      candidates: candidateTrends,
      applications: applicationTrends
    };

    res.json({
      success: true,
      metrics,
      trends,
      period,
      generatedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Dashboard metrics error:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard metrics' });
  }
});

/**
 * @swagger
 * /api/dashboard/audit-logs:
 *   get:
 *     summary: Get recent audit logs
 *     description: Fetch recent audit logs for the organization with filtering
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: limit
 *         in: query
 *         description: Number of logs to return
 *         schema:
 *           type: integer
 *           default: 50
 *       - name: action
 *         in: query
 *         description: Filter by action type
 *         schema:
 *           type: string
 *       - name: resourceType
 *         in: query
 *         description: Filter by resource type
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Audit logs data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/audit-logs', authenticateToken, auditLoggerMiddleware('dashboard', 'view_audit_logs'), async (req: AuthenticatedRequest, res) => {
  try {
    const organizationId = req.user!.organizationId;
    const { limit = '50', action, resourceType } = req.query;
    const limitNum = parseInt(limit as string, 10);

    const conditions = [eq(auditLogs.organizationId, organizationId)];

    if (action) {
      conditions.push(eq(auditLogs.action, action as string));
    }

    if (resourceType) {
      conditions.push(eq(auditLogs.resourceType, resourceType as string));
    }

    const logs = await db
      .select({
        id: auditLogs.id,
        action: auditLogs.action,
        resourceType: auditLogs.resourceType,
        resourceId: auditLogs.resourceId,
        userId: auditLogs.userId,
        userEmail: auditLogs.userEmail,
        userRole: auditLogs.userRole,
        details: auditLogs.details,
        ipAddress: auditLogs.ipAddress,
        success: auditLogs.success,
        errorMessage: auditLogs.errorMessage,
        createdAt: auditLogs.createdAt
      })
      .from(auditLogs)
      .where(conditions.length === 1 ? conditions[0] : and(...conditions))
      .orderBy(desc(auditLogs.createdAt))
      .limit(limitNum);

    res.json({
      success: true,
      logs,
      total: logs.length
    });

  } catch (error) {
    console.error('Audit logs fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch audit logs' });
  }
});

export default router;

