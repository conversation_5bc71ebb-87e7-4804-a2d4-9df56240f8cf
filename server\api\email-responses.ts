import { Router } from 'express';
import { gmailService } from '../services/gmailService';

const router = Router();

// Manually trigger email response check
router.post('/check-responses', async (req, res) => {
  try {
    if (!gmailService.isAuthenticated()) {
      return res.status(401).json({ error: 'Gmail not authenticated' });
    }

    console.log('Manually checking for email responses...');
    await gmailService.checkForEmailResponses();
    
    res.json({ 
      success: true, 
      message: 'Email response check completed' 
    });
  } catch (error) {
    console.error('Error checking email responses:', error);
    res.status(500).json({ error: 'Failed to check email responses' });
  }
});

// Force start monitoring
router.post('/start-monitoring', async (req, res) => {
  try {
    if (!gmailService.isAuthenticated()) {
      return res.status(401).json({ error: 'Gmail not authenticated' });
    }

    console.log('Manually starting Gmail monitoring...');
    await gmailService.startMonitoring();
    
    res.json({ 
      success: true, 
      message: 'Gmail monitoring started' 
    });
  } catch (error) {
    console.error('Error starting monitoring:', error);
    res.status(500).json({ error: 'Failed to start monitoring' });
  }
});

export default router;