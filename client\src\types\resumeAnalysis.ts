
export interface SkillAnalysis {
  matched_skills: string[];
  missing_skills: string[];
  transferable_skills: string[];
}

export interface ExperienceAnalysis {
  relevant_experience: string;
  experience_gap: string;
  career_progression: string;
}

export interface Role {
  title: string;
  company: string;
}

export interface EnhancedAnalysisResult {
  name: string;
  email?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
  experience_years: number;
  key_skills: string[];
  recent_roles: Role[];
  education: string;
  overall_score: number;
  match_score: number;
  strengths: string[];
  concerns: string[];
  skill_analysis: SkillAnalysis;
  experience_analysis: ExperienceAnalysis;
  recommendation: string;
  detailed_feedback: string;
  interview_questions: string[];
  candidate_id?: string; // Add this property to store the candidate ID from database
}
