import { Router } from 'express';
import { db } from '../db';
import { jobPostings, candidates, applications } from '@shared/schema';
import { eq, desc, like, and, isNull, or, sql, isNotNull } from 'drizzle-orm';
import { z } from 'zod';
import { authenticateToken, AuthenticatedRequest } from '../auth';
import { requireOrganizationAccess, requireRole } from '../middleware/organizationAccess';
import { auditLoggerMiddleware, criticalActionLogger } from '../middleware/auditLogger';

const router = Router();

// Schema for creating job postings
const createJobPostingSchema = z.object({
  title: z.string(),
  description: z.string(),
  detailedDescription: z.string().optional(),
  responsibilities: z.string().optional(),
  requirements: z.string().optional(),
  preferredQualifications: z.string().optional(),
  benefits: z.string().optional(),
  companyOverview: z.string().optional(),
  workEnvironment: z.string().optional(),
  growthOpportunities: z.string().optional(),
  department: z.string().optional(),
  location: z.string().optional(),
  salaryRange: z.string().optional(),
  employmentType: z.string().default('full-time'),
  skillsRequired: z.array(z.string()).optional(),
  experienceLevel: z.string().optional(),
  educationRequirement: z.string().optional(),
  keywords: z.array(z.string()).optional(),
  externalJobId: z.string().optional(),
  sourceUrl: z.string().optional(),
  sourcePlatform: z.string().optional(),
});

// Schema for job posting analysis
const analyzeJobPostingSchema = z.object({
  description: z.string(),
  requirements: z.string().optional(),
});

// Get active job postings with candidate counts - organization scoped
router.get('/', authenticateToken, requireOrganizationAccess, auditLoggerMiddleware('job_posting', 'list'), async (req: AuthenticatedRequest, res) => {
  try {
    const organizationId = req.user!.organizationId;
    
    // CRITICAL SECURITY: Organization-scoped data access with NULL protection
    let allJobPostings;
    
    if (req.user!.role === 'super_admin') {
      // Super admin can see all orgs but NEVER null org records (security vulnerability)
      allJobPostings = await db
        .select({
          id: jobPostings.id,
          title: jobPostings.title,
          description: jobPostings.description,
          requirements: jobPostings.requirements,
          department: jobPostings.department,
          location: jobPostings.location,
          salaryRange: jobPostings.salaryRange,
          employmentType: jobPostings.employmentType,
          skillsRequired: jobPostings.skillsRequired,
          experienceLevel: jobPostings.experienceLevel,
          educationRequirement: jobPostings.educationRequirement,
          keywords: jobPostings.keywords,
          sourcePlatform: jobPostings.sourcePlatform,
          sourceUrl: jobPostings.sourceUrl,
          isActive: jobPostings.isActive,
          organizationId: jobPostings.organizationId,
          createdAt: jobPostings.createdAt,
          updatedAt: jobPostings.updatedAt,
          lastSynced: jobPostings.lastSynced,
        })
        .from(jobPostings)
        .where(and(
          eq(jobPostings.isActive, true),
          isNotNull(jobPostings.organizationId)
        ))
        .orderBy(desc(jobPostings.createdAt));
    } else {
      // Regular users only see their organization's job postings
      allJobPostings = await db
        .select({
          id: jobPostings.id,
          title: jobPostings.title,
          description: jobPostings.description,
          requirements: jobPostings.requirements,
          department: jobPostings.department,
          location: jobPostings.location,
          salaryRange: jobPostings.salaryRange,
          employmentType: jobPostings.employmentType,
          skillsRequired: jobPostings.skillsRequired,
          experienceLevel: jobPostings.experienceLevel,
          educationRequirement: jobPostings.educationRequirement,
          keywords: jobPostings.keywords,
          sourcePlatform: jobPostings.sourcePlatform,
          sourceUrl: jobPostings.sourceUrl,
          isActive: jobPostings.isActive,
          organizationId: jobPostings.organizationId,
          createdAt: jobPostings.createdAt,
          updatedAt: jobPostings.updatedAt,
          lastSynced: jobPostings.lastSynced,
        })
        .from(jobPostings)
        .where(and(
          eq(jobPostings.isActive, true),
          eq(jobPostings.organizationId, organizationId!),
          isNotNull(jobPostings.organizationId)
        ))
        .orderBy(desc(jobPostings.createdAt));
    }

    // Get candidate counts for each job posting (organization-scoped)
    const jobPostingsWithCounts = await Promise.all(
      allJobPostings.map(async (job) => {
        let candidateQuery = db
          .select({ count: candidates.id })
          .from(candidates)
          .where(eq(candidates.appliedJobId, job.id));

        // For non-super admin users, also filter candidates by organization
        if (req.user!.role !== 'super_admin') {
          candidateQuery = candidateQuery.where(eq(candidates.organizationId, organizationId!));
        }

        const candidateCount = await candidateQuery;

        return {
          ...job,
          candidateCount: candidateCount.length,
        };
      })
    );

    res.json(jobPostingsWithCounts);
  } catch (error) {
    console.error('Error fetching job postings:', error);
    res.status(500).json({ error: 'Failed to fetch job postings' });
  }
});

/**
 * @swagger
 * /job-postings/all:
 *   get:
 *     summary: Get all job postings
 *     description: Retrieve all job postings including inactive ones with candidate counts
 *     tags: [Job Postings]
 *     responses:
 *       200:
 *         description: List of all job postings with candidate counts
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 allOf:
 *                   - $ref: '#/components/schemas/JobPosting'
 *                   - type: object
 *                     properties:
 *                       candidateCount:
 *                         type: number
 *       500:
 *         description: Internal server error
 */
router.get('/all', authenticateToken, requireOrganizationAccess, auditLoggerMiddleware('job_posting', 'list_all'), async (req: AuthenticatedRequest, res) => {
  try {
    const organizationId = req.user!.organizationId;
    
    // CRITICAL SECURITY: Organization-scoped access with NULL protection
    let allJobPostings;
    
    if (req.user!.role === 'super_admin') {
      // Super admin can see all orgs but NEVER null org records (security vulnerability)
      allJobPostings = await db
        .select({
          id: jobPostings.id,
          title: jobPostings.title,
          description: jobPostings.description,
          requirements: jobPostings.requirements,
          department: jobPostings.department,
          location: jobPostings.location,
          salaryRange: jobPostings.salaryRange,
          employmentType: jobPostings.employmentType,
          skillsRequired: jobPostings.skillsRequired,
          experienceLevel: jobPostings.experienceLevel,
          educationRequirement: jobPostings.educationRequirement,
          keywords: jobPostings.keywords,
          sourcePlatform: jobPostings.sourcePlatform,
          sourceUrl: jobPostings.sourceUrl,
          isActive: jobPostings.isActive,
          organizationId: jobPostings.organizationId,
          createdAt: jobPostings.createdAt,
          updatedAt: jobPostings.updatedAt,
          lastSynced: jobPostings.lastSynced,
        })
        .from(jobPostings)
        .where(isNotNull(jobPostings.organizationId))
        .orderBy(desc(jobPostings.createdAt));
    } else {
      // Regular users only see their organization's job postings (including inactive)
      allJobPostings = await db
        .select({
          id: jobPostings.id,
          title: jobPostings.title,
          description: jobPostings.description,
          requirements: jobPostings.requirements,
          department: jobPostings.department,
          location: jobPostings.location,
          salaryRange: jobPostings.salaryRange,
          employmentType: jobPostings.employmentType,
          skillsRequired: jobPostings.skillsRequired,
          experienceLevel: jobPostings.experienceLevel,
          educationRequirement: jobPostings.educationRequirement,
          keywords: jobPostings.keywords,
          sourcePlatform: jobPostings.sourcePlatform,
          sourceUrl: jobPostings.sourceUrl,
          isActive: jobPostings.isActive,
          organizationId: jobPostings.organizationId,
          createdAt: jobPostings.createdAt,
          updatedAt: jobPostings.updatedAt,
          lastSynced: jobPostings.lastSynced,
        })
        .from(jobPostings)
        .where(and(
          eq(jobPostings.organizationId, organizationId!),
          isNotNull(jobPostings.organizationId)
        ))
        .orderBy(desc(jobPostings.createdAt));
    }

    // Get candidate counts for each job posting (organization-scoped)
    const jobPostingsWithCounts = await Promise.all(
      allJobPostings.map(async (job) => {
        let candidateQuery = db
          .select({ count: candidates.id })
          .from(candidates)
          .where(eq(candidates.appliedJobId, job.id));

        // For non-super admin users, also filter candidates by organization
        if (req.user!.role !== 'super_admin') {
          candidateQuery = candidateQuery.where(eq(candidates.organizationId, organizationId!));
        }

        const candidateCount = await candidateQuery;

        return {
          ...job,
          candidateCount: candidateCount.length,
        };
      })
    );

    res.json(jobPostingsWithCounts);
  } catch (error) {
    console.error('Error fetching all job postings:', error);
    res.status(500).json({ error: 'Failed to fetch job postings' });
  }
});

/**
 * @swagger
 * /job-postings/{id}:
 *   get:
 *     summary: Get job posting by ID
 *     description: Retrieve a specific job posting with detailed analytics
 *     tags: [Job Postings]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Job posting ID
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Job posting details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/JobPosting'
 *       404:
 *         description: Job posting not found
 *       500:
 *         description: Internal server error
 */
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [jobPosting] = await db
      .select()
      .from(jobPostings)
      .where(eq(jobPostings.id, id));

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    // Get candidates for this job posting
    const relatedCandidates = await db
      .select()
      .from(candidates)
      .where(eq(candidates.appliedJobId, id));

    // Get application analytics
    const applications = await db
      .select()
      .from(candidates)
      .where(eq(candidates.appliedJobId, id));

    const analytics = {
      totalApplications: applications.length,
      pendingReview: applications.filter(c => c.status === 'pending_review').length,
      approvedForInterview: applications.filter(c => c.status === 'approved_for_interview').length,
      rejected: applications.filter(c => c.status === 'rejected').length,
      hired: applications.filter(c => c.status === 'hired').length,
      averageScore: applications.length > 0 
        ? Math.round(applications.reduce((sum, c) => sum + (c.overallScore || 0), 0) / applications.length)
        : 0,
    };

    res.json({
      ...jobPosting,
      candidates: relatedCandidates,
      analytics,
    });
  } catch (error) {
    console.error('Error fetching job posting:', error);
    res.status(500).json({ error: 'Failed to fetch job posting' });
  }
});

// Create new job posting
router.post('/', authenticateToken, requireOrganizationAccess, auditLoggerMiddleware('job_posting', 'create'), async (req: AuthenticatedRequest, res) => {
  try {
    const organizationId = req.user!.organizationId;
    const validatedData = createJobPostingSchema.parse(req.body);

    // Use AI to generate enhanced job details if OpenAI is available
    let aiGeneratedSummary = '';
    let matchingCriteria = '';

    if (process.env.OPENAI_API_KEY) {
      try {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'You are an HR AI assistant that analyzes job postings to create summaries and matching criteria.'
              },
              {
                role: 'user',
                content: `Analyze this job posting and provide:
1. A 2-sentence AI summary highlighting key requirements
2. Matching criteria as a JSON object with weights for skills, experience, education

Job Title: ${validatedData.title}
Description: ${validatedData.description}
Requirements: ${validatedData.requirements || 'Not specified'}

Format your response as:
SUMMARY: [summary here]
CRITERIA: [JSON object here]`
              }
            ],
            max_tokens: 500,
            temperature: 0.3,
          }),
        });

        if (response.ok) {
          const aiResponse = await response.json();
          const content = aiResponse.choices[0]?.message?.content || '';
          
          const summaryMatch = content.match(/SUMMARY:\s*(.+?)(?=CRITERIA:|$)/s);
          const criteriaMatch = content.match(/CRITERIA:\s*(\{.+\})/s);
          
          if (summaryMatch) aiGeneratedSummary = summaryMatch[1].trim();
          if (criteriaMatch) matchingCriteria = criteriaMatch[1].trim();
        }
      } catch (aiError) {
        console.log('AI analysis failed, continuing without it:', aiError);
      }
    }

    const [newJobPosting] = await db
      .insert(jobPostings)
      .values({
        ...validatedData,
        organizationId: organizationId!,
        postedBy: req.user!.id,
        // aiGeneratedSummary, // Field doesn't exist in schema
        matchingCriteria,
        lastSynced: new Date(),
      })
      .returning();

    res.status(201).json(newJobPosting);
  } catch (error) {
    console.error('Error creating job posting:', error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: 'Invalid input', details: error.errors });
    }
    res.status(500).json({ error: 'Failed to create job posting' });
  }
});

// Update job posting
router.put('/:id', authenticateToken, requireOrganizationAccess, auditLoggerMiddleware('job_posting', 'update'), async (req: AuthenticatedRequest, res) => {
  try {
    const { id } = req.params;
    const organizationId = req.user!.organizationId;
    const validatedData = createJobPostingSchema.partial().parse(req.body);

    // Verify job posting belongs to organization
    const [existingJob] = await db
      .select()
      .from(jobPostings)
      .where(and(
        eq(jobPostings.id, id),
        eq(jobPostings.organizationId, organizationId!)
      ))
      .limit(1);

    if (!existingJob) {
      return res.status(404).json({ error: 'Job posting not found or access denied' });
    }

    const [updatedJobPosting] = await db
      .update(jobPostings)
      .set({
        ...validatedData,
        updatedAt: new Date(),
      })
      .where(eq(jobPostings.id, id))
      .returning();

    res.json(updatedJobPosting);
  } catch (error) {
    console.error('Error updating job posting:', error);
    res.status(500).json({ error: 'Failed to update job posting' });
  }
});

// Auto-match candidates to job postings based on skills and experience
router.post('/:id/auto-match', async (req, res) => {
  try {
    const { id } = req.params;

    const [jobPosting] = await db
      .select()
      .from(jobPostings)
      .where(eq(jobPostings.id, id));

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    // Get candidates without assigned jobs or with low match scores
    const availableCandidates = await db
      .select()
      .from(candidates)
      .where(
        or(
          isNull(candidates.appliedJobId),
          and(
            eq(candidates.appliedJobId, id),
            // Re-evaluate existing candidates with low scores
          )
        )
      );

    const matchResults = [];

    for (const candidate of availableCandidates) {
      let matchScore = 0;
      const matchedSkills = [];
      const candidateSkills = candidate.skills || [];
      const requiredSkills = jobPosting.skillsRequired || [];

      // Calculate skill match score
      for (const skill of requiredSkills) {
        if (candidateSkills.some(cs => 
          cs.toLowerCase().indexOf(skill.toLowerCase()) !== -1 ||
          skill.toLowerCase().indexOf(cs.toLowerCase()) !== -1
        )) {
          matchedSkills.push(skill);
          matchScore += 15; // 15 points per matched skill
        }
      }

      // Experience level matching
      const candidateYears = candidate.experienceYears || 0;
      const requiredLevel = jobPosting.experienceLevel;
      
      if (requiredLevel) {
        if (requiredLevel === 'entry' && candidateYears <= 3) matchScore += 20;
        else if (requiredLevel === 'mid' && candidateYears >= 3 && candidateYears <= 7) matchScore += 20;
        else if (requiredLevel === 'senior' && candidateYears >= 7) matchScore += 20;
        else if (requiredLevel === 'executive' && candidateYears >= 10) matchScore += 20;
      }

      // Location matching (if specified)
      if (jobPosting.location && candidate.location) {
        if (candidate.location.toLowerCase().indexOf(jobPosting.location.toLowerCase()) !== -1 ||
            jobPosting.location.toLowerCase().indexOf(candidate.location.toLowerCase()) !== -1) {
          matchScore += 10;
        }
      }

      // Only suggest matches above threshold
      if (matchScore >= 30) {
        // Update candidate with job assignment
        await db
          .update(candidates)
          .set({
            appliedJobId: id,
            matchScore: matchScore,
            updatedAt: new Date(),
          })
          .where(eq(candidates.id, candidate.id));

        matchResults.push({
          candidateId: candidate.id,
          candidateName: candidate.fullName,
          matchScore: matchScore,
          matchedSkills: matchedSkills,
        });
      }
    }

    res.json({
      jobPostingId: id,
      matchedCandidates: matchResults.length,
      matches: matchResults.sort((a, b) => b.matchScore - a.matchScore),
    });
  } catch (error) {
    console.error('Error auto-matching candidates:', error);
    res.status(500).json({ error: 'Failed to auto-match candidates' });
  }
});

// Sync job postings from external sources (placeholder for future integration)
router.post('/sync/:platform', async (req, res) => {
  try {
    const { platform } = req.params;
    const { urls } = req.body;

    // This would be expanded to integrate with actual job board APIs
    // For now, return a success message
    res.json({
      message: `Job sync initiated for ${platform}`,
      platform,
      urls: urls || [],
      status: 'pending',
      note: 'External API integration would be implemented here',
    });
  } catch (error) {
    console.error('Error syncing job postings:', error);
    res.status(500).json({ error: 'Failed to sync job postings' });
  }
});

// Close a job position
router.post('/:id/close', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if job posting exists
    const [jobPosting] = await db
      .select()
      .from(jobPostings)
      .where(eq(jobPostings.id, id));

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    // Update job posting to inactive
    await db
      .update(jobPostings)
      .set({
        isActive: false,
        updatedAt: new Date(),
      })
      .where(eq(jobPostings.id, id));

    res.json({
      message: 'Position closed successfully',
      jobPostingId: id,
      title: jobPosting.title,
      status: 'closed',
    });
  } catch (error) {
    console.error('Error closing position:', error);
    res.status(500).json({ error: 'Failed to close position' });
  }
});

// Reopen a job position
router.post('/:id/reopen', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if job posting exists
    const [jobPosting] = await db
      .select()
      .from(jobPostings)
      .where(eq(jobPostings.id, id));

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    // Update job posting to active
    await db
      .update(jobPostings)
      .set({
        isActive: true,
        updatedAt: new Date(),
      })
      .where(eq(jobPostings.id, id));

    res.json({
      message: 'Position reopened successfully',
      jobPostingId: id,
      title: jobPosting.title,
      status: 'reopened',
    });
  } catch (error) {
    console.error('Error reopening position:', error);
    res.status(500).json({ error: 'Failed to reopen position' });
  }
});

// Delete a job position permanently
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if job posting exists
    const [jobPosting] = await db
      .select()
      .from(jobPostings)
      .where(eq(jobPostings.id, id));

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    // First, update all candidates that were assigned to this job
    await db
      .update(candidates)
      .set({
        appliedJobId: null,
        matchScore: null,
        updatedAt: new Date(),
      })
      .where(eq(candidates.appliedJobId, id));

    // Then delete the job posting
    await db
      .delete(jobPostings)
      .where(eq(jobPostings.id, id));

    res.json({
      message: 'Position deleted permanently',
      jobPostingId: id,
      title: jobPosting.title,
      status: 'deleted',
    });
  } catch (error) {
    console.error('Error deleting position:', error);
    res.status(500).json({ error: 'Failed to delete position' });
  }
});

// Public endpoint to get job postings by organization (no auth required)
/**
 * @swagger
 * /job-postings/by-organization/{organizationId}:
 *   get:
 *     summary: Get job postings by organization (public)
 *     description: Public endpoint to retrieve all active job postings for a specific organization
 *     tags: [Job Postings]
 *     security: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: List of job postings for the organization
 */
router.get('/by-organization/:organizationId', async (req, res) => {
  try {
    const { organizationId } = req.params;
    
    const jobPostingsByOrg = await db
      .select({
        id: jobPostings.id,
        title: jobPostings.title,
        description: jobPostings.description,
        detailedDescription: jobPostings.detailedDescription,
        responsibilities: jobPostings.responsibilities,
        requirements: jobPostings.requirements,
        preferredQualifications: jobPostings.preferredQualifications,
        benefits: jobPostings.benefits,
        companyOverview: jobPostings.companyOverview,
        workEnvironment: jobPostings.workEnvironment,
        growthOpportunities: jobPostings.growthOpportunities,
        department: jobPostings.department,
        location: jobPostings.location,
        salaryRange: jobPostings.salaryRange,
        employmentType: jobPostings.employmentType,
        experienceLevel: jobPostings.experienceLevel,
        organizationId: jobPostings.organizationId,
        createdAt: jobPostings.createdAt,
        isActive: jobPostings.isActive,
      })
      .from(jobPostings)
      .where(and(
        eq(jobPostings.organizationId, organizationId),
        eq(jobPostings.isActive, true)
      ))
      .orderBy(desc(jobPostings.createdAt));

    // Format the response to match expected interface
    const formattedJobs = jobPostingsByOrg.map(job => ({
      id: job.id,
      title: job.title,
      department: job.department || '',
      location: job.location || '',
      employment_type: job.employmentType || 'full-time',
      experience_level: job.experienceLevel || '',
      salary_range: job.salaryRange || '',
      description: job.description || '',
      responsibilities: job.responsibilities ? job.responsibilities.split('\n').filter(r => r.trim()) : [],
      preferred_qualifications: job.preferredQualifications ? job.preferredQualifications.split('\n').filter(q => q.trim()) : [],
      benefits: job.benefits ? job.benefits.split('\n').filter(b => b.trim()) : [],
      company_overview: job.companyOverview || '',
      work_environment: job.workEnvironment || '',
      growth_opportunities: job.growthOpportunities || '',
      status: job.isActive ? 'active' : 'inactive',
      organization_id: job.organizationId,
      posted_date: job.createdAt
    }));

    res.json(formattedJobs);
  } catch (error) {
    console.error('Error fetching job postings by organization:', error);
    res.status(500).json({ error: 'Failed to fetch job postings' });
  }
});

export default router;