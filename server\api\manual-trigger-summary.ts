import { Request, Response } from 'express';
import { processCallSummaryAsync } from '../services/conversationSummarizationService';

export async function manualTriggerSummary(req: Request, res: Response) {
  try {
    console.log('🚀 Manual trigger endpoint called!');
    if (process.env.NODE_ENV === 'development') {
      console.log('Request received with fields:', Object.keys(req.body));
    }
    
    const { callId, organizationId, transcript } = req.body;
    
    if (!callId || !organizationId || !transcript) {
      console.log('❌ Missing required fields');
      return res.status(400).json({
        error: 'Missing required fields: callId, organizationId, transcript'
      });
    }
    
    console.log(`🚀 Manual trigger: Processing call summary for call ${callId}...`);
    console.log(`Organization ID: ${organizationId}`);
    console.log(`Transcript length: ${transcript.length} characters`);
    
    // Trigger async summarization
    console.log('📞 Calling processCallSummaryAsync...');
    processCallSummaryAsync(callId, organizationId, transcript);
    console.log('✅ processCallSummaryAsync called successfully');
    
    const response = {
      success: true,
      message: `Call summarization triggered for call ${callId}`,
      callId,
      organizationId,
      transcriptLength: transcript.length
    };
    
    console.log('📤 Sending response:', JSON.stringify(response, null, 2));
    res.json(response);
    
  } catch (error) {
    console.error('❌ Error in manual trigger:', error);
    res.status(500).json({
      error: 'Failed to trigger summarization',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}