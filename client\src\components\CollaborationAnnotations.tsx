import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarInitials } from '@/components/ui/avatar';
import { 
  MessageCircle, 
  Plus, 
  Edit, 
  Trash2, 
  Check, 
  X, 
  Reply,
  AlertCircle,
  CheckCircle,
  Star,
  Eye,
  Users,
  Clock
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
// Remove the import as we'll use fetch directly

interface Annotation {
  id: string;
  content: string;
  type: 'comment' | 'highlight' | 'note' | 'question' | 'approval' | 'concern';
  entityType: string;
  entityId: string;
  authorId: string;
  authorName: string;
  authorEmail: string;
  parentId?: string;
  position?: {
    x?: number;
    y?: number;
    selector?: string;
    selectedText?: string;
  };
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: string;
  mentions?: string[];
  attachments?: string[];
  createdAt: string;
  updatedAt: string;
  replies: Annotation[];
}

interface CollaborationAnnotationsProps {
  entityType: string;
  entityId: string;
  showPositioned?: boolean;
}

interface WebSocketMessage {
  type: string;
  payload: any;
  entityType: string;
  entityId: string;
}

export default function CollaborationAnnotations({ 
  entityType, 
  entityId, 
  showPositioned = false 
}: CollaborationAnnotationsProps) {
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [selectedType, setSelectedType] = useState<Annotation['type']>('comment');
  const [activeUsers, setActiveUsers] = useState<any[]>([]);
  const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
  
  const wsRef = useRef<WebSocket | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch annotations using the standard API pattern
  const { data: fetchedAnnotations, isLoading } = useQuery({
    queryKey: [`/api/annotations`, { entityType, entityId }],
    queryFn: async () => {
      const response = await fetch(`/api/annotations?entityType=${entityType}&entityId=${entityId}`, {
        credentials: 'include', // Include session cookies
      });
      if (!response.ok) return [];
      return response.json();
    },
    refetchInterval: 30000, // Fallback polling
  });

  // Create annotation mutation
  const createAnnotationMutation = useMutation({
    mutationFn: async (annotationData: any) => {
      const response = await fetch('/api/annotations', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include session cookies
        body: JSON.stringify(annotationData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create annotation');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/annotations`, { entityType, entityId }] });
      setNewComment('');
      setSelectedType('comment');
    },
    onError: (error) => {
      console.error('Annotation creation error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create annotation",
        variant: "destructive"
      });
    }
  });

  // Update annotation mutation
  const updateAnnotationMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await fetch(`/api/annotations/${id}`, {
        method: 'PUT',
        headers: { 
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include session cookies
        body: JSON.stringify(data),
      });
      if (!response.ok) throw new Error('Failed to update annotation');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/annotations`, { entityType, entityId }] });
      setEditingId(null);
      setEditContent('');
    },
  });

  // Delete annotation mutation
  const deleteAnnotationMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/annotations/${id}`, {
        method: 'DELETE',
        credentials: 'include', // Include session cookies
      });
      if (!response.ok) throw new Error('Failed to delete annotation');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/annotations`, { entityType, entityId }] });
    },
  });

  // Initialize WebSocket connection
  useEffect(() => {
    if (!user) return;

    const token = localStorage.getItem('auth_token');
    if (!token) return;

    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/collaboration?token=${token}&entityType=${entityType}&entityId=${entityId}`;
    
    const ws = new WebSocket(wsUrl);
    wsRef.current = ws;

    ws.onopen = () => {
      console.log('Collaboration WebSocket connected');
    };

    ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data);
        handleWebSocketMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onclose = () => {
      console.log('Collaboration WebSocket disconnected');
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, [user, entityType, entityId]);

  const handleWebSocketMessage = (message: WebSocketMessage) => {
    switch (message.type) {
      case 'annotation_created':
      case 'annotation_updated':
      case 'annotation_deleted':
        // Refresh annotations
        queryClient.invalidateQueries({ queryKey: [`/api/annotations/${entityType}/${entityId}`] });
        break;
      
      case 'user_joined':
        setActiveUsers(prev => [...prev.filter(u => u.userId !== message.payload.userId), message.payload]);
        break;
      
      case 'user_left':
        setActiveUsers(prev => prev.filter(u => u.userId !== message.payload.userId));
        break;
      
      case 'typing_started':
        setTypingUsers(prev => new Set(prev).add(message.payload.userId));
        break;
      
      case 'typing_stopped':
        setTypingUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(message.payload.userId);
          return newSet;
        });
        break;
    }
  };

  useEffect(() => {
    if (fetchedAnnotations) {
      setAnnotations(fetchedAnnotations);
    }
  }, [fetchedAnnotations]);

  const handleCreateAnnotation = async () => {
    if (!newComment.trim()) {
      toast({
        title: "Error",
        description: "Please enter annotation content",
        variant: "destructive"
      });
      return;
    }

    if (!user?.id) {
      toast({
        title: "Error", 
        description: "You must be logged in to add annotations",
        variant: "destructive"
      });
      return;
    }

    createAnnotationMutation.mutate({
      content: newComment,
      type: selectedType,
      entityType,
      entityId,
    });
  };

  const handleReply = async (parentId: string) => {
    if (!replyContent.trim()) return;

    createAnnotationMutation.mutate({
      content: replyContent,
      type: 'comment',
      entityType,
      entityId,
      parentId,
    });

    setReplyingTo(null);
    setReplyContent('');
  };

  const handleEdit = async (id: string) => {
    if (!editContent.trim()) return;

    updateAnnotationMutation.mutate({
      id,
      data: { content: editContent }
    });
  };

  const handleResolve = async (id: string, isResolved: boolean) => {
    updateAnnotationMutation.mutate({
      id,
      data: { isResolved }
    });
  };

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this annotation?')) {
      deleteAnnotationMutation.mutate(id);
    }
  };

  const getTypeIcon = (type: Annotation['type']) => {
    switch (type) {
      case 'question': return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'approval': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'concern': return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'note': return <Star className="w-4 h-4 text-blue-500" />;
      case 'highlight': return <Eye className="w-4 h-4 text-purple-500" />;
      default: return <MessageCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getTypeColor = (type: Annotation['type']) => {
    switch (type) {
      case 'question': return 'bg-yellow-100 text-yellow-800';
      case 'approval': return 'bg-green-100 text-green-800';
      case 'concern': return 'bg-red-100 text-red-800';
      case 'note': return 'bg-blue-100 text-blue-800';
      case 'highlight': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-32">
          <div className="animate-spin w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Active Collaborators */}
      {activeUsers.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-green-500" />
              <span className="text-sm text-gray-600">
                {activeUsers.length} user{activeUsers.length !== 1 ? 's' : ''} viewing
              </span>
              <div className="flex -space-x-2 ml-2">
                {activeUsers.slice(0, 3).map(user => (
                  <Avatar key={user.userId} className="w-6 h-6 border-2 border-white">
                    <AvatarFallback className="text-xs">
                      {user.userName?.charAt(0) || 'U'}
                    </AvatarFallback>
                  </Avatar>
                ))}
                {activeUsers.length > 3 && (
                  <div className="w-6 h-6 rounded-full bg-gray-100 border-2 border-white flex items-center justify-center text-xs">
                    +{activeUsers.length - 3}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* New Annotation Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            Add Annotation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2 flex-wrap">
            {(['comment', 'note', 'question', 'approval', 'concern'] as const).map(type => (
              <Button
                key={type}
                variant={selectedType === type ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedType(type)}
                className="flex items-center gap-1"
              >
                {getTypeIcon(type)}
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Button>
            ))}
          </div>
          <Textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="Add your annotation..."
            className="min-h-[80px]"
          />
          <div className="flex gap-2">
            <Button 
              onClick={handleCreateAnnotation}
              disabled={!newComment.trim() || createAnnotationMutation.isPending}
              className="flex items-center gap-2 flex-1"
            >
              <Plus className="w-4 h-4" />
              Add Annotation
            </Button>
            
            {/* Demo button for testing */}
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  const response = await fetch('/api/annotations/demo', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                  });
                  const result = await response.json();
                  toast({
                    title: "Demo Success",
                    description: result.message,
                    variant: "default"
                  });
                  console.log('Demo annotation result:', result);
                } catch (error) {
                  toast({
                    title: "Demo Failed", 
                    description: "Could not create demo annotation",
                    variant: "destructive"
                  });
                }
              }}
            >
              Test Demo
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Annotations List */}
      <div className="space-y-4">
        {annotations.map(annotation => (
          <Card key={annotation.id} className={`${annotation.isResolved ? 'opacity-75' : ''}`}>
            <CardContent className="p-4">
              <div className="space-y-3">
                {/* Annotation Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Avatar className="w-8 h-8">
                      <AvatarFallback>
                        {annotation.authorName?.charAt(0) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-sm">{annotation.authorName}</span>
                        <Badge className={getTypeColor(annotation.type)}>
                          <span className="flex items-center gap-1">
                            {getTypeIcon(annotation.type)}
                            {annotation.type}
                          </span>
                        </Badge>
                        {annotation.isResolved && (
                          <Badge variant="outline" className="text-green-600">
                            <Check className="w-3 h-3 mr-1" />
                            Resolved
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-1 text-xs text-gray-500">
                        <Clock className="w-3 h-3" />
                        {new Date(annotation.createdAt).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex items-center gap-1">
                    {!annotation.isResolved && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleResolve(annotation.id, true)}
                        className="h-8 w-8 p-0"
                      >
                        <Check className="w-4 h-4" />
                      </Button>
                    )}
                    {annotation.isResolved && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleResolve(annotation.id, false)}
                        className="h-8 w-8 p-0"
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    )}
                    {annotation.authorId === user?.id && (
                      <>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => {
                            setEditingId(annotation.id);
                            setEditContent(annotation.content);
                          }}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => handleDelete(annotation.id)}
                          className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {/* Annotation Content */}
                {editingId === annotation.id ? (
                  <div className="space-y-2">
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="min-h-[60px]"
                    />
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleEdit(annotation.id)}
                        disabled={!editContent.trim()}
                      >
                        Save
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setEditingId(null);
                          setEditContent('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-sm whitespace-pre-wrap">{annotation.content}</div>
                )}

                {/* Reply Button */}
                {!annotation.isResolved && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setReplyingTo(annotation.id)}
                    className="flex items-center gap-1 text-xs"
                  >
                    <Reply className="w-3 h-3" />
                    Reply
                  </Button>
                )}

                {/* Reply Form */}
                {replyingTo === annotation.id && (
                  <div className="space-y-2 mt-3 p-3 bg-gray-50 rounded-lg">
                    <Textarea
                      value={replyContent}
                      onChange={(e) => setReplyContent(e.target.value)}
                      placeholder="Write a reply..."
                      className="min-h-[60px] bg-white"
                    />
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => handleReply(annotation.id)}
                        disabled={!replyContent.trim()}
                      >
                        Reply
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setReplyingTo(null);
                          setReplyContent('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}

                {/* Replies */}
                {annotation.replies.length > 0 && (
                  <div className="space-y-2 mt-3 pl-6 border-l-2 border-gray-200">
                    {annotation.replies.map(reply => (
                      <div key={reply.id} className="space-y-2 p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <Avatar className="w-6 h-6">
                            <AvatarFallback className="text-xs">
                              {reply.authorName?.charAt(0) || 'U'}
                            </AvatarFallback>
                          </Avatar>
                          <span className="font-medium text-sm">{reply.authorName}</span>
                          <span className="text-xs text-gray-500">
                            {new Date(reply.createdAt).toLocaleString()}
                          </span>
                        </div>
                        <div className="text-sm">{reply.content}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Typing Indicators */}
      {typingUsers.size > 0 && (
        <div className="text-sm text-gray-500 italic">
          {Array.from(typingUsers).slice(0, 2).join(', ')} 
          {typingUsers.size > 2 && ` and ${typingUsers.size - 2} other${typingUsers.size > 3 ? 's' : ''}`}
          {' is typing...'}
        </div>
      )}

      {annotations.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No annotations yet. Be the first to add one!</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}