import React from 'react';

interface AuthLayoutProps {
  children: React.ReactNode;
}

/**
 * AuthLayout Component
 * 
 * Reusable layout for all authentication pages (Login, Signup, Create Organization, Book Demo)
 * Features:
 * - Two-column layout with distinct background colors
 * - Left: Light purple/blue background with form content
 * - Right: White background with lady image and feature list
 * - Responsive design that hides right side on mobile
 * - No vertical scrollbar - content fits within viewport
 */
export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div
      className="min-h-screen flex"
      style={{
        background: 'linear-gradient(135deg, #FFF5F0 0%, #FFE8E0 30%, #F0E8FF 70%, #E8F0FF 100%)'
      }}
    >
      {/* Left Side - Form Content */}
      <div
        className="w-full lg:w-1/2 flex items-center justify-center px-6 py-8 lg:py-12"
      >
        <div className="w-full max-w-md">
          {children}
        </div>
      </div>

      {/* Right Side - Visual on Gradient Background */}
      <div className="hidden lg:flex lg:w-1/2 items-center justify-center p-12 relative">
        <div className="flex flex-col items-center justify-center space-y-16 max-w-md">
          {/* Image Section with Radiating Lines */}
          <div className="relative flex items-center justify-center">
            {/* Decorative Lines - Radiating from center using Line SVG files */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <img src="/Login/Line 576.svg" alt="" className="absolute" style={{ top: '10%', left: '20%' }} />
              <img src="/Login/Line 577.svg" alt="" className="absolute" style={{ top: '8%', left: '25%' }} />
              <img src="/Login/Line 578.svg" alt="" className="absolute" style={{ top: '6%', left: '30%' }} />
              <img src="/Login/Line 579.svg" alt="" className="absolute" style={{ top: '4%', left: '35%' }} />
              <img src="/Login/Line 580.svg" alt="" className="absolute" style={{ top: '2%', left: '40%' }} />
              <img src="/Login/Line 581.svg" alt="" className="absolute" style={{ top: '0%', left: '45%' }} />
              <img src="/Login/Line 582.svg" alt="" className="absolute" style={{ top: '0%', left: '50%' }} />
              <img src="/Login/Line 583.svg" alt="" className="absolute" style={{ top: '0%', left: '55%' }} />
              <img src="/Login/Line 584.svg" alt="" className="absolute" style={{ top: '2%', left: '60%' }} />
              <img src="/Login/Line 585.svg" alt="" className="absolute" style={{ top: '4%', left: '65%' }} />
              <img src="/Login/Line 586.svg" alt="" className="absolute" style={{ top: '6%', left: '70%' }} />
              <img src="/Login/Line 587.svg" alt="" className="absolute" style={{ top: '8%', left: '75%' }} />
              <img src="/Login/Line 588.svg" alt="" className="absolute" style={{ top: '10%', left: '80%' }} />
              <img src="/Login/Line 589.svg" alt="" className="absolute" style={{ top: '15%', left: '85%' }} />
              <img src="/Login/Line 590.svg" alt="" className="absolute" style={{ top: '20%', left: '88%' }} />
              <img src="/Login/Line 591.svg" alt="" className="absolute" style={{ top: '80%', left: '20%' }} />
              <img src="/Login/Line 592.svg" alt="" className="absolute" style={{ top: '85%', left: '50%' }} />
              <img src="/Login/Line 593.svg" alt="" className="absolute" style={{ top: '80%', left: '80%' }} />
            </div>

            {/* Circular Image with Gradient Background */}
            <div className="relative z-10">
              {/* Circular Gradient Background */}
              <div
                className="absolute inset-0 rounded-full blur-3xl opacity-50"
                style={{
                  background: 'radial-gradient(circle, rgba(114, 190, 244, 0.6) 0%, rgba(120, 86, 255, 0.4) 50%, transparent 70%)',
                  width: '350px',
                  height: '350px',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)'
                }}
              />

              {/* Circular Frame - Ellipse 897.svg */}
              <div className="relative flex items-center justify-center">
                <div className="absolute inset-0 flex items-center justify-center">
                  <img src="/Login/Ellipse 897.svg" alt="" className="w-80 h-80" />
                </div>

                {/* Center Image */}
                <div className="relative z-20">
                  <img
                    src="/Login/image 210316.png"
                    alt="AI Assistant"
                    className="w-64 h-64 rounded-full object-cover"
                    style={{
                      border: '6px solid white',
                      boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2)'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Feature List - Below Image */}
          <div className="w-full space-y-3">
            <div className="flex items-center gap-3 bg-white/95 backdrop-blur-sm px-4 py-3 rounded-xl shadow-md">
              <img src="/Login/Group 1618876033.svg" alt="" className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm font-medium text-[#151518]">Automated resume screening</span>
            </div>
            <div className="flex items-center gap-3 bg-white/95 backdrop-blur-sm px-4 py-3 rounded-xl shadow-md">
              <img src="/Login/Group 1618876033.svg" alt="" className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm font-medium text-[#151518]">Human in the loop for approval</span>
            </div>
            <div className="flex items-center gap-3 bg-white/95 backdrop-blur-sm px-4 py-3 rounded-xl shadow-md">
              <img src="/Login/Group 1618876033.svg" alt="" className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm font-medium text-[#151518]">Email communication with candidate</span>
            </div>
            <div className="flex items-center gap-3 bg-white/95 backdrop-blur-sm px-4 py-3 rounded-xl shadow-md">
              <img src="/Login/Group 1618876033.svg" alt="" className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm font-medium text-[#151518]">Automated Interview scheduling</span>
            </div>
            <div className="flex items-center gap-3 bg-white/95 backdrop-blur-sm px-4 py-3 rounded-xl shadow-md">
              <img src="/Login/Group 1618876033.svg" alt="" className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm font-medium text-[#151518]">Voice AI initial interview screening</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

