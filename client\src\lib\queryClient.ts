// Enhanced query client with automatic token refresh
import { QueryClient } from '@tanstack/react-query';
import { authAPI } from './authApi';

// Create a custom apiRequest function that uses the authAPI for authenticated requests
export async function apiRequest(url: string, options: RequestInit = {}): Promise<any> {
  try {
    const response = await authAPI.makeAuthenticatedRequest(url, options);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || errorData.message || `HTTP ${response.status}`);
    }
    
    return response.json();
  } catch (error) {
    console.error('API Request failed:', error);
    throw error;
  }
}

// Default fetch for non-authenticated requests
async function defaultQueryFn({ queryKey }: { queryKey: readonly unknown[] }): Promise<any> {
  const url = Array.isArray(queryKey) ? String(queryKey[0]) : String(queryKey);
  
  try {
    const response = await fetch(url, {
      credentials: 'include',
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`);
    }
    
    return response.json();
  } catch (error) {
    console.error('Query failed:', error);
    throw error;
  }
}

// Enhanced QueryClient with automatic token refresh support
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: defaultQueryFn,
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error) => {
        // Don't retry 401 errors (will be handled by auth interceptor)
        if (error.message?.includes('401')) {
          return false;
        }
        // Retry up to 2 times for other errors
        return failureCount < 2;
      },
    },
    mutations: {
      retry: false, // Don't retry mutations by default
    },
  },
});

// Export utility for making authenticated queries
export function createAuthenticatedQuery(url: string) {
  return {
    queryKey: [url],
    queryFn: () => apiRequest(url),
  };
}

// Export utility for making authenticated mutations
export function createAuthenticatedMutation(
  url: string, 
  method: 'POST' | 'PUT' | 'PATCH' | 'DELETE' = 'POST'
) {
  return {
    mutationFn: (data?: any) => apiRequest(url, {
      method,
      headers: {
        'Content-Type': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
    }),
  };
}