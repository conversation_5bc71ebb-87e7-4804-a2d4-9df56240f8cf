import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  UserPlus, 
  Shield, 
  Building2, 
  Mail, 
  Phone, 
  Calendar,
  Filter,
  Search,
  Settings,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Clock,
  MoreHorizontal,
  Copy,
  Share2,
  Link
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface User {
  id: string;
  email: string;
  full_name: string;
  role: 'admin' | 'member' | 'viewer';
  organization_id: string;
  is_active: boolean;
  is_approved: boolean;
  created_at: string;
  updated_at?: string;
  last_login?: string;
  profile_image?: string;
  department?: string;
  phone?: string;
}

interface Organization {
  id: string;
  name: string;
  domain?: string;
  is_active: boolean;
  created_at: string;
  user_count: number;
}

interface UserInvitation {
  id: string;
  email: string;
  full_name: string;
  role: string;
  created_at: string;
  expires_at: string;
  invited_by: string;
}

interface OrganizationShareInfo {
  organizationId: string;
  organizationName: string;
  domain?: string;
  registrationUrl: string;
  instructions: string;
  createdAt: string;
}

const getRoleColor = (role: string) => {
  switch (role) {
    case 'admin':
      return 'bg-red-100 text-red-800';
    case 'member':
      return 'bg-blue-100 text-blue-800';
    case 'viewer':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusColor = (isActive: boolean, isApproved: boolean) => {
  if (!isActive) return 'bg-gray-100 text-gray-800';
  if (!isApproved) return 'bg-yellow-100 text-yellow-800';
  return 'bg-green-100 text-green-800';
};

const getStatusText = (isActive: boolean, isApproved: boolean) => {
  if (!isActive) return 'Inactive';
  if (!isApproved) return 'Pending';
  return 'Active';
};

const getInitials = (name: string) => {
  return name.split(' ').map(n => n[0]).join('').toUpperCase();
};

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [invitations, setInvitations] = useState<UserInvitation[]>([]);
  const [shareInfo, setShareInfo] = useState<OrganizationShareInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [activeTab, setActiveTab] = useState('users');
  
  const [inviteForm, setInviteForm] = useState({
    email: '',
    full_name: '',
    role: 'member',
    department: ''
  });

  const { toast } = useToast();
  const { user: currentUser } = useAuth();

  // Fetch organization data
  const fetchOrganization = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/organizations/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setOrganization(data);
      }
    } catch (error) {
      console.error('Error fetching organization:', error);
    }
  };

  // Fetch users
  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  // Fetch invitations
  const fetchInvitations = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/users/invitations', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setInvitations(data);
      }
    } catch (error) {
      console.error('Error fetching invitations:', error);
    }
  };

  // Fetch organization share info
  const fetchShareInfo = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/organizations/share-info', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setShareInfo(data);
      }
    } catch (error) {
      console.error('Error fetching share info:', error);
    }
  };

  // Copy to clipboard function
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive"
      });
    }
  };

  // Invite user
  const handleInviteUser = async () => {
    if (!inviteForm.email || !inviteForm.full_name) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/users/invite', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(inviteForm)
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "User invitation sent successfully"
        });
        setShowInviteModal(false);
        setInviteForm({ email: '', full_name: '', role: 'member', department: '' });
        fetchInvitations();
      } else {
        const error = await response.json();
        toast({
          title: "Error",
          description: error.detail || "Failed to send invitation",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send invitation",
        variant: "destructive"
      });
    }
  };

  // Update user
  const handleUpdateUser = async (userId: string, updates: Partial<User>) => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "User updated successfully"
        });
        fetchUsers();
      } else {
        const error = await response.json();
        toast({
          title: "Error",
          description: error.detail || "Failed to update user",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user",
        variant: "destructive"
      });
    }
  };

  // Approve user
  const handleApproveUser = async (userId: string) => {
    await handleUpdateUser(userId, { is_approved: true });
  };

  // Deactivate user
  const handleDeactivateUser = async (userId: string) => {
    await handleUpdateUser(userId, { is_active: false });
  };

  // Filter users
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && user.is_active && user.is_approved) ||
                         (statusFilter === 'pending' && !user.is_approved) ||
                         (statusFilter === 'inactive' && !user.is_active);
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchOrganization(), fetchUsers(), fetchInvitations(), fetchShareInfo()]);
      setLoading(false);
    };
    
    loadData();
  }, []);

  const canManageUsers = currentUser?.role === 'admin';

  return (
    <div
      className="h-screen overflow-y-auto"
      style={{ backgroundColor: '#FBFAFF' }}
    >
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold" style={{ color: '#0152FF' }}>User Management</h1>
            <p className="text-gray-600 mt-2">
              Manage users and permissions for {organization?.name || 'your organization'}
            </p>
          </div>

          {canManageUsers && (
            <Dialog open={showInviteModal} onOpenChange={setShowInviteModal}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  className="flex items-center gap-2 hover:bg-blue-50"
                  style={{ borderColor: '#0152FF', color: '#0152FF' }}
                >
                  <UserPlus className="w-4 h-4" />
                  Invite User
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Invite New User</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={inviteForm.email}
                      onChange={(e) => setInviteForm({...inviteForm, email: e.target.value})}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="full_name">Full Name *</Label>
                    <Input
                      id="full_name"
                      value={inviteForm.full_name}
                      onChange={(e) => setInviteForm({...inviteForm, full_name: e.target.value})}
                      placeholder="John Doe"
                    />
                  </div>
                  <div>
                    <Label htmlFor="role">Role</Label>
                    <Select value={inviteForm.role} onValueChange={(value) => setInviteForm({...inviteForm, role: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="viewer">Viewer</SelectItem>
                        <SelectItem value="member">Member</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="department">Department</Label>
                    <Input
                      id="department"
                      value={inviteForm.department}
                      onChange={(e) => setInviteForm({...inviteForm, department: e.target.value})}
                      placeholder="Engineering"
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowInviteModal(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleInviteUser}>
                      Send Invitation
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </div>

        {/* Organization Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold">{users.length}</p>
                </div>
                <Users className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Active Users</p>
                  <p className="text-2xl font-bold">
                    {users.filter(u => u.is_active && u.is_approved).length}
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Pending Approval</p>
                  <p className="text-2xl font-bold">
                    {users.filter(u => !u.is_approved).length}
                  </p>
                </div>
                <Clock className="w-8 h-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Administrators</p>
                  <p className="text-2xl font-bold">
                    {users.filter(u => u.role === 'admin').length}
                  </p>
                </div>
                <Shield className="w-8 h-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="users">Users ({users.length})</TabsTrigger>
            <TabsTrigger value="invitations">Invitations ({invitations.length})</TabsTrigger>
            <TabsTrigger value="organization">Organization</TabsTrigger>
          </TabsList>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-9"
                      />
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Select value={roleFilter} onValueChange={setRoleFilter}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Roles</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="member">Member</SelectItem>
                        <SelectItem value="viewer">Viewer</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Users Table */}
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Joined</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-10 h-10">
                              <AvatarImage src={user.profile_image} />
                              <AvatarFallback>{getInitials(user.full_name)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-medium">{user.full_name}</p>
                              <p className="text-sm text-gray-500">{user.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getRoleColor(user.role)}>
                            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(user.is_active, user.is_approved)}>
                            {getStatusText(user.is_active, user.is_approved)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-600">
                            {user.department || 'Not specified'}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-600">
                            {new Date(user.created_at).toLocaleDateString()}
                          </span>
                        </TableCell>
                        <TableCell>
                          {canManageUsers && (
                            <div className="flex items-center space-x-2">
                              {!user.is_approved && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleApproveUser(user.id)}
                                  className="text-green-600 hover:text-green-700"
                                >
                                  <CheckCircle className="w-4 h-4" />
                                </Button>
                              )}
                              {user.is_active && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleDeactivateUser(user.id)}
                                  className="text-red-600 hover:text-red-700"
                                >
                                  <XCircle className="w-4 h-4" />
                                </Button>
                              )}
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Invitations Tab */}
          <TabsContent value="invitations" className="space-y-6">
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Email</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Invited</TableHead>
                      <TableHead>Expires</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {invitations.map((invitation) => (
                      <TableRow key={invitation.id}>
                        <TableCell className="font-medium">{invitation.email}</TableCell>
                        <TableCell>{invitation.full_name}</TableCell>
                        <TableCell>
                          <Badge className={getRoleColor(invitation.role)}>
                            {invitation.role.charAt(0).toUpperCase() + invitation.role.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {new Date(invitation.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          {new Date(invitation.expires_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Badge className="bg-yellow-100 text-yellow-800">
                            Pending
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Organization Tab */}
          <TabsContent value="organization" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Organization Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Organization Name</Label>
                  <p className="text-lg font-medium">{organization?.name}</p>
                </div>
                <div>
                  <Label>Domain</Label>
                  <p className="text-sm text-gray-600">{organization?.domain || 'Not configured'}</p>
                </div>
                <div>
                  <Label>Created</Label>
                  <p className="text-sm text-gray-600">
                    {organization?.created_at ? new Date(organization.created_at).toLocaleDateString() : 'Unknown'}
                  </p>
                </div>
                <div>
                  <Label>Total Users</Label>
                  <p className="text-sm text-gray-600">{organization?.user_count || 0}</p>
                </div>
              </CardContent>
            </Card>

            {/* Employee Access Card */}
            {shareInfo && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Share2 className="w-5 h-5" />
                    Employee Access
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Share these details with your employees to allow them to register and access the system
                  </p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Organization ID */}
                  <div className="space-y-2">
                    <Label className="font-medium">Organization ID</Label>
                    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
                      <code className="flex-1 text-sm font-mono text-gray-800">
                        {shareInfo.organizationId}
                      </code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(shareInfo.organizationId, 'Organization ID')}
                        className="flex items-center gap-1"
                      >
                        <Copy className="w-4 h-4" />
                        Copy
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">
                      Employees need this ID to register for your organization
                    </p>
                  </div>

                  {/* Registration URL */}
                  <div className="space-y-2">
                    <Label className="font-medium">Registration Link</Label>
                    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border">
                      <code className="flex-1 text-sm font-mono text-gray-800 truncate">
                        {shareInfo.registrationUrl}
                      </code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(shareInfo.registrationUrl, 'Registration link')}
                        className="flex items-center gap-1"
                      >
                        <Copy className="w-4 h-4" />
                        Copy
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500">
                      Direct link for employees to register with your organization ID pre-filled
                    </p>
                  </div>

                  {/* Instructions */}
                  <div className="space-y-2">
                    <Label className="font-medium">Instructions for Employees</Label>
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <p className="text-sm text-blue-800 leading-relaxed">
                        {shareInfo.instructions}
                      </p>
                      <div className="mt-3 pt-3 border-t border-blue-200">
                        <p className="text-xs text-blue-600">
                          <strong>Steps:</strong>
                        </p>
                        <ol className="text-xs text-blue-600 mt-1 ml-4 space-y-1">
                          <li>1. Visit the registration page</li>
                          <li>2. Enter the Organization ID above</li>
                          <li>3. Complete the registration form</li>
                          <li>4. Wait for admin approval</li>
                        </ol>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(shareInfo.instructions, 'Instructions')}
                        className="mt-3 flex items-center gap-1"
                      >
                        <Copy className="w-4 h-4" />
                        Copy Instructions
                      </Button>
                    </div>
                  </div>

                  {/* Email Template */}
                  <div className="space-y-2">
                    <Label className="font-medium">Email Template</Label>
                    <div className="p-4 bg-gray-50 rounded-lg border">
                      <div className="text-sm text-gray-700 space-y-2">
                        <p><strong>Subject:</strong> Access to {shareInfo.organizationName} ATS System</p>
                        <div className="space-y-2 text-sm">
                          <p>Hi [Employee Name],</p>
                          <p>You've been granted access to our ATS system. Please follow these steps to register:</p>
                          <p>1. Visit: <a href={shareInfo.registrationUrl} className="text-blue-600 underline">{shareInfo.registrationUrl}</a></p>
                          <p>2. Use Organization ID: <code className="bg-gray-200 px-1 rounded">{shareInfo.organizationId}</code></p>
                          <p>3. Complete your registration and wait for approval</p>
                          <p>Best regards,<br/>{organization?.name} HR Team</p>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(
                          `Subject: Access to ${shareInfo.organizationName} ATS System\n\nHi [Employee Name],\n\nYou've been granted access to our ATS system. Please follow these steps to register:\n\n1. Visit: ${shareInfo.registrationUrl}\n2. Use Organization ID: ${shareInfo.organizationId}\n3. Complete your registration and wait for approval\n\nBest regards,\n${organization?.name} HR Team`,
                          'Email template'
                        )}
                        className="mt-3 flex items-center gap-1"
                      >
                        <Copy className="w-4 h-4" />
                        Copy Email Template
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}