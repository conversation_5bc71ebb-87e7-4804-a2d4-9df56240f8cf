import { Router } from 'express';
import multer from 'multer';
import path from 'path';
import { authenticateToken } from '../auth';
import { transcriptionService } from '../services/transcriptionService';
import { db } from '../db';
import { interviewsV2, interviewArtifacts, authUsers } from '@shared/schema';
import { eq, and } from 'drizzle-orm';

const router = Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, path.join(process.cwd(), 'recordings'));
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `upload-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({ 
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024 // 100MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['.wav', '.mp3', '.m4a', '.ogg', '.flac'];
    const ext = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only audio files are allowed.'));
    }
  }
});

/**
 * @swagger
 * /transcription/upload:
 *   post:
 *     summary: Upload and transcribe audio file
 *     description: Upload an audio file and get transcription
 *     tags: [Transcription]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               audio:
 *                 type: string
 *                 format: binary
 *               interviewId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Transcription completed successfully
 *       400:
 *         description: Invalid file or request
 *       500:
 *         description: Transcription failed
 */
router.post('/upload', authenticateToken, upload.single('audio'), async (req, res) => {
  try {
    const { interviewId } = req.body;
    const userId = req.user?.id;

    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No audio file provided'
      });
    }

    if (!interviewId) {
      return res.status(400).json({
        success: false,
        error: 'Interview ID is required'
      });
    }

    // Verify user has access to this interview
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(and(
        eq(interviewsV2.id, interviewId),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!interview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Transcribe the audio
    const transcription = await transcriptionService.transcribeAudio(req.file.path);

    // Generate summary
    const summary = await transcriptionService.generateInterviewSummary(
      transcription,
      interview.role
    );

    // Store artifacts in database
    await db.insert(interviewArtifacts).values([
      {
        interviewId,
        organizationId: user.organizationId,
        type: 'recording',
        filePath: req.file.path,
        metadata: {
          originalName: req.file.originalname,
          size: req.file.size,
          duration: transcription.duration
        }
      },
      {
        interviewId,
        organizationId: user.organizationId,
        type: 'transcript',
        content: transcription.text,
        metadata: {
          language: transcription.language,
          segmentCount: transcription.segments?.length || 0
        }
      },
      {
        interviewId,
        organizationId: user.organizationId,
        type: 'summary',
        content: JSON.stringify(summary),
        metadata: {
          overallRating: summary.overallRating,
          competencyCount: Object.keys(summary.competencyScores).length
        }
      }
    ]);

    res.json({
      success: true,
      transcription,
      summary,
      message: 'Audio transcribed and analyzed successfully'
    });

  } catch (error) {
    console.error('Error processing audio upload:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process audio file'
    });
  }
});

/**
 * @swagger
 * /transcription/interview/{interviewId}:
 *   get:
 *     summary: Get interview transcription and analysis
 *     description: Retrieve transcription and analysis for a specific interview
 *     tags: [Transcription]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: interviewId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Transcription data retrieved successfully
 *       404:
 *         description: Interview or transcription not found
 */
router.get('/interview/:interviewId', authenticateToken, async (req, res) => {
  try {
    const { interviewId } = req.params;
    const userId = req.user?.id;

    // Verify user has access
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get interview artifacts
    const artifacts = await db
      .select()
      .from(interviewArtifacts)
      .where(and(
        eq(interviewArtifacts.interviewId, interviewId),
        eq(interviewArtifacts.organizationId, user.organizationId)
      ));

    if (artifacts.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No transcription data found for this interview'
      });
    }

    // Organize artifacts by type
    const result: any = {
      interviewId,
      artifacts: {}
    };

    for (const artifact of artifacts) {
      result.artifacts[artifact.type] = {
        id: artifact.id,
        content: artifact.content,
        filePath: artifact.filePath,
        metadata: artifact.metadata,
        createdAt: artifact.createdAt
      };
    }

    res.json({
      success: true,
      ...result
    });

  } catch (error) {
    console.error('Error retrieving transcription data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to retrieve transcription data'
    });
  }
});

/**
 * @swagger
 * /transcription/summary/{interviewId}:
 *   post:
 *     summary: Regenerate interview summary
 *     description: Regenerate summary and competency scores for an interview
 *     tags: [Transcription]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: interviewId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Summary regenerated successfully
 *       404:
 *         description: Interview or transcript not found
 */
router.post('/summary/:interviewId', authenticateToken, async (req, res) => {
  try {
    const { interviewId } = req.params;
    const userId = req.user?.id;

    // Verify user has access
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get interview and transcript
    const [interview] = await db
      .select()
      .from(interviewsV2)
      .where(and(
        eq(interviewsV2.id, interviewId),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!interview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    const [transcriptArtifact] = await db
      .select()
      .from(interviewArtifacts)
      .where(and(
        eq(interviewArtifacts.interviewId, interviewId),
        eq(interviewArtifacts.type, 'transcript'),
        eq(interviewArtifacts.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!transcriptArtifact || !transcriptArtifact.content) {
      return res.status(404).json({
        success: false,
        error: 'Transcript not found for this interview'
      });
    }

    // Generate new summary
    const transcription = {
      text: transcriptArtifact.content,
      language: transcriptArtifact.metadata?.language,
      duration: transcriptArtifact.metadata?.duration
    };

    const summary = await transcriptionService.generateInterviewSummary(
      transcription,
      interview.role
    );

    // Update or create summary artifact
    await db
      .insert(interviewArtifacts)
      .values({
        interviewId,
        organizationId: user.organizationId,
        type: 'summary',
        content: JSON.stringify(summary),
        metadata: {
          overallRating: summary.overallRating,
          competencyCount: Object.keys(summary.competencyScores).length,
          regenerated: true
        }
      })
      .onConflictDoUpdate({
        target: [interviewArtifacts.interviewId, interviewArtifacts.type],
        set: {
          content: JSON.stringify(summary),
          metadata: {
            overallRating: summary.overallRating,
            competencyCount: Object.keys(summary.competencyScores).length,
            regenerated: true
          },
          updatedAt: new Date()
        }
      });

    res.json({
      success: true,
      summary,
      message: 'Summary regenerated successfully'
    });

  } catch (error) {
    console.error('Error regenerating summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to regenerate summary'
    });
  }
});

/**
 * @swagger
 * /transcription/cleanup:
 *   post:
 *     summary: Clean up old recordings
 *     description: Remove old recording files to free up storage
 *     tags: [Transcription]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               maxAgeHours:
 *                 type: number
 *                 default: 24
 *     responses:
 *       200:
 *         description: Cleanup completed successfully
 */
router.post('/cleanup', authenticateToken, async (req, res) => {
  try {
    const { maxAgeHours = 24 } = req.body;
    
    await transcriptionService.cleanupOldRecordings(maxAgeHours);
    
    res.json({
      success: true,
      message: `Cleanup completed for recordings older than ${maxAgeHours} hours`
    });

  } catch (error) {
    console.error('Error during cleanup:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup recordings'
    });
  }
});

export default router;
