import { Router } from 'express';
import { db } from '../db';
import { organizations } from '@shared/schema';
import { ilike, or } from 'drizzle-orm';

const router = Router();

// Public endpoint to search organizations (for user registration)
router.get('/search', async (req, res) => {
  try {
    const { q } = req.query;
    
    if (!q || typeof q !== 'string' || q.trim().length < 2) {
      return res.status(400).json({ error: 'Search query must be at least 2 characters' });
    }

    const searchTerm = q.trim();
    
    // Search organizations by name or domain
    const results = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        domain: organizations.domain,
      })
      .from(organizations)
      .where(
        or(
          ilike(organizations.name, `%${searchTerm}%`),
          ilike(organizations.domain, `%${searchTerm}%`)
        )
      )
      .limit(10); // Limit results for performance

    res.json(results);
  } catch (error) {
    console.error('Error searching organizations:', error);
    res.status(500).json({ error: 'Failed to search organizations' });
  }
});

export default router;