import { SpeechClient } from '@google-cloud/speech';
import WebSocket from 'ws';

export interface GoogleSpeechConfig {
  encoding: 'MULAW' | 'LINEAR16';
  sampleRateHertz: number;
  languageCode: string;
  enableAutomaticPunctuation: boolean;
  model: string;
}

export interface TranscriptionResult {
  transcript: string;
  isFinal: boolean;
  confidence: number;
}

export class GoogleSpeechService {
  private speechClient: SpeechClient;
  private recognizeStream: any = null;
  private config: GoogleSpeechConfig;

  constructor() {
    // Initialize Google Speech client
    this.speechClient = new SpeechClient({
      // Credentials will be loaded from GOOGLE_APPLICATION_CREDENTIALS env var
      // or from service account key file
    });

    // Default configuration for Twilio phone calls
    this.config = {
      encoding: 'MULAW',
      sampleRateHertz: 8000,
      languageCode: 'en-US',
      enableAutomaticPunctuation: true,
      model: 'phone_call' // Optimized for phone audio
    };
  }

  /**
   * Start streaming recognition for a phone call
   */
  startRecognition(onTranscript: (result: TranscriptionResult) => void, onError: (error: Error) => void): void {
    try {
      const request = {
        config: {
          encoding: this.config.encoding,
          sampleRateHertz: this.config.sampleRateHertz,
          languageCode: this.config.languageCode,
          enableAutomaticPunctuation: this.config.enableAutomaticPunctuation,
          model: this.config.model,
          useEnhanced: true, // Use enhanced model for better accuracy
          enableSpeakerDiarization: false, // Disable for single speaker scenarios
          maxAlternatives: 1
        },
        interimResults: true, // Get partial results as user speaks
        enablePartialResults: true
      };

      this.recognizeStream = this.speechClient
        .streamingRecognize(request)
        .on('data', (data: any) => {
          if (data.results && data.results.length > 0) {
            const result = data.results[0];
            const transcript = result.alternatives[0].transcript;
            const confidence = result.alternatives[0].confidence || 0;
            const isFinal = result.isFinal || false;

            onTranscript({
              transcript,
              isFinal,
              confidence
            });
          }
        })
        .on('error', (error: Error) => {
          console.error('Google Speech recognition error:', error);
          onError(error);
        })
        .on('end', () => {
          console.log('Google Speech recognition stream ended');
        });

    } catch (error) {
      console.error('Error starting Google Speech recognition:', error);
      onError(error as Error);
    }
  }

  /**
   * Send audio data to Google Speech for recognition
   */
  sendAudio(audioBuffer: Buffer): void {
    if (this.recognizeStream && !this.recognizeStream.destroyed) {
      try {
        this.recognizeStream.write(audioBuffer);
      } catch (error) {
        console.error('Error writing audio to Google Speech stream:', error);
      }
    }
  }

  /**
   * Stop recognition and clean up resources
   */
  stopRecognition(): void {
    if (this.recognizeStream) {
      try {
        this.recognizeStream.end();
        this.recognizeStream = null;
      } catch (error) {
        console.error('Error stopping Google Speech recognition:', error);
      }
    }
  }

  /**
   * Check if recognition is active
   */
  isRecognitionActive(): boolean {
    return this.recognizeStream && !this.recognizeStream.destroyed;
  }

  /**
   * Test Google Speech API connectivity
   */
  async testConnection(): Promise<boolean> {
    try {
      // Test with a simple audio buffer
      const testAudio = Buffer.alloc(1024); // Small silence buffer
      
      const request = {
        config: {
          encoding: this.config.encoding,
          sampleRateHertz: this.config.sampleRateHertz,
          languageCode: this.config.languageCode,
        },
        audio: {
          content: testAudio.toString('base64')
        }
      };

      await this.speechClient.recognize(request);
      return true;
    } catch (error) {
      console.error('Google Speech connection test failed:', error);
      return false;
    }
  }
}

export const googleSpeechService = new GoogleSpeechService();