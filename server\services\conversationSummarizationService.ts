import OpenAI from "openai";
// the newest OpenAI model is "gpt-5" which was released August 7, 2025. do not change this unless explicitly requested by the user

const client = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// JSON schema for structured call summary response
const callSummarySchema = {
  name: "call_summary",
  schema: {
    type: "object",
    description: "Structured summary of an agent-candidate call.",
    properties: {
      summary: {
        type: "string",
        description: "Brief summary of the call (≤ 4 sentences)."
      },
      next_actions: {
        type: "object",
        description: "Key–value map of system-automatable actions. Keys are stable action codes.",
        additionalProperties: {
          type: "object",
          description: "Action payload.",
          properties: {
            description: {
              type: "string",
              description: "Human-readable description of the action."
            },
            due_time: {
              type: ["string", "null"],
              description: "Optional ISO-8601 datetime in UTC-4 (e.g., 2025-09-12T15:30:00-04:00). Null if not applicable."
            },
            assignee: {
              type: ["string", "null"],
              description: "Optional owner for this action (e.g., 'recruiter', 'hiring_manager', or a name/email)."
            },
            metadata: {
              type: ["object", "null"],
              description: "Optional extra fields your system may need.",
              additionalProperties: true
            }
          },
          required: ["description"],
          additionalProperties: false
        }
      },
      scheduled: {
        type: "boolean",
        description: "True if the agent and candidate explicitly agreed on a meeting/interview time."
      },
      scheduled_time: {
        type: ["string", "null"],
        description: "Agreed time in ISO-8601 with UTC-4 offset (e.g., 2025-09-12T15:30:00-04:00). Null if not agreed."
      },
      scheduled_evidence: {
        type: ["string", "null"],
        description: "Short quote (≤ 15 words) from the transcript confirming the scheduled time, or null."
      },
      open_questions: {
        type: "array",
        description: "Outstanding questions or info gaps (0–5).",
        items: { type: "string" }
      },
      risk_flags: {
        type: "array",
        description: "Potential risks/concerns (0–5). Empty if none.",
        items: { type: "string" }
      }
    },
    required: [
      "summary",
      "next_actions",
      "scheduled",
      "scheduled_time",
      "scheduled_evidence",
      "open_questions",
      "risk_flags"
    ],
    additionalProperties: false
  }
};

export interface CallSummaryResult {
  summary: string;
  next_actions: Record<string, {
    description: string;
    due_time?: string | null;
    assignee?: string | null;
    metadata?: Record<string, any> | null;
  }>;
  scheduled: boolean;
  scheduled_time: string | null;
  scheduled_evidence: string | null;
  open_questions: string[];
  risk_flags: string[];
}

export async function summarizeCall(transcript: string): Promise<CallSummaryResult> {
  const startTime = Date.now();
  
  try {
    console.log('📋 Starting call summarization with OpenAI GPT-3.5-turbo...');
    
    const system = `
You are an analyst. Read the call transcript and produce a JSON object that matches the provided schema exactly.
Rules:
- Use ONLY facts from the transcript.
- Keep "summary" ≤ 4 sentences.
- All date/times MUST be ISO-8601 with UTC-4 offset (e.g., 2025-09-12T15:30:00-04:00).
- If only a weekday/time is given, resolve to the next occurrence in UTC-4; if no year, assume current year.
- If no explicit agreement on timing, set scheduled=false and scheduled_time=null.
- "next_actions" MUST be a key–value map where keys are stable action codes (e.g., send_followup_email).
`;

    const resp = await client.chat.completions.create({
      model: "gpt-3.5-turbo", // Using GPT-3.5-turbo for reliable access
      messages: [
        { role: "system", content: system },
        { role: "user", content: `Transcript:\n${transcript}` }
      ],
      response_format: { type: "json_object" }
    });

    const processingTime = Date.now() - startTime;
    console.log(`✅ Call summarization completed in ${processingTime}ms`);

    const result = JSON.parse(resp.choices[0].message.content || '{}');
    
    // Validate the response has required fields
    if (!result.summary || !result.next_actions || typeof result.scheduled !== 'boolean') {
      throw new Error('Invalid response structure from OpenAI');
    }
    
    return result as CallSummaryResult;
    
  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error(`❌ Call summarization failed after ${processingTime}ms:`, error);
    throw new Error(`Failed to summarize call: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export async function summarizeCallAndSaveToDatabase(
  callId: string,
  organizationId: string,
  transcript: string
): Promise<CallSummaryResult | null> {
  try {
    console.log(`🔄 Processing call summary for call ${callId}...`);
    
    // Generate summary using OpenAI
    const summaryResult = await summarizeCall(transcript);
    
    // Import database dependencies dynamically to avoid circular imports
    const { db } = await import('../db');
    const { callSummaries } = await import('../../shared/schema');
    
    // Save to database
    const [savedSummary] = await db.insert(callSummaries).values({
      callId,
      organizationId,
      summary: summaryResult.summary,
      nextActions: summaryResult.next_actions,
      scheduled: summaryResult.scheduled,
      scheduledTime: summaryResult.scheduled_time ? new Date(summaryResult.scheduled_time) : null,
      scheduledEvidence: summaryResult.scheduled_evidence,
      openQuestions: summaryResult.open_questions,
      riskFlags: summaryResult.risk_flags,
      modelUsed: "gpt-3.5-turbo",
      processingTimeMs: null // Will be updated if needed
    }).returning();
    
    console.log(`✅ Call summary saved to database for call ${callId}`);
    return summaryResult;
    
  } catch (error) {
    console.error(`❌ Failed to process and save call summary for call ${callId}:`, error);
    return null;
  }
}

// Async function to be called after call ends - completely non-blocking
export function processCallSummaryAsync(
  callId: string,
  organizationId: string,
  transcript: string
): void {
  // Fire and forget - don't await this
  setTimeout(async () => {
    try {
      await summarizeCallAndSaveToDatabase(callId, organizationId, transcript);
    } catch (error) {
      console.error(`❌ Async call summary processing failed for call ${callId}:`, error);
    }
  }, 1000); // 1-second delay to ensure call is fully ended
}