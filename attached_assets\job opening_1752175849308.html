<html>
 <head>
  <title>
   Matches - Senior Product Designer
  </title>
  <script src="https://cdn.tailwindcss.com">
  </script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
  <style>
   body {
        font-family: 'Inter', sans-serif;
      }
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
  </style>
 </head>
 <body class="bg-[#FAFAFB] text-[#23262F]">
  <div class="flex min-h-screen">
   <!-- Sidebar -->
   <aside class="w-[270px] bg-white border-r border-[#F1F1F3] py-8 px-8">
    <div>
     <h1 class="text-3xl font-semibold mb-10">
      Matches
     </h1>
     <button class="flex items-center w-full border border-[#E6E8EC] rounded-lg px-4 py-2 text-base font-medium text-[#23262F] mb-8">
      <i class="fas fa-sliders-h mr-2 text-[#777E90]">
      </i>
      Filter
     </button>
     <div class="mb-6">
      <div class="text-xs font-semibold text-[#777E90] mb-2">
       Match Rating
      </div>
      <div class="flex flex-col gap-2">
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        90%-100%
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        70%-80%
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        50%-70%
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        &lt;50%
       </label>
      </div>
     </div>
     <div class="mb-6">
      <div class="text-xs font-semibold text-[#777E90] mb-2">
       License
      </div>
      <div class="flex flex-col gap-2">
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        Yes
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        No
       </label>
      </div>
     </div>
     <div class="mb-6">
      <div class="text-xs font-semibold text-[#777E90] mb-2">
       Experience
      </div>
      <div class="flex flex-col gap-2">
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        5-7 years
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        3-5 years
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        1-3 years
       </label>
      </div>
     </div>
     <div class="mb-6">
      <div class="text-xs font-semibold text-[#777E90] mb-2">
       Location
      </div>
      <div class="flex flex-col gap-2">
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        Toronto, Canada
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        Newyork, USA
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        Lead, UK
       </label>
      </div>
     </div>
     <div class="mb-6">
      <div class="text-xs font-semibold text-[#777E90] mb-2">
       Match Date
      </div>
      <div class="flex flex-col gap-2">
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        Today
       </label>
       <label class="flex items-center text-sm font-medium text-[#23262F]">
        <input class="mr-2 accent-[#23262F]" type="checkbox"/>
        This week
       </label>
      </div>
     </div>
    </div>
   </aside>
   <!-- Main Content -->
   <main class="flex-1 px-10 py-8">
    <div class="flex items-start justify-between">
     <div>
      <div class="flex items-center gap-2 mb-1">
       <h2 class="text-2xl font-semibold">
        Senior Product Designer
       </h2>
       <span class="flex items-center px-3 py-1 rounded-full border border-[#E6E8EC] text-xs font-medium text-[#7F56D9] bg-[#F4F3FF] ml-2">
        <span class="w-2 h-2 rounded-full bg-[#7F56D9] mr-2">
        </span>
        Open
       </span>
      </div>
      <div class="flex items-center text-[#777E90] text-sm font-medium mb-6">
       <i class="fas fa-map-marker-alt mr-2">
       </i>
       Canada
      </div>
     </div>
     <div class="flex gap-3">
      <button class="flex items-center px-5 py-2 border border-[#E6E8EC] rounded-lg text-[#23262F] text-base font-medium bg-white hover:bg-[#F4F3FF] transition">
       <i class="far fa-edit mr-2">
       </i>
       Edit Post
      </button>
      <button class="flex items-center px-5 py-2 border border-[#E6E8EC] rounded-lg text-[#23262F] text-base font-medium bg-white hover:bg-[#F4F3FF] transition">
       <i class="fas fa-share-alt mr-2">
       </i>
       Share
      </button>
     </div>
    </div>
    <!-- Job Details -->
    <div class="grid grid-cols-3 gap-6 bg-white rounded-2xl border border-[#F1F1F3] p-6 mt-6 mb-6">
     <div>
      <div class="text-xs font-semibold text-[#777E90] mb-1">
       Category
      </div>
      <div class="text-base font-medium text-[#23262F] mb-4">
       Product
      </div>
      <div class="text-xs font-semibold text-[#777E90] mb-1">
       Experience
      </div>
      <div class="text-base font-medium text-[#23262F]">
       3+ years
      </div>
     </div>
     <div>
      <div class="text-xs font-semibold text-[#777E90] mb-1">
       Availability
      </div>
      <div class="text-base font-medium text-[#23262F] mb-4">
       Full-time/part-time
      </div>
      <div class="text-xs font-semibold text-[#777E90] mb-1">
       Salary
      </div>
      <div class="text-base font-medium text-[#23262F]">
       $80K - $100K
      </div>
     </div>
     <div>
      <div class="text-xs font-semibold text-[#777E90] mb-1">
       Work Approach
      </div>
      <div class="text-base font-medium text-[#23262F] mb-4">
       Onsite
      </div>
      <div class="text-xs font-semibold text-[#777E90] mb-1">
       License
      </div>
      <div class="text-base font-medium text-[#23262F]">
       Required
      </div>
     </div>
    </div>
    <!-- Tabs -->
    <div class="flex items-center border-b border-[#E6E8EC] mb-2">
     <button class="px-4 py-2 text-[#7F56D9] font-semibold border-b-2 border-[#7F56D9] -mb-px">
      All Matches
     </button>
     <button class="px-4 py-2 text-[#777E90] font-medium">
      Screened
     </button>
     <button class="px-4 py-2 text-[#777E90] font-medium">
      Scheduled
     </button>
     <button class="px-4 py-2 text-[#777E90] font-medium">
      Hired
     </button>
     <button class="px-4 py-2 text-[#777E90] font-medium">
      Dismissed
     </button>
     <div class="flex-1">
     </div>
     <button class="flex items-center text-[#777E90] text-sm font-medium px-2">
      <i class="fas fa-th-list mr-2">
      </i>
      List View
     </button>
    </div>
    <!-- Table -->
    <div class="overflow-x-auto scrollbar-hide">
     <table class="min-w-full bg-white rounded-2xl border border-[#F1F1F3]">
      <thead>
       <tr class="text-[#777E90] text-xs font-semibold">
        <th class="text-left px-6 py-4">
         Candidate Name
        </th>
        <th class="text-left px-6 py-4">
         Matched
        </th>
        <th class="text-left px-6 py-4">
         Match date
        </th>
        <th class="text-left px-6 py-4">
         License
        </th>
        <th class="text-left px-6 py-4">
         Salary expectation
        </th>
        <th class="text-left px-6 py-4">
         Experience
        </th>
        <th class="px-6 py-4">
        </th>
       </tr>
      </thead>
      <tbody class="text-[#23262F] text-sm font-medium">
       <!-- Row 1 -->
       <tr class="border-t border-[#F1F1F3]">
        <td class="flex items-center gap-3 px-6 py-4">
         <img alt="Avatar of Marvin McKinney, illustrated, orange background" class="w-10 h-10 rounded-full object-cover" height="40" src="https://replicate.delivery/xezq/HrMoYQOQWj5TBVKdlfrEwcSQn4OmKcwz60gIksBMsVp5LufUA/out-0.png" width="40"/>
         Marvin McKinney
        </td>
        <td class="px-6 py-4">
         <div class="relative w-10 h-10 flex items-center justify-center">
          <svg class="absolute top-0 left-0" height="40" width="40">
           <circle cx="20" cy="20" fill="none" r="18" stroke="#E6E8EC" stroke-width="4">
           </circle>
           <circle cx="20" cy="20" fill="none" r="18" stroke="#58C27D" stroke-dasharray="113" stroke-dashoffset="0" stroke-linecap="round" stroke-width="4">
           </circle>
          </svg>
          <span class="relative text-[#58C27D] font-semibold text-base">
           85
          </span>
         </div>
        </td>
        <td class="px-6 py-4">
         12 May, 2023
        </td>
        <td class="px-6 py-4">
         Yes
        </td>
        <td class="px-6 py-4">
         $80K - $100K
        </td>
        <td class="px-6 py-4">
         3 years
        </td>
        <td class="px-6 py-4 text-right">
         <button class="text-[#777E90]">
          <i class="fas fa-chevron-down">
          </i>
         </button>
         <button class="ml-3 text-[#777E90]">
          <i class="fas fa-ellipsis-h">
          </i>
         </button>
        </td>
       </tr>
       <!-- Row 2 -->
       <tr class="border-t border-[#F1F1F3]">
        <td class="flex items-center gap-3 px-6 py-4">
         <img alt="Avatar of Savannah Nguyen, illustrated, blue background" class="w-10 h-10 rounded-full object-cover" height="40" src="https://replicate.delivery/xezq/JsmOxU5k3yJfMarU8IlZFcrhbNt8fqoC559H9wsnLyfnv4eTB/out-0.png" width="40"/>
         Savannah Nguyen
        </td>
        <td class="px-6 py-4">
         <div class="relative w-10 h-10 flex items-center justify-center">
          <svg class="absolute top-0 left-0" height="40" width="40">
           <circle cx="20" cy="20" fill="none" r="18" stroke="#E6E8EC" stroke-width="4">
           </circle>
           <circle cx="20" cy="20" fill="none" r="18" stroke="#58C27D" stroke-dasharray="113" stroke-dashoffset="0" stroke-linecap="round" stroke-width="4">
           </circle>
          </svg>
          <span class="relative text-[#58C27D] font-semibold text-base">
           85
          </span>
         </div>
        </td>
        <td class="px-6 py-4">
         12 May, 2023
        </td>
        <td class="px-6 py-4">
         Yes
        </td>
        <td class="px-6 py-4">
         $80K - $100K
        </td>
        <td class="px-6 py-4">
         3 years
        </td>
        <td class="px-6 py-4 text-right">
         <button class="text-[#777E90]">
          <i class="fas fa-chevron-down">
          </i>
         </button>
         <button class="ml-3 text-[#777E90]">
          <i class="fas fa-ellipsis-h">
          </i>
         </button>
        </td>
       </tr>
       <!-- Row 3 -->
       <tr class="border-t border-[#F1F1F3]">
        <td class="flex items-center gap-3 px-6 py-4">
         <img alt="Avatar of Kathryn Murphy, illustrated, dark green background" class="w-10 h-10 rounded-full object-cover" height="40" src="https://replicate.delivery/xezq/A9Lz31ytHraGKVJnN4eOooGe43gjtpKie4LF2KTNk9rnv4eTB/out-0.png" width="40"/>
         Kathryn Murphy
        </td>
        <td class="px-6 py-4">
         <div class="relative w-10 h-10 flex items-center justify-center">
          <svg class="absolute top-0 left-0" height="40" width="40">
           <circle cx="20" cy="20" fill="none" r="18" stroke="#E6E8EC" stroke-width="4">
           </circle>
           <circle cx="20" cy="20" fill="none" r="18" stroke="#F6C768" stroke-dasharray="99" stroke-dashoffset="0" stroke-linecap="round" stroke-width="4">
           </circle>
          </svg>
          <span class="relative text-[#F6C768] font-semibold text-base">
           75
          </span>
         </div>
        </td>
        <td class="px-6 py-4">
         12 May, 2023
        </td>
        <td class="px-6 py-4">
         Yes
        </td>
        <td class="px-6 py-4">
         $80K - $100K
        </td>
        <td class="px-6 py-4">
         3 years
        </td>
        <td class="px-6 py-4 text-right">
         <button class="text-[#777E90]">
          <i class="fas fa-chevron-down">
          </i>
         </button>
         <button class="ml-3 text-[#777E90]">
          <i class="fas fa-ellipsis-h">
          </i>
         </button>
        </td>
       </tr>
       <!-- Row 4 -->
       <tr class="border-t border-[#F1F1F3]">
        <td class="flex items-center gap-3 px-6 py-4">
         <img alt="Avatar of Robert Fox, illustrated, light yellow background" class="w-10 h-10 rounded-full object-cover" height="40" src="https://replicate.delivery/xezq/kevK5lwZcATVPC7n9UR30cinP9ZrBorPe47AoEpNqbLzXcfpA/out-0.png" width="40"/>
         Robert Fox
        </td>
        <td class="px-6 py-4">
         <div class="relative w-10 h-10 flex items-center justify-center">
          <svg class="absolute top-0 left-0" height="40" width="40">
           <circle cx="20" cy="20" fill="none" r="18" stroke="#E6E8EC" stroke-width="4">
           </circle>
           <circle cx="20" cy="20" fill="none" r="18" stroke="#F6C768" stroke-dasharray="92" stroke-dashoffset="0" stroke-linecap="round" stroke-width="4">
           </circle>
          </svg>
          <span class="relative text-[#F6C768] font-semibold text-base">
           70
          </span>
         </div>
        </td>
        <td class="px-6 py-4">
         12 May, 2023
        </td>
        <td class="px-6 py-4">
         No
        </td>
        <td class="px-6 py-4">
         $80K - $100K
        </td>
        <td class="px-6 py-4">
         3 years
        </td>
        <td class="px-6 py-4 text-right">
         <button class="text-[#777E90]">
          <i class="fas fa-chevron-down">
          </i>
         </button>
         <button class="ml-3 text-[#777E90]">
          <i class="fas fa-ellipsis-h">
          </i>
         </button>
        </td>
       </tr>
       <!-- Row 5 -->
       <tr class="border-t border-[#F1F1F3]">
        <td class="flex items-center gap-3 px-6 py-4">
         <img alt="Avatar of Jane Cooper, illustrated, red background" class="w-10 h-10 rounded-full object-cover" height="40" src="https://replicate.delivery/xezq/404CMBxOsF7oMNtnifU6b1dM8fwUYee07rLTwSLaTb8Nfi7nC/out-0.png" width="40"/>
         Jane Cooper
        </td>
        <td class="px-6 py-4">
         <div class="relative w-10 h-10 flex items-center justify-center">
          <svg class="absolute top-0 left-0" height="40" width="40">
           <circle cx="20" cy="20" fill="none" r="18" stroke="#E6E8EC" stroke-width="4">
           </circle>
           <circle cx="20" cy="20" fill="none" r="18" stroke="#58C27D" stroke-dasharray="113" stroke-dashoffset="0" stroke-linecap="round" stroke-width="4">
           </circle>
          </svg>
          <span class="relative text-[#58C27D] font-semibold text-base">
           85
          </span>
         </div>
        </td>
        <td class="px-6 py-4">
         12 May, 2023
        </td>
        <td class="px-6 py-4">
         No
        </td>
        <td class="px-6 py-4">
         $80K - $100K
        </td>
        <td class="px-6 py-4">
         3 years
        </td>
        <td class="px-6 py-4 text-right">
         <button class="text-[#777E90]">
          <i class="fas fa-chevron-down">
          </i>
         </button>
         <button class="ml-3 text-[#777E90]">
          <i class="fas fa-ellipsis-h">
          </i>
         </button>
        </td>
       </tr>
       <!-- Row 6 -->
       <tr class="border-t border-[#F1F1F3]">
        <td class="flex items-center gap-3 px-6 py-4">
         <img alt="Avatar of Ralph Edwards, illustrated, brown background" class="w-10 h-10 rounded-full object-cover" height="40" src="https://replicate.delivery/xezq/RSNu0i7tWFatNl8C8DIf0oPKi20anOeAHAzMx1J0KGqzXcfpA/out-0.png" width="40"/>
         Ralph Edwards
        </td>
        <td class="px-6 py-4">
         <div class="relative w-10 h-10 flex items-center justify-center">
          <svg class="absolute top-0 left-0" height="40" width="40">
           <circle cx="20" cy="20" fill="none" r="18" stroke="#E6E8EC" stroke-width="4">
           </circle>
           <circle cx="20" cy="20" fill="none" r="18" stroke="#58C27D" stroke-dasharray="113" stroke-dashoffset="0" stroke-linecap="round" stroke-width="4">
           </circle>
          </svg>
          <span class="relative text-[#58C27D] font-semibold text-base">
           85
          </span>
         </div>
        </td>
        <td class="px-6 py-4">
         12 May, 2023
        </td>
        <td class="px-6 py-4">
         Yes
        </td>
        <td class="px-6 py-4">
         $80K - $100K
        </td>
        <td class="px-6 py-4">
         3 years
        </td>
        <td class="px-6 py-4 text-right">
         <button class="text-[#777E90]">
          <i class="fas fa-chevron-down">
          </i>
         </button>
         <button class="ml-3 text-[#777E90]">
          <i class="fas fa-ellipsis-h">
          </i>
         </button>
        </td>
       </tr>
       <!-- Row 7 -->
       <tr class="border-t border-[#F1F1F3]">
        <td class="flex items-center gap-3 px-6 py-4">
         <img alt="Avatar of Arlene McCoy, illustrated, orange background" class="w-10 h-10 rounded-full object-cover" height="40" src="https://replicate.delivery/xezq/08WsFFewLKTIOq2KInU2ROJ9c3JbeEhhIz0RhjsMq5fmv4eTB/out-0.png" width="40"/>
         Arlene McCoy
        </td>
        <td class="px-6 py-4">
         <div class="relative w-10 h-10 flex items-center justify-center">
          <svg class="absolute top-0 left-0" height="40" width="40">
           <circle cx="20" cy="20" fill="none" r="18" stroke="#E6E8EC" stroke-width="4">
           </circle>
           <circle cx="20" cy="20" fill="none" r="18" stroke="#58C27D" stroke-dasharray="113" stroke-dashoffset="0" stroke-linecap="round" stroke-width="4">
           </circle>
          </svg>
          <span class="relative text-[#58C27D] font-semibold text-base">
           85
          </span>
         </div>
        </td>
        <td class="px-6 py-4">
         12 May, 2023
        </td>
        <td class="px-6 py-4">
         Yes
        </td>
        <td class="px-6 py-4">
         $80K - $100K
        </td>
        <td class="px-6 py-4">
         3 years
        </td>
        <td class="px-6 py-4 text-right">
         <button class="text-[#777E90]">
          <i class="fas fa-chevron-down">
          </i>
         </button>
         <button class="ml-3 text-[#777E90]">
          <i class="fas fa-ellipsis-h">
          </i>
         </button>
        </td>
       </tr>
       <!-- Row 8 -->
       <tr class="border-t border-[#F1F1F3]">
        <td class="flex items-center gap-3 px-6 py-4">
         <img alt="Avatar of Arlene McCoy, illustrated, orange background" class="w-10 h-10 rounded-full object-cover" height="40" src="https://replicate.delivery/xezq/08WsFFewLKTIOq2KInU2ROJ9c3JbeEhhIz0RhjsMq5fmv4eTB/out-0.png" width="40"/>
         Arlene McCoy
        </td>
        <td class="px-6 py-4">
         <div class="relative w-10 h-10 flex items-center justify-center">
          <svg class="absolute top-0 left-0" height="40" width="40">
           <circle cx="20" cy="20" fill="none" r="18" stroke="#E6E8EC" stroke-width="4">
           </circle>
           <circle cx="20" cy="20" fill="none" r="18" stroke="#58C27D" stroke-dasharray="113" stroke-dashoffset="0" stroke-linecap="round" stroke-width="4">
           </circle>
          </svg>
          <span class="relative text-[#58C27D] font-semibold text-base">
           85
          </span>
         </div>
        </td>
        <td class="px-6 py-4">
         12 May, 2023
        </td>
        <td class="px-6 py-4">
         Yes
        </td>
        <td class="px-6 py-4">
         $80K - $100K
        </td>
        <td class="px-6 py-4">
         3 years
        </td>
        <td class="px-6 py-4 text-right">
         <button class="text-[#777E90]">
          <i class="fas fa-chevron-down">
          </i>
         </button>
         <button class="ml-3 text-[#777E90]">
          <i class="fas fa-ellipsis-h">
          </i>
         </button>
        </td>
       </tr>
      </tbody>
     </table>
    </div>
   </main>
  </div>
 </body>
</html>