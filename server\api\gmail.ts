import { Router } from 'express';
import { gmailService } from '../services/gmailService';

const router = Router();

// Debug route to test if gmail routes are working
router.get('/test', (req, res) => {
  res.json({ message: 'Gmail router is working', timestamp: new Date().toISOString() });
});

/**
 * @swagger
 * /gmail/auth-status:
 *   get:
 *     summary: Check Gmail authentication status
 *     description: Verify if Gmail API is properly authenticated and ready for use
 *     tags: [Gmail]
 *     responses:
 *       200:
 *         description: Gmail authentication status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 authenticated:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 error:
 *                   type: string
 */
router.get('/auth-status', async (req, res) => {
  try {
    const isAuthenticated = await gmailService.isAuthenticated();
    res.json({ 
      authenticated: isAuthenticated,
      message: isAuthenticated ? 'Gmail is properly authenticated' : 'Gmail authentication required'
    });
  } catch (error) {
    console.error('Error checking Gmail auth status:', error);
    res.json({ authenticated: false, error: 'Failed to check authentication status' });
  }
});

// Refresh Gmail authentication token
router.post('/refresh-auth', async (req, res) => {
  try {
    const refreshResult = await gmailService.refreshAuth();
    if (refreshResult.success) {
      res.json({ 
        success: true, 
        message: 'Gmail authentication refreshed successfully' 
      });
    } else {
      res.json({ 
        success: false, 
        error: refreshResult.error || 'Failed to refresh authentication',
        requiresReauth: true
      });
    }
  } catch (error) {
    console.error('Error refreshing Gmail auth:', error);
    res.json({ 
      success: false, 
      error: 'Failed to refresh Gmail authentication',
      requiresReauth: true
    });
  }
});

// Get Gmail authorization URL
router.get('/auth-url', (req, res) => {
  try {
    const authUrl = gmailService.getAuthUrl();
    console.log('Generated auth URL with redirect:', authUrl);
    res.json({ authUrl });
  } catch (error) {
    console.error('Error getting auth URL:', error);
    res.status(500).json({ error: 'Failed to get authorization URL' });
  }
});

// Handle OAuth callback - GET route for OAuth redirect
router.get('/callback', async (req, res) => {
  try {
    console.log('OAuth callback received:', req.query);
    const { code, error } = req.query;
    
    if (error) {
      console.error('OAuth error:', error);
      return res.status(400).send(`
        <html>
          <body>
            <h2>Gmail Authentication Failed</h2>
            <p>Error: ${error}</p>
            <p><a href="/">Return to Home</a></p>
          </body>
        </html>
      `);
    }
    
    if (!code) {
      return res.status(400).send(`
        <html>
          <body>
            <h2>Gmail Authentication Failed</h2>
            <p>Authorization code not found</p>
            <p><a href="/">Return to Home</a></p>
          </body>
        </html>
      `);
    }

    console.log('Setting auth code...');
    await gmailService.setAuthCode(code as string);
    
    console.log('Starting Gmail monitoring...');
    gmailService.startMonitoring();
    
    res.send(`
      <html>
        <body style="font-family: Arial, sans-serif; text-align: center; padding: 50px;">
          <div style="max-width: 500px; margin: 0 auto;">
            <h2 style="color: #4CAF50;">Gmail Authentication Successful!</h2>
            <p>Your Gmail account has been connected successfully.</p>
            <p>The system will now monitor for candidate email responses.</p>
            <p><strong>You can close this window and return to the application.</strong></p>
            <script>
              setTimeout(() => {
                window.close();
              }, 5000);
            </script>
          </div>
        </body>
      </html>
    `);
  } catch (error) {
    console.error('Error in OAuth callback:', error);
    res.status(500).send(`
      <html>
        <body>
          <h2>Gmail Authentication Error</h2>
          <p>Failed to authenticate with Gmail: ${error.message}</p>
          <p><a href="/">Return to Home</a></p>
        </body>
      </html>
    `);
  }
});

// Handle OAuth callback via POST (backup)
router.post('/auth-callback', async (req, res) => {
  try {
    const { code } = req.body;
    
    if (!code) {
      return res.status(400).json({ error: 'Authorization code required' });
    }

    await gmailService.setAuthCode(code);
    
    // Start monitoring after successful auth
    gmailService.startMonitoring();
    
    res.json({ success: true, message: 'Gmail authorization successful' });
  } catch (error) {
    console.error('Error in auth callback:', error);
    res.status(500).json({ error: 'Failed to authorize Gmail access' });
  }
});

// Check Gmail authentication status
router.get('/status', (req, res) => {
  const isAuthenticated = gmailService.isAuthenticated();
  res.json({ 
    authenticated: isAuthenticated,
    message: isAuthenticated ? 'Gmail API connected' : 'Gmail API not authenticated'
  });
});

// Manually trigger email check
router.post('/check-emails', async (req, res) => {
  try {
    await gmailService.checkForEmailResponses();
    res.json({ success: true, message: 'Email check completed' });
  } catch (error) {
    console.error('Error checking emails:', error);
    res.status(500).json({ error: 'Failed to check emails' });
  }
});

export default router;