import React, { useState } from 'react';
import { Search, Building2, <PERSON><PERSON>, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Organization {
  id: string;
  name: string;
  domain: string;
}

export default function OrganizationLookup() {
  const [searchTerm, setSearchTerm] = useState('');
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [loading, setLoading] = useState(false);
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const { toast } = useToast();

  const searchOrganizations = async () => {
    if (!searchTerm.trim()) {
      toast({
        title: "Search Required",
        description: "Please enter an organization name or domain to search",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/organizations/search?q=${encodeURIComponent(searchTerm)}`);
      
      if (response.ok) {
        const data = await response.json();
        setOrganizations(data);
        
        if (data.length === 0) {
          toast({
            title: "No Results",
            description: "No organizations found matching your search"
          });
        }
      } else {
        toast({
          title: "Search Failed",
          description: "Unable to search organizations",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Network error while searching",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (orgId: string) => {
    try {
      await navigator.clipboard.writeText(orgId);
      setCopiedId(orgId);
      toast({
        title: "Copied!",
        description: "Organization ID copied to clipboard"
      });
      
      // Reset copy state after 2 seconds
      setTimeout(() => setCopiedId(null), 2000);
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Unable to copy to clipboard",
        variant: "destructive"
      });
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      searchOrganizations();
    }
  };

  return (
    <div className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-2xl p-6 shadow-xl">
      <div className="flex items-center gap-3 mb-4">
        <Building2 className="w-6 h-6 text-cyan-300" />
        <h3 className="text-lg font-semibold text-white">
          Find Your Organization ID
        </h3>
      </div>
      
      <p className="text-white/80 text-sm mb-4">
        Search for your organization by name or domain to get the Organization ID needed for registration.
      </p>

      <div className="flex gap-3 mb-4">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search by organization name or domain..."
            className="w-full px-4 py-3 bg-white/20 backdrop-blur-lg border border-white/30 rounded-xl placeholder-white/60 text-white focus:outline-none focus:ring-2 focus:ring-cyan-400 focus:border-transparent transition-all duration-200"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={handleKeyPress}
          />
        </div>
        <button
          onClick={searchOrganizations}
          disabled={loading}
          className="px-6 py-3 bg-cyan-500/20 backdrop-blur-lg border border-cyan-300/30 rounded-xl text-cyan-200 hover:bg-cyan-500/30 transition-all duration-200 disabled:opacity-50"
        >
          {loading ? (
            <div className="w-5 h-5 border-2 border-cyan-300 border-t-transparent rounded-full animate-spin" />
          ) : (
            <Search className="w-5 h-5" />
          )}
        </button>
      </div>

      {organizations.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-white/90 font-medium">Search Results:</h4>
          {organizations.map((org) => (
            <div
              key={org.id}
              className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-xl p-4 hover:bg-white/10 transition-all duration-200"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <h5 className="text-white font-medium">{org.name}</h5>
                  {org.domain && (
                    <p className="text-white/60 text-sm">Domain: {org.domain}</p>
                  )}
                  <p className="text-white/80 text-sm font-mono mt-1">
                    ID: {org.id}
                  </p>
                </div>
                <button
                  onClick={() => copyToClipboard(org.id)}
                  className="ml-3 p-2 bg-cyan-500/20 backdrop-blur-lg border border-cyan-300/30 rounded-lg text-cyan-200 hover:bg-cyan-500/30 transition-all duration-200"
                  title="Copy Organization ID"
                >
                  {copiedId === org.id ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}