import express from 'express';
import { db } from '../db';
import { voiceCalls, candidates, jobPostings, organizations } from '@shared/schema';
import { eq } from 'drizzle-orm';

const router = express.Router();

// Step 1: Simple static TwiML endpoint - no streaming, no ElevenLabs
router.post('/twiml-static', (req, res) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('🧪 STEP 1: Static TwiML requested - CallSID:', req.body.CallSid);
  }
  
  res.set('Content-Type', 'text/xml');
  res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="alice">This is a connectivity test from your recruitment system. The call is working correctly. Staying on the line for eight seconds to test connection stability.</Say>
  <Pause length="8"/>
  <Say voice="alice">Test complete. Thank you for your patience. Goodbye.</Say>
</Response>`);
  
  console.log('✅ STEP 1: Static TwiML response sent successfully');
});

// Step 2: Status callback endpoint to track call lifecycle
router.post('/twilio-status', async (req, res) => {
  console.log('📊 STEP 2: Twilio Status Callback Received');
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 StatusCB - CallSID:', req.body.CallSid, 'Status:', req.body.CallStatus);
  }
  
  try {
    // Update call status in database
    const callSid = req.body.CallSid;
    const callStatus = req.body.CallStatus;
    const duration = req.body.Duration;
    
    if (callSid) {
      // Find the call record by Twilio SID
      const [voiceCall] = await db
        .select()
        .from(voiceCalls)
        .where(eq(voiceCalls.twilioCallSid, callSid));

      if (voiceCall) {
        const updateData: any = {};
        
        if (callStatus === 'completed') {
          updateData.status = 'completed';
          updateData.endedAt = new Date();
          if (duration) {
            updateData.duration = parseInt(duration);
          }
          console.log('✅ UPDATING CALL STATUS TO COMPLETED');
        } else if (callStatus === 'failed' || callStatus === 'canceled' || callStatus === 'busy' || callStatus === 'no-answer') {
          updateData.status = 'failed';
          updateData.endedAt = new Date();
          console.log('❌ UPDATING CALL STATUS TO FAILED:', callStatus);
        }

        if (Object.keys(updateData).length > 0) {
          await db
            .update(voiceCalls)
            .set(updateData)
            .where(eq(voiceCalls.id, voiceCall.id));
          
          console.log('✅ CALL STATUS UPDATED IN DATABASE:', updateData);
        }
      } else {
        console.log('⚠️ No voice call found for Twilio SID:', callSid);
      }
    }
    
    // Log status for debugging
    if (process.env.NODE_ENV === 'development') {
      console.log('📊 Status Data:', {
        callSid: req.body.CallSid,
        callStatus: req.body.CallStatus,
        duration: req.body.Duration,
        timestamp: new Date().toISOString()
      });
    }
    
  } catch (error) {
    console.error('❌ Error updating call status:', error);
  }
  
  res.sendStatus(200);
});

// Step 3: TwiML endpoint that connects to WebSocket stream (no ElevenLabs yet)
router.post('/twiml-stream', (req, res) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('🧪 STEP 3: Stream TwiML requested - CallSID:', req.body.CallSid);
  }
  
  res.set('Content-Type', 'text/xml');
  res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="alice">Connecting to test stream. You should hear silence while the connection is active.</Say>
  <Connect>
    <Stream url="wss://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/twilio-stream"/>
  </Connect>
</Response>`);
  
  console.log('✅ STEP 3: Stream TwiML response sent successfully');
});

// Step 4: TwiML endpoint with ElevenLabs integration (FIXED TO USE REAL DATA)
router.post('/twiml-elevenlabs', async (req, res) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('🎯 TWIML ELEVENLABS ENDPOINT CALLED - CallSID:', req.body.CallSid);
    console.log('🔍 Voice call reached TwiML handler successfully!');
  }
  
  try {
    const callSid = req.body.CallSid;
    
    // CRITICAL FIX: Look up call by Twilio Call SID to get real data
    const [call] = await db.select().from(voiceCalls).where(eq(voiceCalls.twilioCallSid, callSid));
    
    if (!call) {
      console.log('⚠️ STEP 4: Call not found in database for SID:', callSid);
      // Fallback to hardcoded values if call not found
      res.set('Content-Type', 'text/xml');
      res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Connect>
    <Stream url="wss://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/elevenlabs-stream-final">
      <Parameter name="agent_id" value="${process.env.ELEVENLABS_AGENT_ID || 'agent_default'}" />
      <Parameter name="call_id" value="${callSid}" />
      <Parameter name="job_title" value="Technical Position" />
    </Stream>
  </Connect>
  <Say voice="alice">If you're hearing this message, there was a connection issue. Please try calling back in a few minutes. Thank you.</Say>
  <Hangup/>
</Response>`);
      return;
    }

    // Get candidate, job posting, and organization data
    const [candidate] = await db.select().from(candidates).where(eq(candidates.id, call.candidateId));
    const [organization] = await db.select().from(organizations).where(eq(organizations.id, call.organizationId));
    
    let jobPosting = null;
    if (call.jobPostingId) {
      const jobResult = await db.select().from(jobPostings).where(eq(jobPostings.id, call.jobPostingId));
      jobPosting = jobResult[0] || null;
    } else if (candidate?.appliedJobId) {
      const jobResult = await db.select().from(jobPostings).where(eq(jobPostings.id, candidate.appliedJobId));
      jobPosting = jobResult[0] || null;
    }

    // CRITICAL FIX: Pass IDs instead of computed values so WebSocket handler can fetch fresh data
    console.log('🎯 STEP 4: Using ID-based approach for fresh data:', {
      candidate_id: call.candidateId,
      organization_id: call.organizationId,
      job_posting_id: call.jobPostingId || candidate?.appliedJobId,
      call_purpose: call.callPurpose
    });
    
    // 🚨 SIMPLIFIED TEST: Only sending job_title parameter
    const jobTitle = jobPosting?.title || 'Software Engineer';
    console.log('🔍 SIMPLIFIED TEST - ONLY SENDING JOB_TITLE:');
    console.log('  job_title:', jobTitle);
    console.log('  jobPosting found:', !!jobPosting);
    console.log('  jobPosting title:', jobPosting?.title);
    
    if (!jobTitle || jobTitle === 'Software Engineer') {
      console.log('⚠️ Using fallback job title - real job title not found');
    } else {
      console.log('✅ Using REAL job title from database:', jobTitle);
    }
  
    res.set('Content-Type', 'text/xml');
    res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Connect>
    <Stream url="wss://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/elevenlabs-stream-final">
      <Parameter name="agent_id" value="${process.env.ELEVENLABS_AGENT_ID || 'agent_default'}" />
      <Parameter name="call_id" value="${callSid}" />
      <Parameter name="job_title" value="${jobPosting?.title || 'Software Engineer'}" />
    </Stream>
  </Connect>
  <Say voice="alice">If you're hearing this message, there was a connection issue. Please try calling back in a few minutes. Thank you.</Say>
  <Hangup/>
</Response>`);
  
    console.log('✅ STEP 4: ElevenLabs TwiML response sent with REAL DATA successfully');
  } catch (error) {
    console.error('❌ STEP 4: Error fetching call data:', error);
    // Fallback to basic response on error
    res.set('Content-Type', 'text/xml');
    res.status(200).send(`<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="alice">Hello! We're experiencing a technical issue. Please try calling back in a few minutes. Thank you.</Say>
  <Hangup/>
</Response>`);
  }
});

export default router;