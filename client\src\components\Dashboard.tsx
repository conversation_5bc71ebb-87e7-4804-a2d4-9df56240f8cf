import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  FileText,
  Users,
  Calendar,
  Briefcase,
  BarChart3,
  TrendingUp,
  CheckCircle,
  Activity,
  AlertCircle,
  ArrowUp
} from 'lucide-react';
import { apiRequest } from '../lib/queryClient';

interface DashboardMetrics {
  jobPostings: {
    total: number;
    active: number;
    inactive: number;
    new: number;
  };
  candidates: {
    total: number;
    new: number;
    byStatus: Record<string, number>;
  };
  applications: {
    total: number;
    byStatus: Record<string, number>;
  };
  interviews: {
    total: number;
    scheduled: number;
  };
  voiceCalls: {
    total: number;
    completed: number;
  };
}

interface AuditLog {
  id: string;
  action: string;
  resourceType: string;
  resourceId: string | null;
  userEmail: string;
  userRole: string;
  success: boolean;
  errorMessage: string | null;
  createdAt: string;
}

export default function Dashboard() {
  const { user } = useAuth();
  const [greeting, setGreeting] = useState(() => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 17) return 'Good afternoon';
    return 'Good evening';
  });
  const [metrics, setMetrics] = useState<DashboardMetrics | null>(null);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState('month');

  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const [metricsData, logsData] = await Promise.all([
          apiRequest(`/api/dashboard/metrics?period=${period}`),
          apiRequest('/api/dashboard/audit-logs?limit=20')
        ]);

        setMetrics(metricsData.metrics);
        setAuditLogs(logsData.logs);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [period]);

  // Format time ago
  const timeAgo = (date: string) => {
    const seconds = Math.floor((new Date().getTime() - new Date(date).getTime()) / 1000);
    if (seconds < 60) return 'just now';
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`;
    if (seconds < 86400) return `${Math.floor(seconds / 3600)}h ago`;
    return `${Math.floor(seconds / 86400)}d ago`;
  };

  // Prepare data for visualizations
  const candidateStatusData = metrics ? Object.entries(metrics.candidates.byStatus).map(([status, count], index) => {
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'];
    return {
      name: status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value: count,
      color: colors[index % colors.length],
      percentage: metrics.candidates.total > 0 ? ((count / metrics.candidates.total) * 100).toFixed(1) : 0
    };
  }) : [];

  const applicationStatusData = metrics ? Object.entries(metrics.applications.byStatus).map(([status, count], index) => {
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'];
    return {
      name: status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      value: count,
      color: colors[index % colors.length],
      percentage: metrics.applications.total > 0 ? ((count / metrics.applications.total) * 100).toFixed(1) : 0
    };
  }) : [];

  if (loading) {
    return (
      <div className="p-6 max-w-7xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="h-screen overflow-y-auto"
      style={{ backgroundColor: '#FBFAFF' }}
    >
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2" style={{ color: '#0152FF' }}>
              {greeting}, {user?.fullName?.split(' ')[0] || 'User'}
            </h1>
            <p className="text-gray-600">Executive Dashboard - Organization Overview</p>
          </div>
          <div className="flex gap-2">
            <select
              value={period}
              onChange={(e) => setPeriod(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="quarter">Last Quarter</option>
              <option value="year">Last Year</option>
            </select>
          </div>
        </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Job Postings */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 rounded-lg bg-blue-100 text-blue-600">
              <Briefcase className="w-6 h-6" />
            </div>
            <span className="text-xs text-green-600 flex items-center">
              <ArrowUp className="w-3 h-3 mr-1" />
              {metrics?.jobPostings.new || 0} new
            </span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{metrics?.jobPostings.total || 0}</h3>
          <p className="text-sm text-gray-600">Job Postings</p>
          <div className="mt-2 text-xs text-gray-500">
            {metrics?.jobPostings.active || 0} active • {metrics?.jobPostings.inactive || 0} inactive
          </div>
        </div>

        {/* Candidates */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 rounded-lg bg-green-100 text-green-600">
              <Users className="w-6 h-6" />
            </div>
            <span className="text-xs text-green-600 flex items-center">
              <ArrowUp className="w-3 h-3 mr-1" />
              {metrics?.candidates.new || 0} new
            </span>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{metrics?.candidates.total || 0}</h3>
          <p className="text-sm text-gray-600">Total Candidates</p>
          <div className="mt-2 text-xs text-gray-500">
            {metrics?.candidates.byStatus.approved_for_interview || 0} approved
          </div>
        </div>

        {/* Applications */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 rounded-lg bg-purple-100 text-purple-600">
              <FileText className="w-6 h-6" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{metrics?.applications.total || 0}</h3>
          <p className="text-sm text-gray-600">Applications</p>
          <div className="mt-2 text-xs text-gray-500">
            {metrics?.applications.byStatus.pending || 0} pending review
          </div>
        </div>

        {/* Interviews */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 rounded-lg bg-orange-100 text-orange-600">
              <Calendar className="w-6 h-6" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-gray-900">{metrics?.interviews.total || 0}</h3>
          <p className="text-sm text-gray-600">Interviews</p>
          <div className="mt-2 text-xs text-gray-500">
            {metrics?.interviews.scheduled || 0} scheduled
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Candidate Status Distribution */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2" />
            Candidate Pipeline
          </h3>
          <div className="space-y-4">
            {candidateStatusData.length === 0 ? (
              <p className="text-sm text-gray-500 text-center py-8">No candidate data available</p>
            ) : (
              candidateStatusData.map((item, index) => (
                <div key={index}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-gray-700">{item.name}</span>
                    <span className="text-sm text-gray-600">{item.value} ({item.percentage}%)</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full transition-all duration-300"
                      style={{
                        width: `${item.percentage}%`,
                        backgroundColor: item.color
                      }}
                    ></div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        {/* Application Status Distribution */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Application Status
          </h3>
          <div className="space-y-4">
            {applicationStatusData.length === 0 ? (
              <p className="text-sm text-gray-500 text-center py-8">No application data available</p>
            ) : (
              applicationStatusData.map((item, index) => (
                <div key={index}>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-gray-700">{item.name}</span>
                    <span className="text-sm text-gray-600">{item.value} ({item.percentage}%)</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div
                      className="h-2.5 rounded-full transition-all duration-300"
                      style={{
                        width: `${item.percentage}%`,
                        backgroundColor: item.color
                      }}
                    ></div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Audit Logs Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Activity className="w-5 h-5 mr-2" />
          Recent Activity & Audit Logs
        </h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Resource
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {auditLogs.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                    No audit logs available
                  </td>
                </tr>
              ) : (
                auditLogs.map((log) => (
                  <tr key={log.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {timeAgo(log.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{log.userEmail}</div>
                      <div className="text-xs text-gray-500">{log.userRole}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {log.resourceType}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {log.success ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Success
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          <AlertCircle className="w-3 h-3 mr-1" />
                          Failed
                        </span>
                      )}
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
      </div>
    </div>
  );
}