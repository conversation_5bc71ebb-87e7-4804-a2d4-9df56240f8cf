import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Shield, 
  Key, 
  Link, 
  CheckCircle, 
  AlertCircle, 
  Settings, 
  Save,
  ExternalLink,
  Copy,
  RefreshCw,
  Users
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import SSOSetupGuide from './SSOSetupGuide';

interface SSOProvider {
  id: string;
  name: string;
  type: 'saml' | 'oidc' | 'oauth2';
  enabled: boolean;
  configuration: {
    clientId?: string;
    clientSecret?: string;
    issuerUrl?: string;
    discoveryUrl?: string;
    redirectUri?: string;
    scopes?: string[];
    attributes?: {
      email?: string;
      firstName?: string;
      lastName?: string;
      groups?: string;
    };
  };
  metadata?: {
    entityId?: string;
    ssoUrl?: string;
    certificate?: string;
  };
  status: 'active' | 'inactive' | 'error';
  lastSync?: string;
  userCount?: number;
}

interface SSOConfig {
  enabled: boolean;
  autoProvisioning: boolean;
  defaultRole: string;
  allowedDomains: string[];
  providers: SSOProvider[];
}

export default function SSOSetup() {
  const [config, setConfig] = useState<SSOConfig>({
    enabled: false,
    autoProvisioning: true,
    defaultRole: 'member',
    allowedDomains: [],
    providers: []
  });
  const [loading, setLoading] = useState(true);
  const [testingProvider, setTestingProvider] = useState<string | null>(null);
  const [domainInput, setDomainInput] = useState('');
  const [activeProvider, setActiveProvider] = useState<SSOProvider | null>(null);
  const [showProviderForm, setShowProviderForm] = useState(false);
  const [providerForm, setProviderForm] = useState({
    name: '',
    type: 'oidc' as 'saml' | 'oidc' | 'oauth2',
    clientId: '',
    clientSecret: '',
    issuerUrl: '',
    discoveryUrl: '',
    scopes: 'openid email profile',
    attributeEmail: 'email',
    attributeFirstName: 'given_name',
    attributeLastName: 'family_name',
    attributeGroups: 'groups'
  });

  const { toast } = useToast();

  // Fetch SSO configuration
  const fetchSSOConfig = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/sso/config', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setConfig(data);
      }
    } catch (error) {
      console.error('Error fetching SSO config:', error);
    } finally {
      setLoading(false);
    }
  };

  // Save SSO configuration
  const saveSSOConfig = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/sso/config', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "SSO configuration saved successfully"
        });
      } else {
        throw new Error('Failed to save configuration');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save SSO configuration",
        variant: "destructive"
      });
    }
  };

  // Test SSO provider
  const testSSOProvider = async (providerId: string) => {
    setTestingProvider(providerId);
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch(`/api/sso/test/${providerId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          toast({
            title: "Success",
            description: "SSO provider test successful"
          });
        } else {
          toast({
            title: "Test Failed",
            description: result.error || "SSO provider test failed",
            variant: "destructive"
          });
        }
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test SSO provider",
        variant: "destructive"
      });
    } finally {
      setTestingProvider(null);
    }
  };

  // Add SSO provider
  const addSSOProvider = async () => {
    if (!providerForm.name || !providerForm.clientId || !providerForm.clientSecret) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/sso/providers', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: providerForm.name,
          type: providerForm.type,
          configuration: {
            clientId: providerForm.clientId,
            clientSecret: providerForm.clientSecret,
            issuerUrl: providerForm.issuerUrl,
            discoveryUrl: providerForm.discoveryUrl,
            scopes: providerForm.scopes.split(' '),
            attributes: {
              email: providerForm.attributeEmail,
              firstName: providerForm.attributeFirstName,
              lastName: providerForm.attributeLastName,
              groups: providerForm.attributeGroups
            }
          }
        })
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "SSO provider added successfully"
        });
        setShowProviderForm(false);
        setProviderForm({
          name: '',
          type: 'oidc',
          clientId: '',
          clientSecret: '',
          issuerUrl: '',
          discoveryUrl: '',
          scopes: 'openid email profile',
          attributeEmail: 'email',
          attributeFirstName: 'given_name',
          attributeLastName: 'family_name',
          attributeGroups: 'groups'
        });
        fetchSSOConfig();
      } else {
        throw new Error('Failed to add provider');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add SSO provider",
        variant: "destructive"
      });
    }
  };

  // Add allowed domain
  const addAllowedDomain = () => {
    if (domainInput && !config.allowedDomains.includes(domainInput)) {
      setConfig({
        ...config,
        allowedDomains: [...config.allowedDomains, domainInput]
      });
      setDomainInput('');
    }
  };

  // Remove allowed domain
  const removeAllowedDomain = (domain: string) => {
    setConfig({
      ...config,
      allowedDomains: config.allowedDomains.filter(d => d !== domain)
    });
  };

  // Copy redirect URI
  const copyRedirectUri = (providerId: string) => {
    const redirectUri = `${window.location.origin}/api/sso/callback/${providerId}`;
    navigator.clipboard.writeText(redirectUri);
    toast({
      title: "Copied",
      description: "Redirect URI copied to clipboard"
    });
  };

  useEffect(() => {
    fetchSSOConfig();
  }, []);

  const getProviderIcon = (type: string) => {
    switch (type) {
      case 'saml':
        return <Shield className="w-5 h-5" />;
      case 'oidc':
        return <Key className="w-5 h-5" />;
      case 'oauth2':
        return <Link className="w-5 h-5" />;
      default:
        return <Settings className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="h-screen overflow-y-auto bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Single Sign-On (SSO)</h1>
            <p className="text-gray-600 mt-2">Configure authentication providers for your organization</p>
          </div>
          <div className="flex items-center space-x-3">
            <Button onClick={saveSSOConfig} className="flex items-center gap-2">
              <Save className="w-4 h-4" />
              Save Configuration
            </Button>
          </div>
        </div>

        {/* SSO Status */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              SSO Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Single Sign-On</p>
                <p className="text-sm text-gray-600">
                  {config.enabled ? 'Users can sign in with external providers' : 'Only local authentication is available'}
                </p>
              </div>
              <Switch
                checked={config.enabled}
                onCheckedChange={(checked) => setConfig({...config, enabled: checked})}
              />
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="providers" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="providers">Providers</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="users">SSO Users</TabsTrigger>
            <TabsTrigger value="guide">Setup Guide</TabsTrigger>
          </TabsList>

          {/* Providers Tab */}
          <TabsContent value="providers" className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Authentication Providers</h3>
              <Button 
                onClick={() => setShowProviderForm(true)}
                className="flex items-center gap-2"
              >
                <Key className="w-4 h-4" />
                Add Provider
              </Button>
            </div>

            {/* Provider Form */}
            {showProviderForm && (
              <Card>
                <CardHeader>
                  <CardTitle>Add New SSO Provider</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Provider Name *</Label>
                      <Input
                        id="name"
                        value={providerForm.name}
                        onChange={(e) => setProviderForm({...providerForm, name: e.target.value})}
                        placeholder="Google Workspace"
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">Provider Type</Label>
                      <select
                        id="type"
                        value={providerForm.type}
                        onChange={(e) => setProviderForm({...providerForm, type: e.target.value as any})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="oidc">OpenID Connect</option>
                        <option value="oauth2">OAuth 2.0</option>
                        <option value="saml">SAML 2.0</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="clientId">Client ID *</Label>
                      <Input
                        id="clientId"
                        value={providerForm.clientId}
                        onChange={(e) => setProviderForm({...providerForm, clientId: e.target.value})}
                        placeholder="your-client-id"
                      />
                    </div>
                    <div>
                      <Label htmlFor="clientSecret">Client Secret *</Label>
                      <Input
                        id="clientSecret"
                        type="password"
                        value={providerForm.clientSecret}
                        onChange={(e) => setProviderForm({...providerForm, clientSecret: e.target.value})}
                        placeholder="your-client-secret"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="issuerUrl">Issuer URL</Label>
                    <Input
                      id="issuerUrl"
                      value={providerForm.issuerUrl}
                      onChange={(e) => setProviderForm({...providerForm, issuerUrl: e.target.value})}
                      placeholder="https://accounts.google.com"
                    />
                  </div>

                  <div>
                    <Label htmlFor="discoveryUrl">Discovery URL</Label>
                    <Input
                      id="discoveryUrl"
                      value={providerForm.discoveryUrl}
                      onChange={(e) => setProviderForm({...providerForm, discoveryUrl: e.target.value})}
                      placeholder="https://accounts.google.com/.well-known/openid_configuration"
                    />
                  </div>

                  <div>
                    <Label htmlFor="scopes">Scopes</Label>
                    <Input
                      id="scopes"
                      value={providerForm.scopes}
                      onChange={(e) => setProviderForm({...providerForm, scopes: e.target.value})}
                      placeholder="openid email profile"
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setShowProviderForm(false)}>
                      Cancel
                    </Button>
                    <Button onClick={addSSOProvider}>
                      Add Provider
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Existing Providers */}
            <div className="grid gap-4">
              {config.providers.map((provider) => (
                <Card key={provider.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getProviderIcon(provider.type)}
                        <div>
                          <h4 className="font-medium">{provider.name}</h4>
                          <p className="text-sm text-gray-600">
                            {provider.type.toUpperCase()} • {provider.userCount || 0} users
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge className={getStatusColor(provider.status)}>
                          {provider.status}
                        </Badge>
                        <div className="flex items-center space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyRedirectUri(provider.id)}
                          >
                            <Copy className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => testSSOProvider(provider.id)}
                            disabled={testingProvider === provider.id}
                          >
                            {testingProvider === provider.id ? (
                              <RefreshCw className="w-4 h-4 animate-spin" />
                            ) : (
                              <ExternalLink className="w-4 h-4" />
                            )}
                          </Button>
                          <Switch
                            checked={provider.enabled}
                            onCheckedChange={(checked) => {
                              const updatedProviders = config.providers.map(p =>
                                p.id === provider.id ? { ...p, enabled: checked } : p
                              );
                              setConfig({ ...config, providers: updatedProviders });
                            }}
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div className="mt-4 p-3 bg-gray-50 rounded-md">
                      <p className="text-sm font-medium text-gray-700">Redirect URI:</p>
                      <p className="text-sm text-gray-600 font-mono">
                        {window.location.origin}/api/sso/callback/{provider.id}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>SSO Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Auto-provision users</p>
                    <p className="text-sm text-gray-600">
                      Automatically create user accounts for new SSO users
                    </p>
                  </div>
                  <Switch
                    checked={config.autoProvisioning}
                    onCheckedChange={(checked) => setConfig({...config, autoProvisioning: checked})}
                  />
                </div>

                <div>
                  <Label htmlFor="defaultRole">Default Role for New Users</Label>
                  <select
                    id="defaultRole"
                    value={config.defaultRole}
                    onChange={(e) => setConfig({...config, defaultRole: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="viewer">Viewer</option>
                    <option value="member">Member</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>

                <div>
                  <Label>Allowed Domains</Label>
                  <div className="flex items-center space-x-2 mt-2">
                    <Input
                      value={domainInput}
                      onChange={(e) => setDomainInput(e.target.value)}
                      placeholder="company.com"
                    />
                    <Button onClick={addAllowedDomain}>Add</Button>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {config.allowedDomains.map((domain) => (
                      <Badge key={domain} variant="secondary" className="cursor-pointer">
                        {domain}
                        <button
                          onClick={() => removeAllowedDomain(domain)}
                          className="ml-2 text-red-500 hover:text-red-700"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* SSO Users Tab */}
          <TabsContent value="users" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>SSO Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">SSO user management will be available here</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Setup Guide Tab */}
          <TabsContent value="guide" className="space-y-6">
            <SSOSetupGuide />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}