import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';

export function setupTwilioStreamTest(server: Server) {
  console.log('🧪 Setting up Twilio Stream Test WebSocket...');
  
  const wss = new WebSocketServer({ 
    server, 
    path: '/twilio-stream',
    perMessageDeflate: false
  });

  wss.on('connection', (ws: WebSocket, req) => {
    let frames = 0;
    let callSid = 'unknown';
    
    console.log('🎉 STEP 3: Twilio Stream WebSocket Connected!');
    console.log('📞 Connection from:', req.url);
    
    // Keep connection alive with ping every 15 seconds
    const keepAlive = setInterval(() => {
      try {
        ws.ping();
      } catch (error) {
        console.log('Ping failed, connection likely closed');
      }
    }, 15000);

    ws.on('message', (buffer: Buffer) => {
      try {
        const msg = JSON.parse(buffer.toString());
        
        if (msg.event === 'connected') {
          console.log('🔗 STEP 3: Twilio confirmed WebSocket connection');
          console.log('📊 Protocol:', msg.protocol);
        }
        
        if (msg.event === 'start') {
          callSid = msg.start?.callSid || callSid;
          console.log('🎬 STEP 3: Stream started for call:', callSid);
          console.log('📊 Stream details:', {
            streamSid: msg.start?.streamSid,
            tracks: msg.start?.tracks,
            mediaFormat: msg.start?.mediaFormat
          });
        }
        
        if (msg.event === 'media') {
          frames++;
          if (frames % 100 === 0) {
            console.log('📊 STEP 3: Media frames received:', frames);
          }
        }
        
        if (msg.event === 'stop') {
          console.log('🛑 STEP 3: Stream stopped for call:', callSid);
          console.log('📊 Total frames processed:', frames);
          ws.close();
        }
      } catch (error) {
        console.error('❌ STEP 3: Error parsing WebSocket message:', error);
      }
    });

    ws.on('close', () => {
      clearInterval(keepAlive);
      console.log('📞 STEP 3: WebSocket closed for call:', callSid);
      console.log('📊 Final frame count:', frames);
    });

    ws.on('error', (error) => {
      console.error('❌ STEP 3: WebSocket error:', error);
    });

    // IMPORTANT: DO NOT close the socket early - let Twilio close it
    console.log('✅ STEP 3: WebSocket handler initialized, waiting for Twilio...');
  });
  
  console.log('🔊 STEP 3: WebSocket server listening on /twilio-stream');
  return wss;
}