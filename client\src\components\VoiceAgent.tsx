import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from '@/hooks/use-toast';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Phone, PhoneCall, Clock, CheckCircle, XCircle, MessageCircle, Calendar, Star, Al<PERSON><PERSON>rian<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Eye } from 'lucide-react';
import VoiceProviderSelector from './VoiceProviderSelector';
import { useAuth } from '@/contexts/AuthContext';
interface VoiceCall {
  id: string;
  status: string;
  scheduledAt?: string;
  startedAt?: string;
  endedAt?: string;
  durationSeconds?: number;
  callPurpose?: string;
  recordingUrl?: string;
  transcription?: string;
  conversationNotes?: string;
  callRating?: number;
  followUpRequired?: boolean;
  followUpNotes?: string;
  createdAt: string;
}

interface VoiceCallNote {
  id: string;
  noteType: string;
  content: string;
  timestamp: string;
  aiGenerated: boolean;
  importance: number;
  actionRequired: boolean;
}

interface CallSummary {
  id: string;
  callId: string;
  summary: string;
  nextActions: Record<string, {
    description: string;
    due_time?: string | null;
    assignee?: string | null;
    metadata?: Record<string, any> | null;
  }> | null;
  scheduled: boolean;
  scheduledTime: string | null;
  scheduledEvidence: string | null;
  openQuestions: string[] | null;
  riskFlags: string[] | null;
  generatedAt: string;
  modelUsed: string;
  durationSeconds: number;
}

interface VoiceAgentProps {
  candidateId: string;
  candidateName: string;
  candidatePhone?: string;
  candidateEmail: string;
}

interface CallInitiationForm {
  phoneNumber: string;
  scheduledAt: string;
  callPurpose: string;
  provider: string;
}

export default function VoiceAgent({ candidateId, candidateName, candidatePhone, candidateEmail }: VoiceAgentProps) {
  const [showInitiateDialog, setShowInitiateDialog] = useState(false);
  const { user } = useAuth();
  const [callForm, setCallForm] = useState<CallInitiationForm>({
    phoneNumber: candidatePhone || '',
    scheduledAt: '',
    callPurpose: 'technical_interview_scheduling',
    provider: 'manual-call'
  });
  const [selectedCall, setSelectedCall] = useState<VoiceCall | null>(null);
  const [showCallDetails, setShowCallDetails] = useState(false);

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch call history for candidate
  const { data: callHistory = [], isLoading } = useQuery({
    queryKey: [`/api/voice-providers/calls/${candidateId}`],
    queryFn: async () => {
      const response = await fetch(`/api/voice-providers/calls/${candidateId}`, {
        credentials: 'include'
      });
      if (!response.ok) throw new Error('Failed to fetch call history');
      return response.json();
    }
  });

  // Fetch call summaries for the organization
  const { data: summariesResponse } = useQuery({
    queryKey: ['/api/call-summaries/organization', user?.organizationId],
    queryFn: async () => {
      if (!user?.organizationId) return { summaries: [] };
      const response = await fetch(`/api/call-summaries/organization/${user.organizationId}`, {
        credentials: 'include'
      });
      if (!response.ok) throw new Error('Failed to fetch call summaries');
      return response.json();
    },
    enabled: !!user?.organizationId
  });

  const callSummaries: CallSummary[] = summariesResponse?.summaries?.filter((summary: CallSummary) => 
    callHistory.some((call: VoiceCall) => call.id === summary.callId)
  ) || [];

  // Create a map of callId to summary for easy lookup
  const summaryMap = new Map<string, CallSummary>();
  callSummaries.forEach(summary => {
    summaryMap.set(summary.callId, summary);
  });

  // Fetch call notes for selected call
  const { data: callNotes = [] } = useQuery({
    queryKey: [`/api/voice-providers/calls/${selectedCall?.id}/notes`],
    queryFn: async () => {
      if (!selectedCall?.id) return [];
      const response = await fetch(`/api/voice-providers/calls/${selectedCall.id}/notes`, {
        credentials: 'include'
      });
      if (!response.ok) throw new Error('Failed to fetch call notes');
      return response.json();
    },
    enabled: !!selectedCall?.id
  });

  // Initiate call mutation
  const initiateCallMutation = useMutation({
    mutationFn: async (callData: CallInitiationForm) => {
      const response = await fetch(`/api/voice-providers/initiate/${callData.provider}/${candidateId}`, {
        method: 'POST',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          phoneNumber: callData.phoneNumber,
          scheduledAt: callData.scheduledAt,
          callPurpose: callData.callPurpose
        })
      });
      if (!response.ok) throw new Error('Failed to initiate call');
      return response.json();
    },
    onSuccess: (data) => {
      toast({
        title: "Call Initiated",
        description: data.status === 'initiated' ? 
          "Voice call has been started successfully!" : 
          "Voice call has been scheduled successfully!",
      });
      queryClient.invalidateQueries({ queryKey: [`/api/voice-providers/calls/${candidateId}`] });
      setShowInitiateDialog(false);
      setCallForm({
        phoneNumber: candidatePhone || '',
        scheduledAt: '',
        callPurpose: 'technical_interview_scheduling',
        provider: 'manual-call'
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Call Failed",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled': return <Clock className="w-4 h-4 text-blue-500" />;
      case 'in_progress': return <PhoneCall className="w-4 h-4 text-yellow-500 animate-pulse" />;
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed': return <XCircle className="w-4 h-4 text-red-500" />;
      case 'cancelled': return <XCircle className="w-4 h-4 text-gray-500" />;
      default: return <Phone className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      case 'failed': return 'bg-red-100 text-red-800 border-red-200';
      case 'cancelled': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getNoteTypeIcon = (noteType: string) => {
    switch (noteType) {
      case 'experience': return <Star className="w-4 h-4 text-blue-500" />;
      case 'availability': return <Calendar className="w-4 h-4 text-green-500" />;
      case 'concern': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'positive': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'follow_up': return <MessageCircle className="w-4 h-4 text-orange-500" />;
      case 'transcription': return <User className="w-4 h-4 text-blue-500" />;
      case 'ai_response': return <Bot className="w-4 h-4 text-purple-500" />;
      default: return <MessageCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const getNoteTypeLabel = (noteType: string) => {
    switch (noteType) {
      case 'transcription': return 'Candidate';
      case 'ai_response': return 'Agent';
      case 'experience': return 'Experience';
      case 'availability': return 'Availability';
      case 'concern': return 'Concern';
      case 'positive': return 'Positive';
      case 'follow_up': return 'Follow Up';
      default: return noteType;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Call Initiation */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Phone className="w-5 h-5 text-blue-600" />
              Voice Agent - {candidateName}
            </CardTitle>
            <Dialog open={showInitiateDialog} onOpenChange={setShowInitiateDialog}>
              <DialogTrigger asChild>
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <PhoneCall className="w-4 h-4 mr-2" />
                  Initiate Call
                </Button>
              </DialogTrigger>
              <DialogContent className="max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Initiate Voice Call</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 max-h-[70vh] overflow-y-auto pr-2">
                  <div>
                    <Label>Voice Provider</Label>
                    <VoiceProviderSelector
                      selectedProvider={callForm.provider}
                      onProviderSelect={(provider) => setCallForm({ ...callForm, provider })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phoneNumber">Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      value={callForm.phoneNumber}
                      onChange={(e) => setCallForm({ ...callForm, phoneNumber: e.target.value })}
                      placeholder="+****************"
                    />
                  </div>
                  <div>
                    <Label htmlFor="scheduledAt">Schedule For (Optional)</Label>
                    <Input
                      id="scheduledAt"
                      type="datetime-local"
                      value={callForm.scheduledAt}
                      onChange={(e) => setCallForm({ ...callForm, scheduledAt: e.target.value })}
                    />
                    <p className="text-xs text-gray-500 mt-1">Leave empty to call immediately</p>
                  </div>
                  <div>
                    <Label htmlFor="callPurpose">Call Purpose</Label>
                    <Select
                      value={callForm.callPurpose}
                      onValueChange={(value) => setCallForm({ ...callForm, callPurpose: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="technical_interview_scheduling">Technical Interview Scheduling</SelectItem>
                        <SelectItem value="experience_discussion">Experience Discussion</SelectItem>
                        <SelectItem value="availability_check">Availability Check</SelectItem>
                        <SelectItem value="follow_up">Follow-up Call</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button
                      onClick={() => initiateCallMutation.mutate(callForm)}
                      disabled={initiateCallMutation.isPending || !callForm.phoneNumber || !callForm.provider}
                      className="flex-1"
                    >
                      {initiateCallMutation.isPending ? 'Initiating...' : 
                       callForm.provider === 'manual-call' ? 'Schedule Manual Call' :
                       callForm.provider === 'webrtc-browser-call' ? 'Setup Browser Call' :
                       'Start Automated Call'}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() => setShowInitiateDialog(false)}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Email:</span> {candidateEmail}
            </div>
            <div>
              <span className="font-medium">Phone:</span> {candidatePhone || 'Not provided'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Integrated Call History & Summaries */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Phone className="w-5 h-5 text-blue-600" />
            Call History & AI Summaries
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4" />
              <p className="text-gray-600">Loading call history...</p>
            </div>
          ) : callHistory.length === 0 ? (
            <Alert>
              <Phone className="w-4 h-4" />
              <AlertDescription>
                No voice calls yet. Click "Initiate Call" to start your first conversation with this candidate.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-4">
              {callHistory.map((call: VoiceCall) => {
                const summary = summaryMap.get(call.id);
                const duration = call.durationSeconds || summary?.durationSeconds || 0;
                
                return (
                  <div
                    key={call.id}
                    className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => {
                      setSelectedCall(call);
                      setShowCallDetails(true);
                    }}
                  >
                    {/* Call Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(call.status)}
                        <div>
                          <h4 className="font-medium">{call.callPurpose?.replace('_', ' ').toUpperCase()}</h4>
                          <p className="text-sm text-gray-600">
                            {call.scheduledAt ? (() => {
                              const utcDate = new Date(call.scheduledAt);
                              const localOffset = utcDate.getTimezoneOffset() * 60000;
                              const localDate = new Date(utcDate.getTime() + localOffset);
                              return localDate.toLocaleString();
                            })() : 'Immediate call'}
                          </p>
                        </div>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        data-testid={`view-call-${call.id}`}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    </div>

                    {/* Combined Key Fields */}
                    <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
                      <div>
                        <span className="font-medium text-gray-700">Status:</span>
                        <Badge className={`ml-2 ${getStatusColor(call.status)}`}>
                          {call.status.replace('_', ' ')}
                        </Badge>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Duration:</span>
                        <span className="ml-2 text-gray-900">
                          {duration > 0 ? formatDuration(duration) : 'N/A'}
                        </span>
                      </div>
                      {summary?.scheduled && (
                        <>
                          <div>
                            <span className="font-medium text-gray-700">Next Meeting:</span>
                            <Badge className="bg-green-100 text-green-800 ml-2">
                              <Calendar className="w-3 h-3 mr-1" />
                              Scheduled
                            </Badge>
                          </div>
                          {summary.scheduledTime && (
                            <div>
                              <span className="font-medium text-gray-700">Meeting Time:</span>
                              <span className="ml-2 text-gray-900">
                                {new Date(summary.scheduledTime).toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </span>
                            </div>
                          )}
                        </>
                      )}
                      {call.callRating && (
                        <div>
                          <span className="font-medium text-gray-700">Rating:</span>
                          <div className="flex items-center gap-1 ml-2 inline-flex">
                            <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            <span className="text-sm">{call.callRating}/5</span>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* AI Summary Section */}
                    {summary && (
                      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center gap-2 mb-2">
                          <Bot className="w-4 h-4 text-blue-600" />
                          <h5 className="font-medium text-blue-900">AI Call Summary</h5>
                        </div>
                        <p className="text-sm text-blue-800 mb-3">{summary.summary}</p>
                        
                        <div className="flex items-center gap-2 flex-wrap">
                          <Badge 
                            className={`text-xs ${Object.keys(summary.nextActions || {}).length === 0 ? 'bg-gray-100 text-gray-800' : 
                              Object.keys(summary.nextActions || {}).length <= 2 ? 'bg-blue-100 text-blue-800' :
                              Object.keys(summary.nextActions || {}).length <= 4 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}
                          >
                            {Object.keys(summary.nextActions || {}).length} Actions
                          </Badge>
                          
                          {(summary.openQuestions?.length || 0) > 0 && (
                            <Badge className="bg-blue-100 text-blue-800 text-xs">
                              <HelpCircle className="w-3 h-3 mr-1" />
                              {summary.openQuestions?.length || 0} Questions
                            </Badge>
                          )}
                          
                          {(summary.riskFlags?.length || 0) > 0 && (
                            <Badge className={`text-xs ${(summary.riskFlags?.length || 0) === 0 ? 'bg-green-100 text-green-800' : 
                              (summary.riskFlags?.length || 0) <= 2 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
                              <AlertTriangle className="w-3 h-3 mr-1" />
                              {summary.riskFlags?.length || 0} Risks
                            </Badge>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Conversation Notes Preview */}
                    {call.conversationNotes && (
                      <div className="text-sm text-gray-600 line-clamp-2">
                        <strong>Notes:</strong> {call.conversationNotes}
                      </div>
                    )}

                    {/* Follow-up Required */}
                    {call.followUpRequired && (
                      <Badge variant="outline" className="mt-2 text-orange-600 border-orange-200">
                        Follow-up Required
                      </Badge>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Call Details Modal */}
      <Dialog open={showCallDetails} onOpenChange={setShowCallDetails}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedCall && getStatusIcon(selectedCall.status)}
              Call Details - {selectedCall?.callPurpose?.replace('_', ' ')}
            </DialogTitle>
          </DialogHeader>
          {selectedCall && (() => {
            const summary = summaryMap.get(selectedCall.id);
            const duration = selectedCall.durationSeconds || summary?.durationSeconds || 0;
            
            return (
              <div className="space-y-6">
                {/* Combined Call Info & Summary */}
                <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <span className="font-medium">Status:</span>
                    <Badge className={`ml-2 ${getStatusColor(selectedCall.status)}`}>
                      {selectedCall.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  <div>
                    <span className="font-medium">Duration:</span>
                    <span className="ml-2">
                      {duration > 0 ? formatDuration(duration) : 'N/A'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Scheduled:</span>
                    <span className="ml-2">
                      {selectedCall.scheduledAt ? new Date(selectedCall.scheduledAt).toLocaleString() : 'Immediate'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Rating:</span>
                    <span className="ml-2">
                      {selectedCall.callRating ? `${selectedCall.callRating}/5 ⭐` : 'Not rated'}
                    </span>
                  </div>
                  {summary?.scheduled && (
                    <>
                      <div>
                        <span className="font-medium">Next Meeting:</span>
                        <Badge className="bg-green-100 text-green-800 ml-2">
                          <Calendar className="w-3 h-3 mr-1" />
                          Scheduled
                        </Badge>
                      </div>
                      {summary.scheduledTime && (
                        <div>
                          <span className="font-medium">Meeting Time:</span>
                          <span className="ml-2">
                            {new Date(summary.scheduledTime).toLocaleString()}
                          </span>
                        </div>
                      )}
                    </>
                  )}
                </div>

                {/* AI Summary */}
                {summary && (
                  <Card className="bg-blue-50 border-blue-200">
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-2">
                        <Bot className="w-5 h-5 text-blue-600" />
                        AI Call Summary
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-blue-800 leading-relaxed mb-4">{summary.summary}</p>

                      {/* Scheduled Meeting */}
                      {summary.scheduled && summary.scheduledEvidence && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                          <div className="flex items-center gap-2 mb-2">
                            <CheckCircle className="w-5 h-5 text-green-600" />
                            <h3 className="font-medium text-green-900">Meeting Scheduled</h3>
                          </div>
                          <p className="text-sm text-green-700 italic">
                            "{summary.scheduledEvidence}"
                          </p>
                        </div>
                      )}

                      {/* Next Actions */}
                      {Object.keys(summary.nextActions || {}).length > 0 && (
                        <div className="mb-4">
                          <h3 className="font-medium text-gray-900 mb-3">Next Actions</h3>
                          <div className="space-y-3">
                            {Object.entries(summary.nextActions || {}).map(([actionCode, action]) => (
                              <div key={actionCode} className="border border-gray-200 rounded-lg p-3">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1">
                                    <p className="font-medium text-gray-900 mb-1">{action.description}</p>
                                    <div className="flex items-center gap-2 text-sm text-gray-600">
                                      <code className="bg-gray-100 px-2 py-1 rounded text-xs">{actionCode}</code>
                                      {action.assignee && (
                                        <Badge variant="outline" className="text-xs">
                                          {action.assignee}
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                  {action.due_time && (
                                    <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                                      <Clock className="w-3 h-3 mr-1" />
                                      {new Date(action.due_time).toLocaleDateString('en-US', {
                                        month: 'short',
                                        day: 'numeric',
                                        hour: '2-digit',
                                        minute: '2-digit'
                                      })}
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Open Questions */}
                      {(summary.openQuestions?.length || 0) > 0 && (
                        <div className="mb-4">
                          <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                            <HelpCircle className="w-4 h-4 text-blue-600" />
                            Open Questions
                          </h3>
                          <div className="space-y-2">
                            {(summary.openQuestions || []).map((question, index) => (
                              <div key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <p className="text-blue-900">{question}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Risk Flags */}
                      {(summary.riskFlags?.length || 0) > 0 && (
                        <div>
                          <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-red-600" />
                            Risk Flags
                          </h3>
                          <div className="space-y-2">
                            {(summary.riskFlags || []).map((risk, index) => (
                              <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
                                <p className="text-red-900">{risk}</p>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

                {/* Recording & Transcription */}
              {(selectedCall.recordingUrl || selectedCall.transcription) && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Recording & Transcription</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {selectedCall.recordingUrl && (
                      <div className="mb-4">
                        <Label>Call Recording:</Label>
                        <audio controls className="w-full mt-2">
                          <source src={selectedCall.recordingUrl} type="audio/mpeg" />
                          Your browser does not support the audio element.
                        </audio>
                      </div>
                    )}
                    {selectedCall.transcription && (
                      <div>
                        <Label>Transcription:</Label>
                        <div className="mt-2 p-3 bg-gray-50 rounded border max-h-40 overflow-y-auto">
                          <p className="text-sm whitespace-pre-wrap">{selectedCall.transcription}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Conversation Notes */}
              {selectedCall.conversationNotes && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Conversation Notes</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="whitespace-pre-wrap">{selectedCall.conversationNotes}</p>
                  </CardContent>
                </Card>
              )}

              {/* Call Notes */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Detailed Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  {callNotes.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">No detailed notes available</p>
                  ) : (
                    <div className="space-y-3">
                      {callNotes.map((note: VoiceCallNote) => (
                        <div key={note.id} className="border-l-4 border-blue-200 pl-4 py-2">
                          <div className="flex items-center gap-2 mb-1">
                            {getNoteTypeIcon(note.noteType)}
                            <span className="font-medium">{getNoteTypeLabel(note.noteType)}</span>
                            {note.aiGenerated && (
                              <Badge variant="outline" className="text-xs">AI Generated</Badge>
                            )}
                            {note.actionRequired && (
                              <Badge variant="outline" className="text-xs text-orange-600 border-orange-200">
                                Action Required
                              </Badge>
                            )}
                            <span className="text-xs text-gray-500 ml-auto">
                              {new Date(note.timestamp).toLocaleString()}
                            </span>
                          </div>
                          <p className="text-sm text-gray-700">{note.content}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <span className="text-xs text-gray-500">Importance:</span>
                            <div className="flex">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  className={`w-3 h-3 ${
                                    i < note.importance ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Follow-up */}
              {selectedCall.followUpRequired && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg text-orange-600">Follow-up Required</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p>{selectedCall.followUpNotes || 'No specific follow-up notes provided.'}</p>
                  </CardContent>
                </Card>
                )}
              </div>
            );
          })()}
        </DialogContent>
      </Dialog>
    </div>
  );
}