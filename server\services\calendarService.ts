import { google } from 'googleapis';
import { promises as fs } from 'fs';
import path from 'path';
import { gmailService } from './gmailService';

interface CalendarEvent {
  summary: string;
  description: string;
  start: {
    dateTime: string;
    timeZone: string;
  };
  end: {
    dateTime: string;
    timeZone: string;
  };
  attendees: Array<{
    email: string;
    displayName?: string;
  }>;
  reminders: {
    useDefault: boolean;
  };
}

interface InterviewSlot {
  candidateName: string;
  candidateEmail: string;
  interviewDateTime: string;
  duration?: number; // in minutes, default 60
  interviewerEmail?: string;
  notes?: string;
}

class CalendarService {
  private auth: any;
  private calendar: any;

  constructor() {
    this.initializeAuth();
  }

  private async initializeAuth() {
    try {
      const credentialsPath = path.join(process.cwd(), 'attached_assets/client_secret_264185390531-di7joe5ov5eu7927pdd0g09sdupko547.apps.googleusercontent.com_1750734731656.json');
      const tokenPath = path.join(process.cwd(), 'token.json');

      // Check if credentials exist
      await fs.access(credentialsPath);
      const credentials = JSON.parse(await fs.readFile(credentialsPath, 'utf8'));

      const { client_secret, client_id, redirect_uris } = credentials.web;
      this.auth = new google.auth.OAuth2(client_id, client_secret, redirect_uris[0]);

      // Check if token exists and set it
      try {
        await fs.access(tokenPath);
        const token = JSON.parse(await fs.readFile(tokenPath, 'utf8'));
        this.auth.setCredentials(token);
        
        // Initialize calendar API
        this.calendar = google.calendar({ version: 'v3', auth: this.auth });
        
        console.log('Calendar service initialized successfully');
        console.log('Calendar authentication status:', {
          hasAuth: !!this.auth,
          hasCredentials: !!(this.auth && this.auth.credentials),
          hasAccessToken: !!(this.auth && this.auth.credentials && this.auth.credentials.access_token),
          scopes: this.auth.credentials ? this.auth.credentials.scope : 'none'
        });
      } catch (error) {
        console.log('Calendar token not found or invalid. Calendar features will require authentication.');
      }
    } catch (error) {
      console.error('Calendar service initialization failed:', error);
    }
  }

  isAuthenticated(): boolean {
    return this.auth && this.auth.credentials && this.auth.credentials.access_token;
  }

  parseInterviewDateTime(dateTimeStr: string, timezone?: string): { start: Date, end: Date } {
    // Parse various date/time formats
    // Examples: "Aug 10 10am to 11am", "August 10, 2024 10:00 AM", "2024-08-10 10:00"
    
    console.log('Parsing interview datetime:', dateTimeStr, 'with timezone:', timezone);
    
    const now = new Date();
    const currentYear = now.getFullYear();
    let startTime: Date;
    let endTime: Date;

    // Handle "Aug 19 2pm to 3pm" format (with or without day)
    const rangeMatch = dateTimeStr.match(/(\w+)\s+(\d{1,2})\s+(\d{1,2})(am|pm)\s+to\s+(\d{1,2})(am|pm)/i);
    if (rangeMatch) {
      const [, month, day, startHour, startPeriod, endHour, endPeriod] = rangeMatch;
      
      console.log('Parsing range match:', { month, day, startHour, startPeriod, endHour, endPeriod });
      
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                         'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const monthIndex = monthNames.findIndex(m => m.toLowerCase() === month.toLowerCase().substring(0, 3));
      
      if (monthIndex !== -1) {
        let startHour24 = this.convertTo24Hour(parseInt(startHour), startPeriod);
        let endHour24 = this.convertTo24Hour(parseInt(endHour), endPeriod);
        
        console.log('Converted hours:', { startHour24, endHour24 });
        
        // Create dates in the candidate's timezone
        // If timezone is EST, we need to adjust for EST offset
        startTime = new Date(currentYear, monthIndex, parseInt(day), startHour24, 0);
        endTime = new Date(currentYear, monthIndex, parseInt(day), endHour24, 0);
        
        console.log('Initial times (local):', { startTime: startTime.toISOString(), endTime: endTime.toISOString() });
        
        // For timezone-aware calendar events, we need to create the time in the candidate's timezone
        // Check current date to determine if we're in EST (UTC-5) or EDT (UTC-4)
        if (timezone === 'EST' || timezone === 'EDT') {
          // In August, Eastern Time is EDT (UTC-4), not EST (UTC-5)
          // 4pm EDT = 8pm UTC (4pm + 4 hours)
          const offsetHours = 4; // EDT offset from UTC in summer months
          startTime = new Date(startTime.getTime() + offsetHours * 60 * 60 * 1000);
          endTime = new Date(endTime.getTime() + offsetHours * 60 * 60 * 1000);
          console.log('Adjusted for EDT timezone (added 4-hour offset for UTC conversion):', { startTime: startTime.toISOString(), endTime: endTime.toISOString() });
        } else {
          console.log('Times created (will be interpreted in specified timezone):', { startTime: startTime.toISOString(), endTime: endTime.toISOString() });
        }
        
        // If the date is in the past, assume next year
        if (startTime < now) {
          startTime.setFullYear(currentYear + 1);
          endTime.setFullYear(currentYear + 1);
          console.log('Adjusted to next year:', { startTime: startTime.toISOString(), endTime: endTime.toISOString() });
        }
        
        return { start: startTime, end: endTime };
      }
    }

    // Handle single time format "Aug 10 10am"
    const singleMatch = dateTimeStr.match(/(\w+)\s+(\d{1,2})\s+(\d{1,2})(am|pm)/i);
    if (singleMatch) {
      const [, month, day, hour, period] = singleMatch;
      
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                         'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const monthIndex = monthNames.findIndex(m => m.toLowerCase() === month.toLowerCase().substring(0, 3));
      
      if (monthIndex !== -1) {
        const hour24 = this.convertTo24Hour(parseInt(hour), period);
        
        startTime = new Date(currentYear, monthIndex, parseInt(day), hour24, 0);
        endTime = new Date(startTime.getTime() + 60 * 60 * 1000); // Default 1 hour duration
        
        // If the date is in the past, assume next year
        if (startTime < now) {
          startTime.setFullYear(currentYear + 1);
          endTime.setFullYear(currentYear + 1);
        }
        
        return { start: startTime, end: endTime };
      }
    }

    // Default fallback - schedule for next week
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    nextWeek.setHours(10, 0, 0, 0); // 10 AM
    const endNextWeek = new Date(nextWeek.getTime() + 60 * 60 * 1000); // 1 hour later
    
    return { start: nextWeek, end: endNextWeek };
  }

  private convertTo24Hour(hour: number, period: string): number {
    if (period.toLowerCase() === 'pm' && hour !== 12) {
      return hour + 12;
    }
    if (period.toLowerCase() === 'am' && hour === 12) {
      return 0;
    }
    return hour;
  }

  async createInterviewEvent(interviewSlot: InterviewSlot, candidateTimezone?: string): Promise<string | null> {
    // Use Gmail service auth for Calendar operations since they share the same token
    if (!gmailService.isAuthenticated()) {
      console.error('Gmail/Calendar service not authenticated. Please ensure Google API credentials are properly configured.');
      return null;
    }

    // Get auth from Gmail service
    const auth = gmailService.getAuth();
    if (!auth) {
      console.error('Could not get authentication from Gmail service');
      return null;
    }

    // Initialize calendar with shared auth
    this.calendar = google.calendar({ version: 'v3', auth });

    try {
      const { start, end } = this.parseInterviewDateTime(interviewSlot.interviewDateTime, candidateTimezone);
      
      // Map common timezone abbreviations to IANA timezone names
      const timezoneMap = {
        'EST': 'America/New_York',
        'EDT': 'America/New_York', 
        'PST': 'America/Los_Angeles',
        'PDT': 'America/Los_Angeles',
        'CST': 'America/Chicago',
        'CDT': 'America/Chicago',
        'MST': 'America/Denver',
        'MDT': 'America/Denver',
        'UTC': 'UTC',
        'GMT': 'UTC'
      };
      
      const eventTimezone = candidateTimezone && timezoneMap[candidateTimezone] 
        ? timezoneMap[candidateTimezone] 
        : 'America/New_York'; // Default to EST
      
      console.log('Using timezone for calendar event:', eventTimezone, 'from candidate timezone:', candidateTimezone);
      
      const event: CalendarEvent = {
        summary: `Interview: ${interviewSlot.candidateName}`,
        description: `Interview with ${interviewSlot.candidateName}\n\nCandidate Email: ${interviewSlot.candidateEmail}\n\nCandidate Timezone: ${candidateTimezone || 'Not specified'}\n\n${interviewSlot.notes || 'No additional notes provided.'}`,
        start: {
          dateTime: start.toISOString(),
          timeZone: eventTimezone,
        },
        end: {
          dateTime: end.toISOString(),
          timeZone: eventTimezone,
        },
        attendees: [
          {
            email: interviewSlot.candidateEmail,
            displayName: interviewSlot.candidateName,
          },
        ],
        reminders: {
          useDefault: true,
        },
      };

      // Add interviewer if provided
      if (interviewSlot.interviewerEmail) {
        event.attendees.push({
          email: interviewSlot.interviewerEmail,
        });
      }

      console.log('Creating calendar event with attendees:', event.attendees.map(a => a.email));
      console.log('Event details:', {
        summary: event.summary,
        start: event.start.dateTime,
        end: event.end.dateTime,
        attendeeCount: event.attendees.length
      });

      const response = await this.calendar.events.insert({
        calendarId: 'primary',
        resource: event,
        sendUpdates: 'all', // Send email invitations to all attendees
        sendNotifications: true // Explicitly request notifications
      });

      console.log('Calendar event created successfully:', response.data.id);
      console.log('Event response data:', {
        id: response.data.id,
        htmlLink: response.data.htmlLink,
        attendees: response.data.attendees,
        status: response.data.status,
        organizer: response.data.organizer,
        creator: response.data.creator
      });
      
      // Check if attendees received invitations
      if (response.data.attendees) {
        response.data.attendees.forEach((attendee: any, index: number) => {
          console.log(`Attendee ${index + 1}:`, {
            email: attendee.email,
            responseStatus: attendee.responseStatus,
            displayName: attendee.displayName
          });
        });
      }
      
      return response.data.id;
    } catch (error: any) {
      console.error('Error creating calendar event:', error);
      if (error.code === 'ENOTFOUND') {
        console.error('Network error: Unable to reach Google Calendar API');
      } else if (error.status === 401) {
        console.error('Authentication error: Calendar API credentials may be invalid or expired');
      } else if (error.status === 403) {
        console.error('Permission error: Calendar API may not have necessary permissions');
      } else {
        console.error('Calendar API error details:', error.message);
      }
      return null;
    }
  }

  async updateInterviewEvent(eventId: string, interviewSlot: InterviewSlot): Promise<boolean> {
    // Use Gmail service auth for Calendar operations since they share the same token
    if (!gmailService.isAuthenticated()) {
      console.error('Gmail/Calendar service not authenticated');
      return false;
    }

    // Get auth from Gmail service
    const auth = gmailService.getAuth();
    if (!auth) {
      console.error('Could not get authentication from Gmail service');
      return false;
    }

    // Initialize calendar with shared auth
    this.calendar = google.calendar({ version: 'v3', auth });

    try {
      const { start, end } = this.parseInterviewDateTime(interviewSlot.interviewDateTime);
      
      const event: CalendarEvent = {
        summary: `Interview: ${interviewSlot.candidateName}`,
        description: `Interview with ${interviewSlot.candidateName}\n\nCandidate Email: ${interviewSlot.candidateEmail}\n\n${interviewSlot.notes || 'No additional notes provided.'}`,
        start: {
          dateTime: start.toISOString(),
          timeZone: 'America/New_York',
        },
        end: {
          dateTime: end.toISOString(),
          timeZone: 'America/New_York',
        },
        attendees: [
          {
            email: interviewSlot.candidateEmail,
            displayName: interviewSlot.candidateName,
          },
        ],
        reminders: {
          useDefault: true,
        },
      };

      if (interviewSlot.interviewerEmail) {
        event.attendees.push({
          email: interviewSlot.interviewerEmail,
        });
      }

      await this.calendar.events.update({
        calendarId: 'primary',
        eventId: eventId,
        resource: event,
        sendUpdates: 'all',
      });

      console.log('Calendar event updated:', eventId);
      return true;
    } catch (error) {
      console.error('Error updating calendar event:', error);
      return false;
    }
  }

  async deleteInterviewEvent(eventId: string): Promise<boolean> {
    if (!this.isAuthenticated()) {
      console.error('Calendar service not authenticated');
      return false;
    }

    try {
      await this.calendar.events.delete({
        calendarId: 'primary',
        eventId: eventId,
        sendUpdates: 'all',
      });

      console.log('Calendar event deleted:', eventId);
      return true;
    } catch (error) {
      console.error('Error deleting calendar event:', error);
      return false;
    }
  }

  async listUpcomingInterviews(maxResults: number = 10): Promise<any[]> {
    if (!this.isAuthenticated()) {
      console.error('Calendar service not authenticated');
      return [];
    }

    try {
      const now = new Date().toISOString();
      
      const response = await this.calendar.events.list({
        calendarId: 'primary',
        timeMin: now,
        maxResults: maxResults,
        singleEvents: true,
        orderBy: 'startTime',
        q: 'Interview:', // Search for events with "Interview:" in the title
      });

      return response.data.items || [];
    } catch (error) {
      console.error('Error listing upcoming interviews:', error);
      return [];
    }
  }
}

export const calendarService = new CalendarService();
export { CalendarService, InterviewSlot };