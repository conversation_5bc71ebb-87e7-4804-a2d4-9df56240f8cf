
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Briefcase, Target, Users, TrendingUp, ExternalLink, Star, MapPin, Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const JobPostingAgent = () => {
  const [jobDescription, setJobDescription] = useState('');
  const [optimizedDescription, setOptimizedDescription] = useState('');
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [isPosting, setIsPosting] = useState(false);
  const [candidates, setCandidates] = useState<any[]>([]);
  const [apiKey, setApiKey] = useState('');
  const { toast } = useToast();

  const sampleJobDescription = `We are looking for a Senior Frontend Developer to join our team. 

Requirements:
- 5+ years of React experience
- TypeScript proficiency
- Experience with modern build tools
- Strong CSS/HTML skills

Responsibilities:
- Build user interfaces
- Collaborate with design team
- Write clean code
- Mentor junior developers`;

  const optimizeJobDescription = async () => {
    if (!jobDescription.trim()) {
      toast({
        title: "No job description",
        description: "Please enter a job description first",
        variant: "destructive",
      });
      return;
    }

    if (!apiKey) {
      toast({
        title: "API Key Required",
        description: "Please enter your OpenAI API key to optimize the job description",
        variant: "destructive",
      });
      return;
    }

    setIsOptimizing(true);
    console.log("Optimizing job description...");

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are an expert HR recruiter. Rewrite job descriptions to be more engaging, clear, and optimized for LinkedIn. Make them concise but compelling, highlighting company culture and growth opportunities.'
            },
            {
              role: 'user',
              content: `Optimize this job description for maximum visibility and engagement on LinkedIn:\n\n${jobDescription}`
            }
          ],
          temperature: 0.7,
          max_tokens: 800,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const optimized = data.choices[0].message.content;
      
      setOptimizedDescription(optimized);
      toast({
        title: "Job description optimized",
        description: "Your job post has been enhanced for better engagement",
      });
    } catch (error) {
      console.error("Error optimizing job description:", error);
      // Show mock optimized description for demo
      const mockOptimized = `🚀 Senior Frontend Developer - Shape the Future of Web Development!

Join our innovative team and build cutting-edge user experiences that impact millions of users worldwide.

What You'll Do:
✨ Architect scalable React applications with TypeScript
🎨 Collaborate with world-class designers to bring ideas to life  
🚀 Lead technical decisions and mentor growing developers
📈 Drive performance optimizations and best practices

What We're Looking For:
• 5+ years mastering React & TypeScript
• Passion for clean, maintainable code
• Experience with modern tooling (Vite, Webpack, etc.)
• Strong eye for UI/UX details

Why Join Us:
💰 Competitive salary + equity
🏠 Flexible remote/hybrid work
📚 $3K annual learning budget
🌟 Opportunity to lead major initiatives

Ready to level up your career? Apply now!

#FrontendDeveloper #React #TypeScript #TechJobs #RemoteWork`;
      
      setOptimizedDescription(mockOptimized);
      toast({
        title: "Demo: Optimized description created",
        description: "This is a sample optimization (API key needed for real optimization)",
      });
    } finally {
      setIsOptimizing(false);
    }
  };

  const postJob = async () => {
    setIsPosting(true);
    console.log("Posting job to LinkedIn...");

    // Simulate job posting
    setTimeout(() => {
      toast({
        title: "Job posted successfully",
        description: "Your job is now live on LinkedIn and other platforms",
      });
      
      // Simulate finding candidates
      setTimeout(() => {
        const mockCandidates = [
          {
            id: 1,
            name: "Alex Rodriguez",
            title: "Senior Frontend Developer",
            company: "TechCorp",
            experience: "6 years",
            skills: ["React", "TypeScript", "Redux", "GraphQL"],
            score: 95,
            location: "San Francisco, CA",
            availability: "Open to new opportunities"
          },
          {
            id: 2,
            name: "Emily Chen",
            title: "React Developer",
            company: "StartupXYZ",
            experience: "4 years", 
            skills: ["React", "Node.js", "AWS", "MongoDB"],
            score: 88,
            location: "Austin, TX",
            availability: "Actively looking"
          },
          {
            id: 3,
            name: "David Kim",
            title: "Full Stack Engineer",
            company: "InnovateLabs",
            experience: "7 years",
            skills: ["React", "TypeScript", "Python", "Docker"],
            score: 92,
            location: "Remote",
            availability: "Open to remote work"
          }
        ];
        
        setCandidates(mockCandidates);
        toast({
          title: "Candidates found",
          description: `Found ${mockCandidates.length} matching candidates`,
        });
      }, 3000);
      
      setIsPosting(false);
    }, 2000);
  };

  return (
    <div className="space-y-6">
      {/* Job Description Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Briefcase className="w-5 h-5 mr-2" />
            Create & Optimize Job Posting
          </CardTitle>
          <CardDescription>
            AI-powered job description optimization for maximum visibility and engagement
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">OpenAI API Key</label>
            <Input
              type="password"
              placeholder="sk-..."
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="mb-4"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Job Description</label>
            <Textarea
              placeholder={sampleJobDescription}
              value={jobDescription}
              onChange={(e) => setJobDescription(e.target.value)}
              rows={8}
              className="mb-2"
            />
            <Button 
              onClick={() => setJobDescription(sampleJobDescription)}
              variant="outline" 
              size="sm"
            >
              Use Sample Job Description
            </Button>
          </div>

          <div className="flex space-x-4">
            <Select defaultValue="linkedin">
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="linkedin">LinkedIn</SelectItem>
                <SelectItem value="indeed">Indeed</SelectItem>
                <SelectItem value="glassdoor">Glassdoor</SelectItem>
                <SelectItem value="all">All Platforms</SelectItem>
              </SelectContent>
            </Select>

            <Button 
              onClick={optimizeJobDescription}
              disabled={isOptimizing || !jobDescription.trim()}
              className="flex-1"
            >
              {isOptimizing ? (
                <>
                  <TrendingUp className="w-4 h-4 mr-2 animate-pulse" />
                  Optimizing with AI...
                </>
              ) : (
                <>
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Optimize for LinkedIn
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Optimized Description */}
      {optimizedDescription && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Optimized Job Description
            </CardTitle>
            <CardDescription>
              AI-enhanced version optimized for maximum engagement and qualified applicants
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 p-4 rounded-lg border">
              <pre className="whitespace-pre-wrap text-sm">{optimizedDescription}</pre>
            </div>
            
            <div className="flex justify-between items-center">
              <div className="flex space-x-4 text-sm text-gray-600">
                <span>📈 +45% more engaging</span>
                <span>🎯 Better keyword optimization</span>
                <span>⭐ Highlights company culture</span>
              </div>
              
              <Button onClick={postJob} disabled={isPosting}>
                {isPosting ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    Posting...
                  </>
                ) : (
                  <>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Post to LinkedIn
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Candidate Sourcing Results */}
      {candidates.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              AI-Sourced Candidates
            </CardTitle>
            <CardDescription>
              Candidates automatically found and ranked based on job requirements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {candidates.map((candidate) => (
                <Card key={candidate.id} className="p-4">
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="font-semibold text-lg">{candidate.name}</h3>
                      <p className="text-gray-600">{candidate.title} at {candidate.company}</p>
                      <p className="text-sm text-gray-500 flex items-center mt-1">
                        <MapPin className="w-3 h-3 mr-1" />
                        {candidate.location} • {candidate.experience} experience
                      </p>
                    </div>
                    <div className="text-right">
                      <Badge variant="default" className="mb-2">
                        <Star className="w-3 h-3 mr-1" />
                        {candidate.score}% Match
                      </Badge>
                      <p className="text-xs text-gray-500">{candidate.availability}</p>
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <p className="text-sm font-medium mb-2">Skills</p>
                    <div className="flex flex-wrap gap-1">
                      {candidate.skills.map((skill: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button size="sm" className="flex-1">
                      View Profile
                    </Button>
                    <Button size="sm" variant="outline" className="flex-1">
                      Send Message
                    </Button>
                    <Button size="sm" variant="outline">
                      Save for Later
                    </Button>
                  </div>
                </Card>
              ))}
              
              <div className="text-center pt-4">
                <Button variant="outline">
                  <Users className="w-4 h-4 mr-2" />
                  View All {candidates.length + 15} Candidates
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default JobPostingAgent;
