// Comprehensive email domain and address blocklist to prevent delivery failures
export const BLOCKED_DOMAINS = [
  'company.com',
  'testcompany.com', 
  'example.com',
  'test.com',
  'localhost',
  'invalid.com'
];

export const BLOCKED_ADDRESSES = [
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

// High-priority domains that should never receive emails
export const CRITICAL_BLOCKED_DOMAINS = [
  'company.com' // Main problematic domain causing delivery failures
];

export function isEmailBlocked(email: string): boolean {
  const emailLower = email.toLowerCase().trim();
  
  // Empty email check
  if (!emailLower || !emailLower.includes('@')) {
    return true;
  }
  
  // Check blocked addresses (exact match)
  if (BLOCKED_ADDRESSES.includes(emailLower)) {
    logBlockedEmail(emailLower, 'Exact address match in blocklist');
    return true;
  }
  
  // Check critical domains first (highest priority)
  for (const domain of CRITICAL_BLOCKED_DOMAINS) {
    if (emailLower.endsWith(`@${domain}`) || emailLower.includes(domain)) {
      logBlockedEmail(emailLower, `Critical domain blocked: ${domain}`);
      return true;
    }
  }
  
  // Check general blocked domains
  for (const domain of BLOCKED_DOMAINS) {
    if (emailLower.endsWith(`@${domain}`) || emailLower.includes(domain)) {
      logBlockedEmail(emailLower, `Domain blocked: ${domain}`);
      return true;
    }
  }
  
  return false;
}

export function logBlockedEmail(email: string, reason: string): void {
  console.log(`🚫 EMAIL BLOCKED: ${email} - ${reason}`);
  console.log(`   Timestamp: ${new Date().toISOString()}`);
  console.log(`   Action: Email send prevented to avoid delivery failures`);
}