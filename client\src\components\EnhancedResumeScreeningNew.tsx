import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { 
  Upload, 
  FileText, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Linkedin, 
  Star,
  CheckCircle,
  XCircle,
  AlertCircle,
  MessageSquare,
  UserCheck,
  UserX,
  Calendar,
  Building2,
  Target,
  Zap,
  TrendingUp,
  Award,
  Briefcase,
  Home,
  Search,
  ChevronLeft,
  ChevronRight,
  Expand,
  X,
  Download,
  ExternalLink,
  MonitorSpeaker,
  Monitor
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
}

interface ResumeAnalysis {
  overall_score: number;
  match_score: number;
  experience_years: number;
  key_skills: string[];
  matched_skills: string[];
  missing_skills: string[];
  strengths: string[];
  concerns: string[];
  recommendation: 'HIRE' | 'INTERVIEW' | 'REJECT';
  detailed_feedback: string;
  interview_questions: string[];
}

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  department?: string;
  location?: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  aiGeneratedSummary?: string;
}

interface AnalysisResult {
  contactInfo: ContactInfo;
  analysis?: ResumeAnalysis;
  extractedText: string;
  method: string;
  hasJobAnalysis: boolean;
  jobPosting?: JobPosting;
  autoMatchScore?: number;
  matchedJobs?: Array<{
    jobId: string;
    jobTitle: string;
    matchScore: number;
    matchedSkills: string[];
  }>;
}

interface ProcessedCandidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  location?: string;
  matchScore: number;
  recommendation: 'HIRE' | 'INTERVIEW' | 'REJECT';
  skills: string[];
  experience: number;
  avatar: string;
  color: string;
  isSelected: boolean;
  analysisResult?: AnalysisResult;
}

export default function EnhancedResumeScreeningNew() {
  const [file, setFile] = useState<File | null>(null);
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [selectedJobId, setSelectedJobId] = useState<string>('auto');
  const [customJobDescription, setCustomJobDescription] = useState('');
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [processedCandidates, setProcessedCandidates] = useState<ProcessedCandidate[]>([]);
  const [selectedCandidate, setSelectedCandidate] = useState<ProcessedCandidate | null>(null);
  const [selectedTab, setSelectedTab] = useState('all');
  const [candidateTab, setCandidateTab] = useState('analysis');
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    fetchJobPostings();
  }, []);

  const fetchJobPostings = async () => {
    try {
      const response = await fetch('/api/job-postings');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
      }
    } catch (error) {
      console.error('Failed to fetch job postings:', error);
    }
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];
      if (droppedFile.type === 'application/pdf' || 
          droppedFile.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        setFile(droppedFile);
      } else {
        toast({
          title: "Invalid file type",
          description: "Please upload a PDF or Word document",
          variant: "destructive",
        });
      }
    }
  }, [toast]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      setFile(selectedFile);
      toast({
        title: "File selected",
        description: `${selectedFile.name} ready for analysis`,
      });
    }
  };

  const analyzeResume = async () => {
    if (!file) {
      toast({
        title: "No file selected",
        description: "Please upload a resume first",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    const formData = new FormData();
    formData.append('resume', file);
    formData.append('jobId', selectedJobId);
    if (customJobDescription) {
      formData.append('customJobDescription', customJobDescription);
    }

    try {
      const response = await fetch('/api/analyze-resume', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        setAnalysisResult(result);
        
        // Create a new processed candidate
        const candidate: ProcessedCandidate = {
          id: Date.now().toString(),
          name: result.contactInfo.name || 'Unknown',
          email: result.contactInfo.email || '',
          phone: result.contactInfo.phone,
          location: result.contactInfo.location,
          matchScore: result.analysis?.match_score || 0,
          recommendation: result.analysis?.recommendation || 'INTERVIEW',
          skills: result.analysis?.key_skills || [],
          experience: result.analysis?.experience_years || 0,
          avatar: (result.contactInfo.name || 'U')[0].toUpperCase(),
          color: getRandomColor(),
          isSelected: true,
          analysisResult: result
        };

        // Add to processed candidates and select it
        setProcessedCandidates(prev => {
          const updated = prev.map(c => ({ ...c, isSelected: false }));
          return [candidate, ...updated];
        });
        setSelectedCandidate(candidate);

        toast({
          title: "Analysis completed",
          description: "Resume has been analyzed successfully",
        });
      } else {
        throw new Error('Analysis failed');
      }
    } catch (error) {
      console.error('Analysis error:', error);
      toast({
        title: "Analysis failed",
        description: "Failed to analyze resume. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getRandomColor = () => {
    const colors = [
      'bg-blue-500', 'bg-purple-500', 'bg-green-500', 'bg-red-500',
      'bg-yellow-500', 'bg-indigo-500', 'bg-pink-500', 'bg-teal-500'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 text-green-600';
    if (score >= 60) return 'bg-yellow-100 text-yellow-600';
    return 'bg-red-100 text-red-600';
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'HIRE': return 'bg-green-100 text-green-600';
      case 'INTERVIEW': return 'bg-yellow-100 text-yellow-600';
      case 'REJECT': return 'bg-red-100 text-red-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const handleCandidateSelect = (candidate: ProcessedCandidate) => {
    setProcessedCandidates(prev => prev.map(c => ({
      ...c,
      isSelected: c.id === candidate.id
    })));
    setSelectedCandidate(candidate);
  };

  const filteredCandidates = processedCandidates.filter(candidate => {
    if (selectedTab === 'all') return true;
    if (selectedTab === 'hire') return candidate.recommendation === 'HIRE';
    if (selectedTab === 'interview') return candidate.recommendation === 'INTERVIEW';
    if (selectedTab === 'reject') return candidate.recommendation === 'REJECT';
    return true;
  });

  return (
    <div className="flex w-full min-h-screen bg-gray-50">
      {/* Left Panel */}
      <div className="w-[38%] bg-white border-r border-gray-200 pt-6 pl-8 pr-4 pb-8 shadow-md" style={{ minWidth: '480px' }}>
        {/* Header */}
        <div className="flex items-center mb-8">
          <span className="text-2xl font-bold tracking-tight text-black mr-2">recruit</span>
          <div className="flex-1"></div>
          <Button variant="ghost" size="sm" className="p-1">
            <span className="sr-only">Menu</span>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </Button>
        </div>

        {/* Navigation */}
        <div className="flex items-center space-x-8 mb-8">
          <Button variant="ghost" className="flex items-center text-gray-400 hover:text-blue-600 p-0">
            <Home className="w-4 h-4 mr-2" />
            Home
          </Button>
          <Button variant="ghost" className="flex items-center text-gray-400 hover:text-blue-600 p-0">
            <Briefcase className="w-4 h-4 mr-2" />
            Jobs
          </Button>
          <Button variant="ghost" className="flex items-center text-blue-600 border-b-2 border-blue-600 pb-1 p-0">
            <FileText className="w-4 h-4 mr-2" />
            AI Screening
          </Button>
          <Button variant="ghost" className="flex items-center text-gray-400 hover:text-blue-600 p-0">
            <Search className="w-4 h-4 mr-2" />
            Search
          </Button>
        </div>

        {/* Header Section */}
        <div className="mb-6">
          <div className="text-sm font-semibold text-blue-700 mb-4">Resume Analysis</div>
          
          {/* Parallel Columns Layout */}
          <div className="grid grid-cols-2 gap-4">
            
            {/* Resume Upload Column */}
            <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-4" style={{
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <div className="text-sm font-medium text-gray-900 mb-3">Resume Upload</div>
              <div
                className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors ${
                  dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <div className="text-sm font-medium text-gray-900 mb-1">
                  {file ? file.name : 'Upload Resume'}
                </div>
                <p className="text-xs text-gray-500 mb-2">
                  Drag and drop or click to browse
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={handleFileChange}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  size="sm"
                  className="mb-2 text-xs"
                >
                  Choose File
                </Button>
                <Button
                  onClick={analyzeResume}
                  disabled={!file || isAnalyzing}
                  size="sm"
                  className="w-full text-xs"
                >
                  {isAnalyzing ? 'Analyzing...' : 'Analyze Resume'}
                </Button>
              </div>
            </div>

            {/* Job Matching Column */}
            <div className="bg-white/80 backdrop-blur-sm border-0 shadow-lg rounded-xl p-4" style={{
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), inset 0 1px 0 rgba(255, 255, 255, 0.1), inset 0 -1px 0 rgba(0, 0, 0, 0.05)'
            }}>
              <div className="text-sm font-medium text-gray-900 mb-3">Job Matching</div>
              <div className="space-y-3">
                <div>
                  <Label htmlFor="job-select" className="text-xs font-medium text-gray-700 mb-1 block">
                    Select Job Position
                  </Label>
                  <Select value={selectedJobId} onValueChange={setSelectedJobId}>
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Choose a job position" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">Auto-match to all positions</SelectItem>
                      {jobPostings.map((job) => (
                        <SelectItem key={job.id} value={job.id}>
                          {job.title} - {job.department}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-center h-16 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
                  <div className="text-center">
                    <Target className="w-6 h-6 text-blue-500 mx-auto mb-1" />
                    <div className="text-xs text-gray-600">
                      {selectedJobId === 'auto' ? 'Auto-matching enabled' : 'Specific job selected'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Candidate Filter Tabs */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab} className="mb-2">
          <TabsList className="grid w-full grid-cols-4 bg-transparent border-b border-gray-200 rounded-none h-auto p-0">
            <TabsTrigger value="all" className="text-blue-600 border-b-2 border-blue-600 bg-transparent rounded-none pb-2 data-[state=active]:bg-transparent data-[state=active]:text-blue-600">
              All ({processedCandidates.length})
            </TabsTrigger>
            <TabsTrigger value="hire" className="text-gray-400 bg-transparent rounded-none pb-2 data-[state=active]:bg-transparent data-[state=active]:text-blue-600">
              Hire
            </TabsTrigger>
            <TabsTrigger value="interview" className="text-gray-400 bg-transparent rounded-none pb-2 data-[state=active]:bg-transparent data-[state=active]:text-blue-600">
              Interview
            </TabsTrigger>
            <TabsTrigger value="reject" className="text-gray-400 bg-transparent rounded-none pb-2 data-[state=active]:bg-transparent data-[state=active]:text-blue-600">
              Reject
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Candidates List */}
        <div className="overflow-y-auto" style={{ maxHeight: '50vh' }}>
          {filteredCandidates.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p>No candidates analyzed yet</p>
              <p className="text-sm">Upload a resume to get started</p>
            </div>
          ) : (
            filteredCandidates.map((candidate) => (
              <div 
                key={candidate.id} 
                className={`flex items-center py-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 ${
                  candidate.isSelected ? 'bg-gray-50' : ''
                }`}
                onClick={() => handleCandidateSelect(candidate)}
              >
                <div className={`w-10 h-10 rounded-full ${candidate.color} flex items-center justify-center text-white font-medium mr-3`}>
                  {candidate.avatar}
                </div>
                <div className="flex-1">
                  <div className="text-gray-900 font-medium">{candidate.name}</div>
                  <div className="text-xs text-gray-500">{candidate.email}</div>
                </div>
                <div className="text-right">
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full ${getScoreColor(candidate.matchScore)} font-semibold text-sm mb-1`}>
                    {candidate.matchScore}
                  </div>
                  <Badge className={`text-xs ${getRecommendationColor(candidate.recommendation)}`}>
                    {candidate.recommendation}
                  </Badge>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Right Panel */}
      <div className="flex-1 bg-gray-50 flex flex-col items-center justify-start pt-8 pb-8 px-8">
        {selectedCandidate ? (
          <div className="w-full max-w-3xl bg-white rounded-2xl shadow-lg p-8 relative">
            {/* Top Navigation */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-2">
                <Button variant="ghost" className="text-gray-400 hover:text-blue-600 p-2">
                  <ChevronLeft className="w-4 h-4 mr-1" />
                  Previous
                </Button>
                <Button variant="ghost" className="text-gray-400 hover:text-blue-600 p-2">
                  Next
                  <ChevronRight className="w-4 h-4 ml-1" />
                </Button>
              </div>
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                  <Expand className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="sm" className="w-8 h-8 p-0">
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Candidate Header */}
            <div className="flex items-center mb-8">
              <div className={`w-16 h-16 rounded-full ${selectedCandidate.color} flex items-center justify-center text-white font-medium text-xl mr-5`}>
                {selectedCandidate.avatar}
              </div>
              <div>
                <div className="flex items-center space-x-2">
                  <span className="text-xl font-semibold text-gray-900">{selectedCandidate.name}</span>
                  <span className="text-sm text-gray-400">{selectedCandidate.location}</span>
                </div>
                <div className="flex items-center space-x-4 mt-2">
                  <span className="flex items-center text-green-500 text-sm font-medium">
                    <CheckCircle className="w-4 h-4 mr-1" />
                    {selectedCandidate.matchScore}% Match
                  </span>
                  <span className="flex items-center text-gray-400 text-sm font-medium">
                    <Briefcase className="w-4 h-4 mr-1" />
                    {selectedCandidate.experience} years
                  </span>
                  <Badge className={`${getRecommendationColor(selectedCandidate.recommendation)} text-sm`}>
                    {selectedCandidate.recommendation}
                  </Badge>
                </div>
              </div>
              <div className="flex-1"></div>
              <div className="flex items-center space-x-3">
                <Button variant="outline" size="sm" className="flex items-center">
                  <Monitor className="w-4 h-4 mr-2" />
                  Screen
                </Button>
                <Button variant="outline" size="sm" className="flex items-center">
                  <Calendar className="w-4 h-4 mr-2" />
                  Schedule
                </Button>
              </div>
            </div>

            {/* Contact Info Grid */}
            <div className="grid grid-cols-3 gap-6 mb-6">
              <div>
                <div className="text-xs text-gray-400 font-medium mb-1">Email</div>
                <div className="text-sm text-gray-900 font-medium">{selectedCandidate.email}</div>
              </div>
              <div>
                <div className="text-xs text-gray-400 font-medium mb-1">Phone</div>
                <div className="text-sm text-gray-900 font-medium">{selectedCandidate.phone || 'N/A'}</div>
              </div>
              <div>
                <div className="text-xs text-gray-400 font-medium mb-1">Location</div>
                <div className="text-sm text-gray-900 font-medium">{selectedCandidate.location || 'N/A'}</div>
              </div>
            </div>

            {/* Analysis Details Tabs */}
            <Tabs value={candidateTab} onValueChange={setCandidateTab} className="mb-6">
              <TabsList className="grid w-full grid-cols-4 bg-transparent border-b border-gray-200 rounded-none h-auto p-0">
                <TabsTrigger value="analysis" className="text-blue-600 border-b-2 border-blue-600 bg-transparent rounded-none pb-2 data-[state=active]:bg-transparent data-[state=active]:text-blue-600">
                  Analysis
                </TabsTrigger>
                <TabsTrigger value="skills" className="text-gray-400 bg-transparent rounded-none pb-2 data-[state=active]:bg-transparent data-[state=active]:text-blue-600">
                  Skills
                </TabsTrigger>
                <TabsTrigger value="feedback" className="text-gray-400 bg-transparent rounded-none pb-2 data-[state=active]:bg-transparent data-[state=active]:text-blue-600">
                  Feedback
                </TabsTrigger>
                <TabsTrigger value="questions" className="text-gray-400 bg-transparent rounded-none pb-2 data-[state=active]:bg-transparent data-[state=active]:text-blue-600">
                  Questions
                </TabsTrigger>
              </TabsList>

              <TabsContent value="analysis" className="mt-6">
                {selectedCandidate.analysisResult?.analysis && (
                  <div className="space-y-6">
                    {/* Scores */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="text-sm font-semibold text-gray-900 mb-2">Overall Score</div>
                        <div className="flex items-center">
                          <Progress value={selectedCandidate.analysisResult.analysis.overall_score} className="flex-1 mr-3" />
                          <span className="text-sm font-medium">{selectedCandidate.analysisResult.analysis.overall_score}%</span>
                        </div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="text-sm font-semibold text-gray-900 mb-2">Job Match</div>
                        <div className="flex items-center">
                          <Progress value={selectedCandidate.analysisResult.analysis.match_score} className="flex-1 mr-3" />
                          <span className="text-sm font-medium">{selectedCandidate.analysisResult.analysis.match_score}%</span>
                        </div>
                      </div>
                    </div>

                    {/* Strengths */}
                    <div>
                      <div className="text-sm font-semibold text-gray-900 mb-2">Strengths</div>
                      <div className="space-y-1">
                        {selectedCandidate.analysisResult.analysis.strengths.map((strength, index) => (
                          <div key={index} className="flex items-center text-sm text-gray-700">
                            <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                            {strength}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Concerns */}
                    {selectedCandidate.analysisResult.analysis.concerns.length > 0 && (
                      <div>
                        <div className="text-sm font-semibold text-gray-900 mb-2">Concerns</div>
                        <div className="space-y-1">
                          {selectedCandidate.analysisResult.analysis.concerns.map((concern, index) => (
                            <div key={index} className="flex items-center text-sm text-gray-700">
                              <AlertCircle className="w-4 h-4 text-yellow-500 mr-2" />
                              {concern}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="skills" className="mt-6">
                {selectedCandidate.analysisResult?.analysis && (
                  <div className="space-y-6">
                    {/* Matched Skills */}
                    <div>
                      <div className="text-sm font-semibold text-gray-900 mb-2">Matched Skills</div>
                      <div className="flex flex-wrap gap-2">
                        {selectedCandidate.analysisResult.analysis.matched_skills.map((skill, index) => (
                          <Badge key={index} variant="secondary" className="px-3 py-1 rounded-full bg-green-100 text-green-700">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* All Skills */}
                    <div>
                      <div className="text-sm font-semibold text-gray-900 mb-2">All Skills</div>
                      <div className="flex flex-wrap gap-2">
                        {selectedCandidate.analysisResult.analysis.key_skills.map((skill, index) => (
                          <Badge key={index} variant="secondary" className="px-3 py-1 rounded-full bg-gray-100 text-gray-700">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Missing Skills */}
                    {selectedCandidate.analysisResult.analysis.missing_skills.length > 0 && (
                      <div>
                        <div className="text-sm font-semibold text-gray-900 mb-2">Missing Skills</div>
                        <div className="flex flex-wrap gap-2">
                          {selectedCandidate.analysisResult.analysis.missing_skills.map((skill, index) => (
                            <Badge key={index} variant="secondary" className="px-3 py-1 rounded-full bg-red-100 text-red-700">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="feedback" className="mt-6">
                {selectedCandidate.analysisResult?.analysis && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="text-sm font-semibold text-gray-900 mb-2">Detailed Feedback</div>
                    <div className="text-sm text-gray-700 whitespace-pre-wrap">
                      {selectedCandidate.analysisResult.analysis.detailed_feedback}
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="questions" className="mt-6">
                {selectedCandidate.analysisResult?.analysis && (
                  <div>
                    <div className="text-sm font-semibold text-gray-900 mb-4">Suggested Interview Questions</div>
                    <div className="space-y-3">
                      {selectedCandidate.analysisResult.analysis.interview_questions.map((question, index) => (
                        <div key={index} className="flex items-start">
                          <span className="flex-shrink-0 w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                            {index + 1}
                          </span>
                          <div className="text-sm text-gray-700">{question}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </div>
        ) : (
          <div className="w-full max-w-3xl bg-white rounded-2xl shadow-lg p-8 relative">
            <div className="text-center py-12">
              <FileText className="w-16 h-16 mx-auto mb-6 text-gray-300" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Candidate Selected</h3>
              <p className="text-gray-500 mb-6">Upload and analyze a resume to see detailed candidate information</p>
              <Button onClick={() => fileInputRef.current?.click()}>
                Upload Resume
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}