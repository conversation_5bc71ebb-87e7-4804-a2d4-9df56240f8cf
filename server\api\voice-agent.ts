import { Router } from 'express';
import twilio from 'twilio';
import { db } from '../db';
import { voiceCalls, voiceCallNotes, candidates, jobPostings, organizations } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { authenticateToken } from '../auth';
// import { enhancedVoiceCallManager } from '../services/enhancedVoiceCallManager'; // TODO: Restore when needed
import { elevenLabsService } from '../services/elevenlabsService';

const router = Router();

// Initialize Twilio client
const twilioClient = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

const TWILIO_PHONE_NUMBER = process.env.TWILIO_PHONE_NUMBER || '';

/**
 * @swagger
 * /voice-agent/initiate-call/{candidateId}:
 *   post:
 *     summary: Initiate voice call to candidate
 *     description: Start an AI-powered voice call with a candidate for screening or interview
 *     tags: [Voice Agent]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: candidateId
 *         in: path
 *         required: true
 *         description: Candidate ID
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               phoneNumber:
 *                 type: string
 *                 description: Override candidate phone number
 *               scheduledAt:
 *                 type: string
 *                 format: date-time
 *                 description: Schedule call for later
 *               callPurpose:
 *                 type: string
 *                 enum: [technical_interview_scheduling, screening, follow_up]
 *                 default: technical_interview_scheduling
 *     responses:
 *       200:
 *         description: Call initiated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 voiceCallId:
 *                   type: string
 *                   format: uuid
 *                 twilioCallSid:
 *                   type: string
 *                 status:
 *                   type: string
 *       404:
 *         description: Candidate not found
 *       400:
 *         description: Missing phone number or validation error
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post('/initiate-call/:candidateId', authenticateToken, async (req, res) => {
  try {
    const { candidateId } = req.params;
    const { phoneNumber, scheduledAt, callPurpose, personality = 'friendly', useConversationalAI = true } = req.body;
    const userId = (req as any).user.id;
    const organizationId = (req as any).user.organizationId;

    // Verify candidate belongs to organization
    const candidate = await db
      .select()
      .from(candidates)
      .where(and(
        eq(candidates.id, candidateId),
        eq(candidates.organizationId, organizationId)
      ))
      .limit(1);

    if (!candidate.length) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Create voice call record with proper job linking
    const [voiceCall] = await db
      .insert(voiceCalls)
      .values({
        candidateId,
        organizationId,
        jobPostingId: candidate[0].appliedJobId, // CRITICAL FIX: Link call to the job candidate applied for
        initiatedBy: userId || '',
        phoneNumber: phoneNumber || candidate[0].phone,
        scheduledAt: scheduledAt ? new Date(scheduledAt) : new Date(),
        callPurpose: callPurpose || 'technical_interview_scheduling',
        status: 'scheduled'
      })
      .returning();

    // If scheduling for immediate call, initiate Twilio call
    if (!scheduledAt || new Date(scheduledAt) <= new Date()) {
      try {
        const targetPhone = phoneNumber || candidate[0].phone;
        if (!targetPhone) {
          throw new Error('No phone number available for candidate');
        }
        
        let call;

        // TODO: Restore conversational AI when enhancedVoiceCallManager is implemented
        if (false && useConversationalAI && elevenLabsService.isConfigured()) {
          // Use enhanced conversational AI call
          console.log(`🤖 Initiating conversational AI call with ${personality} personality`);

          // const callResponse = await enhancedVoiceCallManager.initiateConversationalCall(
          //   candidateId,
          //   organizationId,
          //   callPurpose || 'technical_interview_scheduling',
          //   {
          //     phoneNumber: targetPhone,
          //     personality,
          //     jobPostingId: candidate[0].appliedJobId || undefined // CRITICAL FIX: Pass job ID for complete context
          //   }
          // );

          // call = await twilioClient.calls.create({
          //   to: targetPhone,
          //   from: TWILIO_PHONE_NUMBER,
          //   twiml: callResponse.twiml,
          //   statusCallback: `${process.env.BASE_URL || 'http://localhost:5000'}/api/voice-agent/conversational-status`,
          //   statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
          //   record: true
          // });
        } else {
          // Use standard call
          console.log(`📞 Initiating standard call`);
          
          call = await twilioClient.calls.create({
            to: targetPhone,
            from: TWILIO_PHONE_NUMBER,
            url: `${process.env.BASE_URL || 'http://localhost:5000'}/api/voice-agent/twiml/${voiceCall.id}`,
            statusCallback: `${process.env.BASE_URL || 'http://localhost:5000'}/api/voice-agent/status-callback`,
            statusCallbackEvent: ['initiated', 'ringing', 'answered', 'completed'],
            record: true,
            recordingStatusCallback: `${process.env.BASE_URL || 'http://localhost:5000'}/api/voice-agent/recording-callback`
          });
        }

        // Update voice call with Twilio SID
        await db
          .update(voiceCalls)
          .set({
            twilioCallSid: call.sid,
            status: 'in_progress',
            startedAt: new Date()
          })
          .where(eq(voiceCalls.id, voiceCall.id));

        const responseData: any = {
          success: true,
          callId: voiceCall.id,
          twilioCallSid: call.sid,
          status: 'initiated'
        };

        // Add conversational AI info if enabled
        if (useConversationalAI && elevenLabsService.isConfigured()) {
          responseData.conversationalAI = {
            enabled: true,
            personality,
            features: [
              'ElevenLabs voice synthesis',
              'Real-time conversational AI',
              'Context-aware responses',
              'Natural conversation flow'
            ]
          };
        }

        res.json(responseData);
      } catch (twilioError) {
        console.error('Twilio call initiation failed:', twilioError);
        
        // Update call status to failed
        await db
          .update(voiceCalls)
          .set({ status: 'failed' })
          .where(eq(voiceCalls.id, voiceCall.id));

        res.status(500).json({
          error: 'Failed to initiate call',
          details: twilioError instanceof Error ? twilioError.message : 'Unknown error'
        });
      }
    } else {
      res.json({
        success: true,
        callId: voiceCall.id,
        status: 'scheduled',
        scheduledAt: voiceCall.scheduledAt
      });
    }
  } catch (error) {
    console.error('Voice call initiation error:', error);
    res.status(500).json({ error: 'Failed to initiate voice call' });
  }
});

// Generate TwiML for voice call conversation
router.post('/twiml/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    
    console.log(`📞 Generating TwiML for call ${callId}`);
    
    // Get call details with complete context
    const [voiceCall] = await db
      .select({
        id: voiceCalls.id,
        candidateId: voiceCalls.candidateId,
        callPurpose: voiceCalls.callPurpose,
        organizationId: voiceCalls.organizationId,
        candidate: {
          name: candidates.fullName,
          email: candidates.email,
          phone: candidates.phone,
          location: candidates.location,
          skills: candidates.skills,
          experienceYears: candidates.experienceYears,
          education: candidates.education,
          currentCompany: candidates.currentCompany,
          currentPosition: candidates.currentPosition,
          matchScore: candidates.matchScore,
          aiSummary: candidates.aiSummary,
          appliedJobId: candidates.appliedJobId
        },
        job: {
          title: jobPostings.title,
          description: jobPostings.description,
          detailedDescription: jobPostings.detailedDescription,
          responsibilities: jobPostings.responsibilities,
          requirements: jobPostings.requirements,
          preferredQualifications: jobPostings.preferredQualifications,
          benefits: jobPostings.benefits,
          companyOverview: jobPostings.companyOverview,
          workEnvironment: jobPostings.workEnvironment,
          salaryRange: jobPostings.salaryRange,
          location: jobPostings.location,
          experienceLevel: jobPostings.experienceLevel,
          skillsRequired: jobPostings.skillsRequired
        },
        organization: {
          name: organizations.name,
          domain: organizations.domain,
          customBranding: organizations.customBranding
        }
      })
      .from(voiceCalls)
      .leftJoin(candidates, eq(voiceCalls.candidateId, candidates.id))
      .leftJoin(jobPostings, eq(candidates.appliedJobId, jobPostings.id))
      .leftJoin(organizations, eq(voiceCalls.organizationId, organizations.id))
      .where(eq(voiceCalls.id, callId))
      .limit(1);

    if (!voiceCall) {
      console.error(`❌ Call ${callId} not found`);
      const errorTwiml = new twilio.twiml.VoiceResponse();
      errorTwiml.say({ voice: 'Polly.Joanna-Neural' }, 'Call information not found. Please try again later.');
      errorTwiml.hangup();
      return res.type('text/xml').send(errorTwiml.toString());
    }

    const candidateName = voiceCall.candidate?.name || 'there';
    const twiml = new twilio.twiml.VoiceResponse();
    
    // Check if ElevenLabs conversational AI is available
    if (elevenLabsService.isConfigured()) {
      try {
        console.log(`🤖 Using ElevenLabs Conversational AI for call ${callId}`);
        console.log('🎯 CONTEXT: Database query results:', {
          candidateName: candidateName,
          callPurpose: voiceCall.callPurpose,
          hasJobDetails: !!voiceCall.job,
          hasOrgDetails: !!voiceCall.organization,
          jobTitle: voiceCall.job?.title || 'NULL',
          jobDescription: voiceCall.job?.description || 'NULL', 
          jobResponsibilities: voiceCall.job?.responsibilities || 'NULL',
          companyName: voiceCall.organization?.name || 'NULL',
          companyDomain: voiceCall.organization?.domain || 'NULL'
        });
        
        // Create conversation state with complete context
        const conversationState = await elevenLabsService.initializeConversation(
          {
            // Complete candidate details
            id: voiceCall.candidateId,
            fullName: candidateName,
            email: voiceCall.candidate?.email || '',
            phone: voiceCall.candidate?.phone || '',
            location: voiceCall.candidate?.location || '',
            skills: voiceCall.candidate?.skills || [],
            experienceYears: voiceCall.candidate?.experienceYears || 0,
            education: voiceCall.candidate?.education || '',
            currentCompany: voiceCall.candidate?.currentCompany || '',
            currentPosition: voiceCall.candidate?.currentPosition || '',
            matchScore: voiceCall.candidate?.matchScore || 0,
            aiSummary: voiceCall.candidate?.aiSummary || '',
            organizationId: voiceCall.organizationId
          },
          // Complete job details
          voiceCall.job ? {
            title: voiceCall.job.title || '',
            description: voiceCall.job.description || '',
            responsibilities: voiceCall.job.responsibilities || '',
            requirements: voiceCall.job.requirements || '',
            preferredQualifications: voiceCall.job.preferredQualifications || '',
            benefits: voiceCall.job.benefits || '',
            workEnvironment: voiceCall.job.workEnvironment || '',
            salaryRange: voiceCall.job.salaryRange || '',
            location: voiceCall.job.location || '',
            experienceLevel: voiceCall.job.experienceLevel || '',
            skillsRequired: voiceCall.job.skillsRequired || []
          } : null,
          // Complete organization details
          voiceCall.organization ? {
            name: voiceCall.organization.name || '',
            domain: voiceCall.organization.domain || '',
            companyOverview: voiceCall.job?.companyOverview || '',
            customBranding: voiceCall.organization.customBranding || {}
          } : null,
          (voiceCall.callPurpose || 'interview_scheduling') as any
        );

        // Generate AI opening message
        const openingResponse = await elevenLabsService.generateOpeningMessage(
          candidateName,
          voiceCall.organization?.name || 'Our Company',
          voiceCall.job?.title || 'the position',
          (voiceCall.callPurpose || 'interview_scheduling') as any,
          'friendly'
        );

        if (openingResponse.success) {
          if (openingResponse.audioUrl) {
            console.log(`🎵 Playing ElevenLabs audio for call ${callId}`);
            twiml.play(openingResponse.audioUrl);
          } else {
            twiml.say({
              voice: 'Polly.Joanna-Neural',
              language: 'en-US'
            }, openingResponse.message || 'Hello, thank you for your time today.');
          }

          // Add gather for conversational input with better error handling
          const gather = twiml.gather({
            input: ['speech'],
            speechTimeout: '5',
            speechModel: 'experimental_conversations',
            enhanced: true,
            action: `/api/voice-agent/conversational-input?voiceCallId=${callId}`,
            method: 'POST',
            language: 'en-US'
          });

          // Pause for response
          twiml.pause({ length: 2 });
          
          // Graceful continuation prompt
          twiml.say({
            voice: 'Polly.Joanna-Neural'
          }, "I'm here whenever you're ready to continue our conversation. Feel free to speak whenever you'd like.");
          
          // End call gracefully after timeout
          twiml.pause({ length: 3 });
          twiml.say({
            voice: 'Polly.Joanna-Neural'
          }, "Thank you for your time today. We'll be in touch soon. Have a great day!");
          
        } else {
          throw new Error('Failed to generate AI opening message');
        }
      } catch (aiError) {
        console.error('❌ ElevenLabs AI failed, using standard greeting:', aiError);
        // Fallback to standard conversation flow
        generateStandardTwiML(twiml, candidateName || 'there', voiceCall.callPurpose || 'interview_scheduling', callId);
      }
    } else {
      console.log(`📞 Using standard TTS for call ${callId}`);
      generateStandardTwiML(twiml, candidateName || 'there', voiceCall.callPurpose || 'interview_scheduling', callId);
    }

    // Always end with hangup
    twiml.hangup();

    // Update call status
    await db
      .update(voiceCalls)
      .set({
        status: 'in_progress',
        startedAt: new Date()
      })
      .where(eq(voiceCalls.id, callId));

    console.log(`✅ TwiML generated successfully for call ${callId}`);
    res.type('text/xml');
    res.send(twiml.toString());
  } catch (error: any) {
    console.error('❌ TwiML generation error:', error);
    
    // Always return valid TwiML even on error
    const errorTwiml = new twilio.twiml.VoiceResponse();
    errorTwiml.say({
      voice: 'Polly.Joanna-Neural'
    }, "I apologize, but we're experiencing technical difficulties. Please try calling back later or contact us directly.");
    errorTwiml.hangup();

    res.type('text/xml').send(errorTwiml.toString());
  }
});

// Helper function for standard TwiML generation
function generateStandardTwiML(twiml: any, candidateName: string, callPurpose: string, callId: string) {
  let greeting = '';
  switch (callPurpose) {
    case 'technical_interview_scheduling':
    case 'interview_scheduling':
      greeting = `Hello ${candidateName}, this is Sarah from the talent acquisition team. I'm calling to schedule your interview. Do you have a few minutes to talk?`;
      break;
    case 'screening':
      greeting = `Hi ${candidateName}, this is Sarah calling about your recent application. I'd like to do a quick screening call with you. Is now a good time?`;
      break;
    case 'follow_up':
      greeting = `Hello ${candidateName}, this is Sarah following up on our previous conversation. How are you doing today?`;
      break;
    default:
      greeting = `Hello ${candidateName}, this is Sarah from the hiring team. Thank you for your interest in our position.`;
  }

  twiml.say({
    voice: 'Polly.Joanna-Neural',
    language: 'en-US'
  }, greeting);

  // Add gather for speech input
  const gather = twiml.gather({
    input: ['speech'],
    speechTimeout: 5,
    action: `/api/voice-agent/speech-input/${callId}`,
    method: 'POST',
    language: 'en-US'
  });

  // Continuation prompts
  twiml.pause({ length: 2 });
  twiml.say({
    voice: 'Polly.Joanna-Neural'
  }, "I'm here whenever you're ready to continue. Please feel free to speak.");
  
  twiml.pause({ length: 3 });
  twiml.say({
    voice: 'Polly.Joanna-Neural'
  }, "Thank you for your time. We'll be in touch soon. Have a great day!");
}

// Handle call status callbacks
router.post('/status-callback', async (req, res) => {
  try {
    const { CallSid, CallStatus, Duration } = req.body;
    
    // Find call by Twilio SID
    const [voiceCall] = await db
      .select()
      .from(voiceCalls)
      .where(eq(voiceCalls.twilioCallSid, CallSid))
      .limit(1);

    if (voiceCall) {
      const updateData: any = { status: CallStatus };
      
      if (CallStatus === 'completed') {
        updateData.endedAt = new Date();
        updateData.durationSeconds = parseInt(Duration) || 0;
      }

      await db
        .update(voiceCalls)
        .set(updateData)
        .where(eq(voiceCalls.id, voiceCall.id));
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Status callback error:', error);
    res.status(500).send('Error');
  }
});

// Handle recording callbacks
router.post('/recording-callback', async (req, res) => {
  try {
    const { CallSid, RecordingUrl } = req.body;
    
    // Find call by Twilio SID and update recording URL
    await db
      .update(voiceCalls)
      .set({ recordingUrl: RecordingUrl })
      .where(eq(voiceCalls.twilioCallSid, CallSid));

    res.status(200).send('OK');
  } catch (error) {
    console.error('Recording callback error:', error);
    res.status(500).send('Error');
  }
});

// Handle conversation ended callback
router.post('/conversation-ended/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    console.log(`Conversation ended for call ${callId}`);

    // Update call status to completed
    await db
      .update(voiceCalls)
      .set({ 
        status: 'completed',
        endedAt: new Date()
      })
      .where(eq(voiceCalls.id, callId));

    // Generate a hangup TwiML to end the call gracefully
    const twiml = new twilio.twiml.VoiceResponse();
    twiml.say({
      voice: 'alice',
      language: 'en-US'
    }, 'Thank you so much for your time! We really enjoyed speaking with you and will be in touch soon with next steps. Have a wonderful day!');
    twiml.hangup();

    res.type('text/xml');
    res.send(twiml.toString());
  } catch (error) {
    console.error('Conversation ended callback error:', error);
    res.status(500).send('Error');
  }
});

// Handle transcription callbacks
router.post('/transcribe-callback/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const { TranscriptionText, TranscriptionStatus } = req.body;

    if (TranscriptionStatus === 'completed' && TranscriptionText) {
      // Update call with transcription
      await db
        .update(voiceCalls)
        .set({ transcription: TranscriptionText })
        .where(eq(voiceCalls.id, callId));

      // Get organization ID from call data
      const call = await db.select()
        .from(voiceCalls)
        .where(eq(voiceCalls.id, callId))
        .limit(1);

      if (call.length > 0) {
        // Create AI-generated notes from transcription
        await db.insert(voiceCallNotes).values({
          callId,
          organizationId: call[0].organizationId,
          noteType: 'follow_up',
          content: `Call transcription: ${TranscriptionText}`,
          aiGenerated: true,
          importance: 3,
          actionRequired: false
        });
      }
    }

    res.status(200).send('OK');
  } catch (error) {
    console.error('Transcription callback error:', error);
    res.status(500).send('Error');
  }
});

// Get call history for a candidate (remove auth for demo)
router.get('/calls/:candidateId', async (req, res) => {
  try {
    const { candidateId } = req.params;
    
    // For demo, get the organization ID from the candidate
    const candidate = await db
      .select({ organizationId: candidates.organizationId })
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);
    
    if (!candidate.length) {
      return res.status(404).json({ error: 'Candidate not found' });
    }
    
    const organizationId = candidate[0].organizationId;

    const calls = await db
      .select({
        id: voiceCalls.id,
        status: voiceCalls.status,
        scheduledAt: voiceCalls.scheduledAt,
        startedAt: voiceCalls.startedAt,
        endedAt: voiceCalls.endedAt,
        durationSeconds: voiceCalls.durationSeconds,
        callPurpose: voiceCalls.callPurpose,
        recordingUrl: voiceCalls.recordingUrl,
        conversationNotes: voiceCalls.conversationNotes,
        callRating: voiceCalls.callRating,
        followUpRequired: voiceCalls.followUpRequired,
        createdAt: voiceCalls.createdAt
      })
      .from(voiceCalls)
      .where(and(
        eq(voiceCalls.candidateId, candidateId),
        eq(voiceCalls.organizationId, organizationId)
      ))
      .orderBy(voiceCalls.createdAt);

    res.json(calls);
  } catch (error) {
    console.error('Get call history error:', error);
    res.status(500).json({ error: 'Failed to fetch call history' });
  }
});

// Get call notes (remove auth for demo)
router.get('/calls/:callId/notes', async (req, res) => {
  try {
    const { callId } = req.params;
    
    // For demo, get the organization ID from the call
    const call = await db
      .select({ organizationId: voiceCalls.organizationId })
      .from(voiceCalls)
      .where(eq(voiceCalls.id, callId))
      .limit(1);
    
    if (!call.length) {
      return res.status(404).json({ error: 'Call not found' });
    }
    
    const organizationId = call[0].organizationId;

    const notes = await db
      .select()
      .from(voiceCallNotes)
      .where(and(
        eq(voiceCallNotes.callId, callId),
        eq(voiceCallNotes.organizationId, organizationId)
      ))
      .orderBy(voiceCallNotes.timestamp);

    res.json(notes);
  } catch (error) {
    console.error('Get call notes error:', error);
    res.status(500).json({ error: 'Failed to fetch call notes' });
  }
});

// Add manual note to call (remove auth for demo)
router.post('/calls/:callId/notes', async (req, res) => {
  try {
    const { callId } = req.params;
    const { noteType, content, importance, actionRequired } = req.body;
    
    // For demo, get the organization ID from the call
    const call = await db
      .select({ organizationId: voiceCalls.organizationId })
      .from(voiceCalls)
      .where(eq(voiceCalls.id, callId))
      .limit(1);
    
    if (!call.length) {
      return res.status(404).json({ error: 'Call not found' });
    }
    
    const organizationId = call[0].organizationId;

    const [note] = await db
      .insert(voiceCallNotes)
      .values({
        callId,
        organizationId,
        noteType,
        content,
        importance: importance || 3,
        actionRequired: actionRequired || false,
        aiGenerated: false
      })
      .returning();

    res.json(note);
  } catch (error) {
    console.error('Add note error:', error);
    res.status(500).json({ error: 'Failed to add note' });
  }
});

/**
 * @swagger
 * /api/voice-agent/conversational-transcript:
 *   post:
 *     summary: Save user transcription from conversational AI
 *     description: Webhook endpoint to receive and save user transcriptions from ElevenLabs/Twilio conversational AI system
 *     tags: [Voice Agent]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - twilioCallSid
 *               - transcript
 *             properties:
 *               twilioCallSid:
 *                 type: string
 *                 description: Twilio Call SID to identify the call
 *               transcript:
 *                 type: string
 *                 description: User's transcribed speech
 *               source:
 *                 type: string
 *                 description: Source of transcription (defaults to 'conversational_ai')
 *                 default: conversational_ai
 *     responses:
 *       200:
 *         description: Transcription saved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 note:
 *                   type: object
 *                   description: The saved note record
 *       404:
 *         description: Call not found
 *       500:
 *         description: Server error
 */
// Save user transcription from conversational AI webhook
router.post('/conversational-transcript', async (req, res) => {
  try {
    const { twilioCallSid, transcript, source = 'conversational_ai' } = req.body;
    
    if (!twilioCallSid || !transcript) {
      return res.status(400).json({ error: 'Missing twilioCallSid or transcript' });
    }

    console.log(`Received conversational transcript for SID ${twilioCallSid}: ${transcript}`);
    
    // Map Twilio SID to our internal call ID
    const call = await db
      .select({ id: voiceCalls.id, organizationId: voiceCalls.organizationId })
      .from(voiceCalls)
      .where(eq(voiceCalls.twilioCallSid, twilioCallSid))
      .limit(1);
    
    if (!call.length) {
      console.error(`Could not find call with Twilio SID: ${twilioCallSid}`);
      return res.status(404).json({ error: 'Call not found' });
    }
    
    const callRecord = call[0];
    
    // Save user transcription note
    const [note] = await db
      .insert(voiceCallNotes)
      .values({
        callId: callRecord.id,
        organizationId: callRecord.organizationId,
        noteType: 'transcription',
        content: `[${source.toUpperCase()}] ${transcript}`,
        aiGenerated: false,
        importance: 3,
        actionRequired: false
      })
      .returning();

    console.log(`Saved user transcription note for call ${callRecord.id}: ${transcript.substring(0, 100)}...`);
    
    res.json({ success: true, note });
  } catch (error) {
    console.error('Save conversational transcript error:', error);
    res.status(500).json({ error: 'Failed to save transcript' });
  }
});

// Update call with manual notes and ratings (remove auth for demo)
router.patch('/calls/:callId', async (req, res) => {
  try {
    const { callId } = req.params;
    const { conversationNotes, callRating, followUpRequired, followUpNotes } = req.body;
    
    // For demo, get the organization ID from the call
    const call = await db
      .select({ organizationId: voiceCalls.organizationId })
      .from(voiceCalls)
      .where(eq(voiceCalls.id, callId))
      .limit(1);
    
    if (!call.length) {
      return res.status(404).json({ error: 'Call not found' });
    }
    
    const organizationId = call[0].organizationId;

    const [updatedCall] = await db
      .update(voiceCalls)
      .set({
        conversationNotes,
        callRating,
        followUpRequired,
        followUpNotes,
        updatedAt: new Date()
      })
      .where(and(
        eq(voiceCalls.id, callId),
        eq(voiceCalls.organizationId, organizationId)
      ))
      .returning();

    if (!updatedCall) {
      return res.status(404).json({ error: 'Call not found' });
    }

    res.json(updatedCall);
  } catch (error) {
    console.error('Update call error:', error);
    res.status(500).json({ error: 'Failed to update call' });
  }
});

/**
 * @swagger
 * /voice-agent/conversational-input:
 *   post:
 *     summary: Handle conversational AI input during calls
 *     description: Process speech input and generate AI responses for ongoing calls
 *     tags: [Voice Agent]
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             properties:
 *               SpeechResult:
 *                 type: string
 *                 description: Transcribed speech from caller
 *               CallSid:
 *                 type: string
 *                 description: Twilio call SID
 *               voiceCallId:
 *                 type: string
 *                 description: Internal voice call ID
 *     responses:
 *       200:
 *         description: TwiML response for continuing conversation
 *         content:
 *           text/xml:
 *             schema:
 *               type: string
 */
router.post('/conversational-input', async (req, res) => {
  try {
    const { SpeechResult, CallSid, voiceCallId } = req.body;
    
    console.log(`🎤 Received speech input: "${SpeechResult}" for call ${CallSid}`);

    if (!SpeechResult || SpeechResult.trim() === '') {
      // No speech detected, continue listening
      const twiml = new (twilio as any).twiml.VoiceResponse();
      twiml.say({
        voice: 'Polly.Joanna-Neural'
      }, "I didn't catch that. Could you please repeat?");
      
      twiml.gather({
        input: ['speech'],
        speechTimeout: 3,
        action: `/api/voice-agent/conversational-input${voiceCallId ? `?voiceCallId=${voiceCallId}` : ''}`,
        method: 'POST'
      });

      res.type('text/xml');
      return res.send(twiml.toString());
    }

    // TODO: Restore conversational AI when enhancedVoiceCallManager is implemented
    // Handle conversational response
    // const callResponse = await enhancedVoiceCallManager.handleConversationalInput(
    //   CallSid,
    //   SpeechResult,
    //   voiceCallId
    // );

    // Fallback response for now
    const twiml = new (twilio as any).twiml.VoiceResponse();
    twiml.say('Thank you for your response. We will get back to you soon.');
    twiml.hangup();

    res.type('text/xml');
    res.send(twiml.toString());

  } catch (error: any) {
    console.error('❌ Error handling conversational input:', error);
    
    // Fallback TwiML
    const twiml = new (twilio as any).twiml.VoiceResponse();
    twiml.say({
      voice: 'Polly.Joanna-Neural'
    }, "I apologize for the technical difficulty. Thank you for your time today.");
    twiml.hangup();

    res.type('text/xml');
    res.send(twiml.toString());
  }
});

/**
 * @swagger
 * /voice-agent/conversational-status:
 *   post:
 *     summary: Handle conversational call status updates
 *     description: Receive status updates for conversational AI calls
 *     tags: [Voice Agent]
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             properties:
 *               CallSid:
 *                 type: string
 *               CallStatus:
 *                 type: string
 *               Direction:
 *                 type: string
 *     responses:
 *       200:
 *         description: Status received
 */
router.post('/conversational-status', async (req, res) => {
  try {
    const { CallSid, CallStatus, Direction } = req.body;
    
    console.log(`📞 Conversational call status update: ${CallSid} - ${CallStatus}`);

    // Find the voice call by Twilio SID
    const [voiceCall] = await db
      .select()
      .from(voiceCalls)
      .where(eq(voiceCalls.twilioCallSid, CallSid));

    if (voiceCall) {
      // Update call status
      await db
        .update(voiceCalls)
        .set({
          status: CallStatus === 'completed' ? 'completed' : 'in_progress',
          endedAt: CallStatus === 'completed' ? new Date() : undefined
        })
        .where(eq(voiceCalls.id, voiceCall.id));

      console.log(`✅ Updated conversational call ${voiceCall.id} status to ${CallStatus}`);
    }

    res.status(200).send('OK');
  } catch (error: any) {
    console.error('❌ Error handling conversational call status:', error);
    res.status(200).send('OK'); // Always respond OK to Twilio
  }
});

/**
 * @swagger
 * /voice-agent/test-conversational-ai:
 *   post:
 *     summary: Test ElevenLabs Conversational AI
 *     description: Test the conversational AI integration without making actual calls
 *     tags: [Voice Agent]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 description: Test message for AI response
 *               personality:
 *                 type: string
 *                 enum: [professional, friendly, warm, authoritative]
 *                 default: friendly
 *     responses:
 *       200:
 *         description: AI response generated successfully
 */
router.post('/test-conversational-ai', async (req, res) => {
  try {
    const { message = "Hello, I'm interested in the position", personality = 'friendly' } = req.body;

    // Test if ElevenLabs is configured
    if (!elevenLabsService.isConfigured()) {
      return res.json({
        error: 'ElevenLabs API key not configured',
        status: 'not_configured'
      });
    }

    // Create a test conversation state
    const testConversationState = await elevenLabsService.initializeConversation(
      {
        fullName: 'Test Candidate',
        currentPosition: 'Software Engineer',
        experienceYears: 3,
        skills: ['JavaScript', 'React', 'Node.js'],
        currentCompany: 'Tech Corp',
        education: 'Computer Science',
        location: 'San Francisco',
        matchScore: 85,
        aiSummary: 'Strong technical candidate with relevant experience'
      },
      {
        title: 'Senior Software Engineer',
        description: 'Join our growing engineering team',
        responsibilities: 'Build scalable web applications',
        requirements: '3+ years experience, JavaScript, React',
        benefits: 'Health insurance, equity, flexible hours',
        workEnvironment: 'Hybrid remote',
        salaryRange: '$120k-$160k'
      },
      {
        name: 'TechCorp Inc',
        domain: 'techcorp.com',
        companyOverview: 'Leading technology company'
      },
      'interview_scheduling'
    );

    // Generate AI response
    const response = await elevenLabsService.generateConversationalResponse(
      message,
      testConversationState,
      { personality }
    );

    res.json({
      success: true,
      input: message,
      personality,
      response: response.response,
      nextAction: response.nextAction,
      hasAudio: !!response.audioUrl,
      conversationStage: response.conversationState?.stage,
      features: {
        elevenlabsConfigured: elevenLabsService.isConfigured(),
        voiceSynthesis: 'ElevenLabs',
        aiModel: 'OpenAI GPT-3.5-turbo',
        personalitySupport: true
      }
    });

  } catch (error: any) {
    console.error('❌ Conversational AI test error:', error);
    res.status(500).json({
      error: error.message,
      status: 'test_failed'
    });
  }
});

export default router;