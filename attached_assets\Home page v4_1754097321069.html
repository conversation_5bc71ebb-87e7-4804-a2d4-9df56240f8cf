<html>
<head>
 <title></title>
 <script src="https://cdn.tailwindcss.com"></script>
 <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
 <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Roboto:wght@400;500&display=swap" rel="stylesheet"/>
 <style>
   body {
     font-family: 'Montserrat', 'Roboto', sans-serif;
   }
   .font-montserrat {
     font-family: 'Montserrat', sans-serif;
   }
   .font-roboto {
     font-family: 'Roboto', sans-serif;
   }
 </style>
</head>
<body class="bg-[#5B7BA6] min-h-screen relative overflow-x-hidden">
 <!-- Header -->
 <div class="absolute top-0 left-0 w-full flex items-center justify-between px-8 pt-6 z-20">
   <div class="flex items-center space-x-4">
     <a href="#" class="flex items-center space-x-2">
      <span class="text-white text-[28px] font-montserrat italic font-bold leading-none">Steorra</span>
     </a>
   </div>
   <div class="flex-1 flex justify-center space-x-12 ml-16">
     <div class="relative group">
       <span class="text-white font-roboto text-sm">Services</span>
     </div>
     <div class="relative group">
       <span class="text-white font-roboto text-sm">Pricing</span>
     </div>
     <div class="relative group">
       <span class="text-white font-roboto text-sm">About Us</span>
     </div>
   </div>
   <div class="flex items-center space-x-6">
     <a class="text-white text-xl" href="#"><i class="fab fa-facebook-f"></i></a>
     <a class="text-white text-xl" href="#"><i class="fab fa-discord"></i></a>
     <a class="text-white font-roboto text-base font-medium" href="#">Sign In</a>
   </div>
 </div>
 <!-- Main Content -->
 <div class="relative flex items-center h-screen px-16 pt-24">
   <!-- Left Section -->
   <div class="flex flex-col justify-start z-10" style="min-width: 500px;">
     <!-- Steorra Logo Top Left -->
     <div class="flex items-center mb-4 mt-2">
       <span class="text-white text-[32px] font-montserrat italic font-normal leading-none"></span>
       <div class="flex -space-x-4 ml-4">
         
       </div>
     </div>
     
     <!-- Flowchart Section -->
     <div class="flex justify-center w-full z-30 relative">
       <div class="bg-[#F6F7F9] rounded-3xl shadow-lg px-6 py-10 w-[430px] mx-auto relative" style="margin-top: 0;">
         <!-- Vertical line -->
         <div class="absolute left-1/2 top-0 h-full w-0.5 bg-[#E5E7EB] z-0" style="transform: translateX(-50%);"></div>
         <!-- Avatars Circle -->
         <div class="flex flex-col items-center relative z-10">
           <div class="rounded-full bg-white shadow-lg flex items-center justify-center w-48 h-48 mb-2 mt-2 relative z-10">
             <div class="grid grid-cols-3 gap-2 w-40 h-40">
               <img alt="Cartoon avatar, woman with red hair and white shirt" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/y0xWxWIdJw5qENLEpEcI2q0VWgYXCCJbUhimCfxbVImFTWjKA/out-0.png" width="60"/>
               <img alt="Cartoon avatar, woman with blue hair and glasses" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/qqgeggkc9MzjeUJgg6nWPL5ezurqZKcvmqbCOVC4qlSUMZNqA/out-0.png" width="60"/>
               <img alt="Cartoon avatar, man with green background and red shirt" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/iCG9HmjoDgpOFRApnNtitqK5RE9nPET1DkPuSpm6qI1iJrRF/out-0.png" width="60"/>
               <img alt="Cartoon avatar, man with blue background and beard" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/eb4He11Q2hpCx0gh9QYwz9vyY45PHprtut587qgh4EbKmsGVA/out-0.png" width="60"/>
               <img alt="Cartoon avatar, man with purple background and glasses" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/u7eALz4YuSRkKqq3CnChqflEscD2fMeVzvelvXXUy3wQxk1oC/out-0.png" width="60"/>
               <img alt="Cartoon avatar, man with yellow background and blue shirt" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/a9GPQmgRf7zzYC9r3BBFeF4NsJZwVjGBeL5kO4iVeqLtYyaUB/out-0.png" width="60"/>
               <img alt="Cartoon avatar, man with yellow background and red shirt" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/HJa3tzMQYHa1MpqiQAu76ldx0UTpvRwuvxi43tKwz8tiJrRF/out-0.png" width="60"/>
               <img alt="Cartoon avatar, woman with yellow background and black hair" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/YhS4eY0whhQhDKVOyVhBfeTeBbwjByNEZCoFUujMh9FqYyaUB/out-0.png" width="60"/>
               <img alt="Cartoon avatar, woman with pink background and dark hair" class="rounded-full w-12 h-12" height="60" src="https://replicate.delivery/xezq/JRSaeSdU14zf7k5sK0WbSoPLHxqa4kMf3Hn8emUbBvHtYyaUB/out-0.png" width="60"/>
             </div>
           </div>
           <!-- Job Requisition -->
           <div class="flex flex-col items-center">
             <div class="bg-white rounded-full shadow-md px-7 py-2 text-gray-900 font-roboto font-medium text-lg mt-2 mb-2 z-10">
               Job Requisition
             </div>
             <div class="w-1 h-6 bg-[#E5E7EB]"></div>
             <!-- AI Automation -->
             <div class="bg-[#23272F] rounded-full shadow-md px-7 py-2 text-white font-roboto font-medium text-lg mb-2 z-10">
               AI Automation
             </div>
             <!-- Automation Steps -->
             <div class="flex flex-wrap justify-center items-center bg-white rounded-full px-2 py-4 shadow-md mb-2 w-[370px]">
               <div class="bg-green-200 text-green-900 font-roboto font-medium text-sm rounded-full px-4 py-1 mx-1 my-1">
                 Job Posting
               </div>
               <div class="bg-blue-300 text-blue-900 font-roboto font-medium text-sm rounded-full px-4 py-1 mx-1 my-1">
                 Resume Parsing
               </div>
               <div class="bg-orange-400 text-white font-roboto font-medium text-sm rounded-full px-4 py-1 mx-1 my-1">
                 Scheduling
               </div>
               <div class="bg-blue-800 text-white font-roboto font-medium text-sm rounded-full px-4 py-1 mx-1 my-1">
                 Screening
               </div>
               <div class="bg-pink-200 text-pink-700 font-roboto font-medium text-sm rounded-full px-4 py-1 mx-1 my-1">
                 Assessment
               </div>
               <div class="bg-emerald-900 text-white font-roboto font-medium text-sm rounded-full px-4 py-1 mx-1 my-1">
                 AI Interview
               </div>
             </div>
             <div class="w-1 h-6 bg-[#E5E7EB]"></div>
             <!-- Human Interview -->
             <div class="bg-white rounded-full shadow-md px-7 py-2 text-gray-900 font-roboto font-medium text-lg mb-2 z-10">
               Human Interview
             </div>
             <div class="w-1 h-6 bg-[#E5E7EB]"></div>
             <!-- Onboarding -->
             <div class="bg-white rounded-full shadow-md px-7 py-2 text-gray-900 font-roboto font-medium text-lg z-10">
               Onboarding
             </div>
           </div>
         </div>
       </div>
     </div>
   </div>
   <!-- Center Image -->
   <div class="absolute right-0 bottom-0 z-0 w-[60vw] h-full pointer-events-none">
     <img alt="Man with brown hair and beard, holding a phone and speaking into it, blue sky background" class="object-cover w-full h-full" height="900" src="https://replicate.delivery/xezq/HWYGN5svpeQuW62zi1mbqO2SLMzfREU6kQpS9eeYzAXzUxaUB/out-0.png" width="900"/>
   </div>
   <!-- Right Cards -->
   <div class="absolute right-16 top-32 flex flex-col space-y-8 z-20">
     <!-- Card 1 -->
     <div class="bg-white rounded-2xl shadow-lg p-6 w-[370px]">
       <div class="flex items-center mb-2">
         <img alt="Profile photo of Nicholas Jones, smiling man" class="w-10 h-10 rounded-full mr-3" height="40" src="https://replicate.delivery/xezq/wLh8Czmm9xLvERfa8nHR6VRxVoAyVfopTaVEEu9O3KgMVsGVA/out-0.png" width="40"/>
         <span class="font-roboto font-medium text-gray-900 mr-2">Nicholas Jones</span>
         <span class="text-xs text-gray-400 font-roboto mr-2">3rd+</span>
         <span class="bg-green-100 text-green-600 text-xs font-roboto px-3 py-1 rounded-full">Distributed Systems</span>
       </div>
       <div class="font-roboto text-gray-800 text-base font-medium mb-1">Senior Software Engineer</div>
       <div class="text-gray-500 text-sm font-roboto mb-2">San Francisco, CA</div>
       <div class="flex mb-2">
         <div class="mr-8">
           <div class="text-xs text-gray-400 font-roboto">Current:</div>
           <div class="text-sm font-roboto font-medium text-gray-900">Software Engineer at OpenAI</div>
         </div>
         <div>
           <div class="text-xs text-gray-400 font-roboto">Past:</div>
           <div class="text-sm font-roboto font-medium text-gray-900">Machine Learning Intern at NVIDIA</div>
         </div>
       </div>
       <div class="text-xs text-gray-400 font-roboto mb-3">Endorsed by 3 managers</div>
       <button class="border border-blue-400 text-blue-500 font-roboto text-base rounded-full px-5 py-1.5 hover:bg-blue-50 transition">
         View Profile
       </button>
     </div>
     <!-- Card 2 -->
     <div class="bg-white rounded-2xl shadow-lg p-6 w-[370px] rotate-3">
       <div class="flex items-center mb-2">
         <img alt="Profile photo of Amanda Patel, woman with dark hair" class="w-10 h-10 rounded-full mr-3" height="40" src="https://replicate.delivery/xezq/86vi3tIu6CaGB1gxnpc7iL5TRq8eeuYhX7dc1BoqIJYMVsGVA/out-0.png" width="40"/>
         <span class="font-roboto font-medium text-gray-900 mr-2">Amanda Patel</span>
         <span class="text-xs text-gray-400 font-roboto mr-2">3rd+</span>
         <span class="bg-pink-100 text-pink-600 text-xs font-roboto px-3 py-1 rounded-full">Machine Learning</span>
       </div>
       <div class="font-roboto text-gray-800 text-base font-medium mb-1">Senior Software Engineer</div>
       <div class="text-gray-500 text-sm font-roboto mb-2">New York, NY</div>
       <div class="flex mb-2">
         <div class="mr-8">
           <div class="text-xs text-gray-400 font-roboto">Current:</div>
           <div class="text-sm font-roboto font-medium text-gray-900">Software Engineer at DeepMind</div>
         </div>
         <div>
           <div class="text-xs text-gray-400 font-roboto">Past:</div>
           <div class="text-sm font-roboto font-medium text-gray-900">AI Research Fellow at Stanford University</div>
         </div>
       </div>
       <div class="text-xs text-gray-400 font-roboto mb-3">Endorsed by 5 managers</div>
       <button class="border border-blue-400 text-blue-500 font-roboto text-base rounded-full px-5 py-1.5 hover:bg-blue-50 transition">
         View Profile
       </button>
     </div>
     <!-- Card 3 -->
     <div class="bg-white rounded-2xl shadow-lg p-6 w-[370px] -rotate-2">
       <div class="flex items-center mb-2">
         <img alt="Profile photo of David Kim, man with glasses" class="w-10 h-10 rounded-full mr-3" height="40" src="https://replicate.delivery/xezq/XuZxeMIBgUQXHq6iFOOVi0ihlmsfQ8LEKNLrg0sGcjkMVsGVA/out-0.png" width="40"/>
         <span class="font-roboto font-medium text-gray-900 mr-2">David Kim</span>
         <span class="text-xs text-gray-400 font-roboto mr-2">3rd+</span>
         <span class="bg-purple-100 text-purple-600 text-xs font-roboto px-3 py-1 rounded-full">Backend Expert</span>
       </div>
       <div class="font-roboto text-gray-800 text-base font-medium mb-1">Software Engineer</div>
       <div class="text-gray-500 text-sm font-roboto mb-2">Seattle, WA</div>
       <div class="flex mb-2">
         <div class="mr-8">
           <div class="text-xs text-gray-400 font-roboto">Current:</div>
           <div class="text-sm font-roboto font-medium text-gray-900">Software Engineer at Anthropic</div>
         </div>
         <div>
           <div class="text-xs text-gray-400 font-roboto">Past:</div>
           <div class="text-sm font-roboto font-medium text-gray-900">Software Developer at Amazon Web Services</div>
         </div>
       </div>
       <div class="text-xs text-gray-400 font-roboto mb-3">Endorsed by 2 managers</div>
       <button class="border border-blue-400 text-blue-500 font-roboto text-base rounded-full px-5 py-1.5 hover:bg-blue-50 transition">
         View Profile
       </button>
     </div>
   </div>
 </div>
</body>
</html>