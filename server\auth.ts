import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { Request, Response, NextFunction } from 'express';
import { db } from './db';
import { authUsers, organizations } from '@shared/schema';
import { eq } from 'drizzle-orm';

// Security: Validate JWT secrets are properly configured
const JWT_SECRET = process.env.JWT_SECRET;
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET;

if (!JWT_SECRET || JWT_SECRET === 'ats-multi-tenant-secret') {
  console.error('🚨 SECURITY WARNING: JWT_SECRET environment variable is missing or using default value! Set a strong secret.');
  if (process.env.NODE_ENV === 'production') {
    throw new Error('JWT_SECRET must be set in production environment');
  }
}

if (!JWT_REFRESH_SECRET || JWT_REFRESH_SECRET === 'ats-refresh-secret') {
  console.error('🚨 SECURITY WARNING: JWT_REFRESH_SECRET environment variable is missing or using default value! Set a strong secret.');
  if (process.env.NODE_ENV === 'production') {
    throw new Error('JWT_REFRESH_SECRET must be set in production environment');
  }
}

// Fallback to defaults only in development
const FINAL_JWT_SECRET = JWT_SECRET || 'ats-multi-tenant-secret';
const FINAL_JWT_REFRESH_SECRET = JWT_REFRESH_SECRET || 'ats-refresh-secret';
const ACCESS_TOKEN_EXPIRES_IN = process.env.ACCESS_TOKEN_EXPIRES_IN || '15m';
const REFRESH_TOKEN_EXPIRES_IN = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d';
const SESSION_EXPIRES_IN = process.env.SESSION_EXPIRES_IN || '24h';
const SALT_ROUNDS = 12;

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    fullName: string;
    role: string;
    organizationId: string;
    organizationName: string;
  };
}

export function hashPassword(password: string): string {
  return bcrypt.hashSync(password, SALT_ROUNDS);
}

export function verifyPassword(password: string, hashedPassword: string): boolean {
  return bcrypt.compareSync(password, hashedPassword);
}

export function generateToken(payload: any): string {
  return jwt.sign(payload, FINAL_JWT_SECRET, { expiresIn: ACCESS_TOKEN_EXPIRES_IN });
}

export function generateRefreshToken(payload: any): string {
  return jwt.sign(payload, FINAL_JWT_REFRESH_SECRET, { expiresIn: REFRESH_TOKEN_EXPIRES_IN });
}

export function verifyToken(token: string): any {
  try {
    const decoded = jwt.verify(token, FINAL_JWT_SECRET);
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Token verification successful for user:', decoded.userId || decoded.sub);
    }
    return decoded;
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ Token verification failed:', {
        error: error.message,
        name: error.name
      });
    }
    
    // Preserve specific error types for better handling
    if (error.name === 'TokenExpiredError') {
      const expiredError = new Error('Token expired');
      expiredError.name = 'TokenExpiredError';
      throw expiredError;
    } else if (error.name === 'JsonWebTokenError') {
      const invalidError = new Error('Invalid token format');
      invalidError.name = 'JsonWebTokenError';
      throw invalidError;
    } else if (error.name === 'NotBeforeError') {
      const notBeforeError = new Error('Token not active yet');
      notBeforeError.name = 'NotBeforeError';
      throw notBeforeError;
    }
    
    throw new Error('Invalid token');
  }
}

export function verifyRefreshToken(token: string): any {
  try {
    return jwt.verify(token, FINAL_JWT_REFRESH_SECRET);
  } catch (error) {
    throw new Error('Invalid refresh token');
  }
}

export function getSessionMaxAge(): number {
  // Convert session expiry to milliseconds
  const timeStr = SESSION_EXPIRES_IN;
  if (timeStr.endsWith('h')) {
    return parseInt(timeStr) * 60 * 60 * 1000;
  } else if (timeStr.endsWith('d')) {
    return parseInt(timeStr) * 24 * 60 * 60 * 1000;
  } else if (timeStr.endsWith('m')) {
    return parseInt(timeStr) * 60 * 1000;
  }
  return 24 * 60 * 60 * 1000; // Default 24 hours
}

export async function authenticateToken(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  let userId: string | null = null;
  let authMethod = 'none';

  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 Authentication started for:', req.method, req.path);
  }

  // Check session first (primary auth method for this app)
  if ((req as any).session?.userId) {
    userId = (req as any).session.userId;
    authMethod = 'session';
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Found userId from session:', userId);
    }
    
    // Touch session to extend expiry on activity
    (req as any).session.touch();
  }

  // Fallback to Bearer token if needed
  if (!userId) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (token) {
      try {
        const decoded = verifyToken(token);
        userId = decoded.userId;
        authMethod = 'jwt';
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ Found userId from JWT token:', userId);
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('❌ Token verification failed:', {
            method: req.method,
            path: req.path,
            error: error.message,
            name: error.name
          });
        }
        
        // Provide specific error responses based on error type
        const baseResponse = {
          ...(process.env.NODE_ENV === 'development' && {
            debug: { method: authMethod, hasSession: !!(req as any).session?.userId }
          })
        };
        
        if (error.name === 'TokenExpiredError') {
          return res.status(401).json({ 
            error: 'Token expired', 
            code: 'TOKEN_EXPIRED',
            message: 'Access token has expired. Please refresh your session.',
            ...baseResponse
          });
        } else if (error.name === 'JsonWebTokenError') {
          return res.status(401).json({ 
            error: 'Invalid token format', 
            code: 'INVALID_TOKEN_FORMAT',
            message: 'The provided token is malformed. Please log in again.',
            ...baseResponse
          });
        }
        
        return res.status(401).json({ 
          error: 'Invalid token',
          code: 'TOKEN_INVALID',
          message: 'Authentication token is invalid. Please log in again.',
          ...baseResponse
        });
      }
    }
  }

  if (!userId) {
    if (process.env.NODE_ENV === 'development') {
      console.log('❌ No userId found in session or token. Session details:', {
        sessionExists: !!(req as any).session,
        sessionUserId: (req as any).session?.userId,
        authHeader: req.headers['authorization'] ? 'present' : 'missing',
        method: req.method,
        path: req.path
      });
    }
    
    return res.status(401).json({ 
      error: 'Authentication required',
      code: 'NO_AUTH',
      message: 'Please log in to continue.',
      ...(process.env.NODE_ENV === 'development' && {
        debug: {
          sessionExists: !!(req as any).session,
          authHeaderPresent: !!req.headers['authorization']
        }
      })
    });
  }

  if (process.env.NODE_ENV === 'development') {
    console.log('Found userId:', userId, 'via', authMethod);
  }

  try {
    // Get user with organization info - use correct table
    const user = await db
      .select({
        id: authUsers.id,
        email: authUsers.email,
        fullName: authUsers.fullName,
        role: authUsers.role,
        organizationId: authUsers.organizationId,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
        organizationName: organizations.name,
      })
      .from(authUsers)
      .leftJoin(organizations, eq(authUsers.organizationId, organizations.id))
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user.length) {
      if (process.env.NODE_ENV === 'development') {
        console.log('User not found for userId:', userId);
      }
      return res.status(401).json({ error: 'User not found' });
    }

    if (!user[0].isActive) {
      return res.status(401).json({ error: 'User account is inactive' });
    }

    // Allow super_admin to bypass approval requirement
    if (!user[0].isApproved && user[0].role !== 'super_admin') {
      return res.status(401).json({ error: 'User account pending approval' });
    }

    req.user = {
      id: user[0].id,
      email: user[0].email,
      fullName: user[0].fullName,
      role: user[0].role,
      organizationId: user[0].organizationId,
      organizationName: user[0].organizationName || '',
      isActive: user[0].isActive,
      isApproved: user[0].isApproved,
    };

    next();
  } catch (error) {
    console.error('Token verification error:', error);
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    return res.status(401).json({ error: 'Invalid token' });
  }
}

export function requireRole(allowedRoles: string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
}

export function requireAdmin(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }

  next();
}

// Middleware to ensure users can only access data from their organization
export function enforceOrganizationAccess(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Add organization filter to query parameters
  req.query.organizationId = req.user.organizationId;
  next();
}