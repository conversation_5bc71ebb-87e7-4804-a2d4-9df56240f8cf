import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import jwt from 'jsonwebtoken';
import { db } from './db';
import { collaborationSessions, annotations, users } from '@shared/schema';
import { eq, and } from 'drizzle-orm';

interface AuthenticatedWebSocket extends WebSocket {
  userId?: string;
  organizationId?: string;
  entityType?: string;
  entityId?: string;
  sessionId?: string;
}

interface CollaborationMessage {
  type: 'annotation_created' | 'annotation_updated' | 'annotation_deleted' | 'user_joined' | 'user_left' | 'cursor_moved' | 'typing_started' | 'typing_stopped';
  payload: any;
  entityType: string;
  entityId: string;
}

class CollaborationWebSocketServer {
  private wss: WebSocketServer;
  private clients: Map<string, AuthenticatedWebSocket> = new Map();
  private rooms: Map<string, Set<string>> = new Map(); // entityType:entityId -> Set of sessionIds

  constructor(server: Server) {
    this.wss = new WebSocketServer({ 
      server, 
      path: '/ws/collaboration',
      verifyClient: async (info: any) => {
        try {
          const url = new URL(info.req.url!, `http://${info.req.headers.host}`);
          const token = url.searchParams.get('token');
          
          if (!token) {
            return false;
          }

          // Verify JWT token
          const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;
          return !!decoded.userId;
        } catch (error) {
          return false;
        }
      }
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    console.log('Collaboration WebSocket server initialized');
  }

  private async handleConnection(ws: AuthenticatedWebSocket, req: any) {
    try {
      const url = new URL(req.url, `http://${req.headers.host}`);
      const token = url.searchParams.get('token');
      const entityType = url.searchParams.get('entityType');
      const entityId = url.searchParams.get('entityId');

      if (!token || !entityType || !entityId) {
        ws.close(1008, 'Missing required parameters');
        return;
      }

      // Verify and decode token
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;
      const userId = decoded.userId;

      // Get user and organization info
      const user = await db
        .select()
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);

      if (!user.length) {
        ws.close(1008, 'User not found');
        return;
      }

      ws.userId = userId;
      ws.organizationId = user[0].organizationId;
      ws.entityType = entityType;
      ws.entityId = entityId;

      // Create collaboration session
      const sessionResult = await db
        .insert(collaborationSessions)
        .values({
          entityType,
          entityId,
          organizationId: user[0].organizationId,
          userId,
          socketId: this.generateSocketId(),
          isActive: true,
        })
        .returning();

      const session = sessionResult[0];
      ws.sessionId = session.id;

      // Add to clients and rooms
      this.clients.set(session.id, ws);
      const roomKey = `${entityType}:${entityId}`;
      if (!this.rooms.has(roomKey)) {
        this.rooms.set(roomKey, new Set());
      }
      this.rooms.get(roomKey)!.add(session.id);

      // Notify other users in the room
      this.broadcastToRoom(roomKey, {
        type: 'user_joined',
        payload: {
          userId,
          userName: user[0].fullName,
          sessionId: session.id,
        },
        entityType,
        entityId,
      }, session.id);

      // Handle messages
      ws.on('message', (data) => this.handleMessage(ws, data));
      ws.on('close', () => this.handleDisconnection(ws));
      ws.on('error', (error) => console.error('WebSocket error:', error));

      // Send confirmation
      ws.send(JSON.stringify({
        type: 'connection_established',
        payload: { sessionId: session.id }
      }));

    } catch (error) {
      console.error('WebSocket connection error:', error);
      ws.close(1011, 'Internal server error');
    }
  }

  private async handleMessage(ws: AuthenticatedWebSocket, data: any) {
    try {
      const message = JSON.parse(data.toString());
      const roomKey = `${ws.entityType}:${ws.entityId}`;

      switch (message.type) {
        case 'cursor_moved':
          await this.updateCursorPosition(ws.sessionId!, message.payload.position);
          this.broadcastToRoom(roomKey, {
            type: 'cursor_moved',
            payload: {
              userId: ws.userId,
              sessionId: ws.sessionId,
              position: message.payload.position,
            },
            entityType: ws.entityType!,
            entityId: ws.entityId!,
          }, ws.sessionId);
          break;

        case 'typing_started':
          this.broadcastToRoom(roomKey, {
            type: 'typing_started',
            payload: {
              userId: ws.userId,
              sessionId: ws.sessionId,
              location: message.payload.location,
            },
            entityType: ws.entityType!,
            entityId: ws.entityId!,
          }, ws.sessionId);
          break;

        case 'typing_stopped':
          this.broadcastToRoom(roomKey, {
            type: 'typing_stopped',
            payload: {
              userId: ws.userId,
              sessionId: ws.sessionId,
            },
            entityType: ws.entityType!,
            entityId: ws.entityId!,
          }, ws.sessionId);
          break;

        default:
          console.warn('Unknown message type:', message.type);
      }
    } catch (error) {
      console.error('Error handling message:', error);
    }
  }

  private async handleDisconnection(ws: AuthenticatedWebSocket) {
    if (!ws.sessionId) return;

    try {
      // Mark session as inactive
      await db
        .update(collaborationSessions)
        .set({ isActive: false })
        .where(eq(collaborationSessions.id, ws.sessionId));

      // Remove from clients and rooms
      this.clients.delete(ws.sessionId);
      const roomKey = `${ws.entityType}:${ws.entityId}`;
      this.rooms.get(roomKey)?.delete(ws.sessionId);

      // Notify other users
      this.broadcastToRoom(roomKey, {
        type: 'user_left',
        payload: {
          userId: ws.userId,
          sessionId: ws.sessionId,
        },
        entityType: ws.entityType!,
        entityId: ws.entityId!,
      });

    } catch (error) {
      console.error('Error handling disconnection:', error);
    }
  }

  private async updateCursorPosition(sessionId: string, position: any) {
    try {
      await db
        .update(collaborationSessions)
        .set({ 
          cursorPosition: position,
          lastActivity: new Date(),
        })
        .where(eq(collaborationSessions.id, sessionId));
    } catch (error) {
      console.error('Error updating cursor position:', error);
    }
  }

  private broadcastToRoom(roomKey: string, message: CollaborationMessage, excludeSessionId?: string) {
    const room = this.rooms.get(roomKey);
    if (!room) return;

    const messageString = JSON.stringify(message);
    
    room.forEach(sessionId => {
      if (sessionId === excludeSessionId) return;
      
      const client = this.clients.get(sessionId);
      if (client && client.readyState === WebSocket.OPEN) {
        client.send(messageString);
      }
    });
  }

  public broadcastAnnotationUpdate(entityType: string, entityId: string, annotation: any, action: 'created' | 'updated' | 'deleted') {
    const roomKey = `${entityType}:${entityId}`;
    this.broadcastToRoom(roomKey, {
      type: `annotation_${action}`,
      payload: annotation,
      entityType,
      entityId,
    });
  }

  private generateSocketId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  public getActiveUsers(entityType: string, entityId: string): string[] {
    const roomKey = `${entityType}:${entityId}`;
    const room = this.rooms.get(roomKey);
    if (!room) return [];

    return Array.from(room).map(sessionId => {
      const client = this.clients.get(sessionId);
      return client?.userId;
    }).filter(Boolean) as string[];
  }
}

export default CollaborationWebSocketServer;