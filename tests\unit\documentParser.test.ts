/**
 * Unit tests for the TypeScript document parser
 * Tests all four main functions to ensure they match Python functionality
 */

import { 
  extractTextFromDocx, 
  extractTextFromPdf, 
  extractContactInfo, 
  analyzeWithOpenAI,
  parseDocument 
} from '../../server/utils/documentParser';
import { ContactInfo } from '../../server/types/documentParser';

// Mock fetch for OpenAI API tests
global.fetch = jest.fn();

describe('DocumentParser', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn(); // Mock console.log
    console.warn = jest.fn(); // Mock console.warn
    console.error = jest.fn(); // Mock console.error
  });

  describe('extractContactInfo', () => {
    it('should extract email addresses correctly', () => {
      const text = 'Contact <NAME_EMAIL> for more information.';
      const result = extractContactInfo(text);
      
      expect(result.email).toBe('<EMAIL>');
    });

    it('should extract phone numbers in various formats', () => {
      const testCases = [
        { text: 'Call me at +1-************', expected: '+1-************' },
        { text: 'Phone: (*************', expected: '(*************' },
        { text: 'Mobile: ************', expected: '************' },
        { text: 'Tel: *************', expected: '*************' }
      ];

      testCases.forEach(({ text, expected }) => {
        const result = extractContactInfo(text);
        expect(result.phone).toBe(expected);
      });
    });

    it('should extract LinkedIn profiles', () => {
      const text = 'Find me on linkedin.com/in/johndoe for professional networking.';
      const result = extractContactInfo(text);
      
      expect(result.linkedin).toBe('linkedin.com/in/johndoe');
    });

    it('should extract location in City, State format', () => {
      const testCases = [
        { text: 'Located in San Francisco, CA', expected: 'San Francisco, CA' },
        { text: 'Based in New York, NY', expected: 'New York, NY' },
        { text: 'Living in Austin, Texas', expected: 'Austin, Texas' }
      ];

      testCases.forEach(({ text, expected }) => {
        const result = extractContactInfo(text);
        expect(result.location).toBe(expected);
      });
    });

    it('should extract name from first few lines', () => {
      const text = `John Doe
Software Engineer
<EMAIL>
************`;
      
      const result = extractContactInfo(text);
      expect(result.name).toBe('John Doe');
    });

    it('should skip header-like lines when extracting name', () => {
      const text = `RESUME
Email: <EMAIL>
John Doe
Software Engineer`;
      
      const result = extractContactInfo(text);
      expect(result.name).toBe('John Doe');
    });

    it('should return null values for empty text', () => {
      const result = extractContactInfo('');
      
      expect(result).toEqual({
        name: null,
        email: null,
        phone: null,
        location: null,
        linkedin: null
      });
    });

    it('should handle text with no contact information', () => {
      const text = 'This is just some random text with no contact details.';
      const result = extractContactInfo(text);
      
      expect(result.email).toBeNull();
      expect(result.phone).toBeNull();
      expect(result.linkedin).toBeNull();
      expect(result.location).toBeNull();
    });
  });

  describe('analyzeWithOpenAI', () => {
    it('should return null when no API key is provided', async () => {
      const result = await analyzeWithOpenAI('test text', '');
      expect(result).toBeNull();
    });

    it('should return null when API key is "none"', async () => {
      const result = await analyzeWithOpenAI('test text', 'none');
      expect(result).toBeNull();
    });

    it('should make correct API call for contact info only', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '{"name":"John Doe","email":"<EMAIL>","phone":"************","location":"San Francisco, CA","linkedin":"linkedin.com/in/johndoe"}'
            }
          }]
        })
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await analyzeWithOpenAI('Resume text here', 'test-api-key');

      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Authorization': 'Bearer test-api-key',
            'Content-Type': 'application/json',
          },
          body: expect.stringContaining('"max_tokens":300')
        })
      );

      expect(result).toEqual({
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '************',
        location: 'San Francisco, CA',
        linkedin: 'linkedin.com/in/johndoe'
      });
    });

    it('should make correct API call for full analysis with job description', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: JSON.stringify({
                contactInfo: {
                  name: 'John Doe',
                  email: '<EMAIL>',
                  phone: '************',
                  location: 'San Francisco, CA',
                  linkedin: 'linkedin.com/in/johndoe'
                },
                analysis: {
                  overall_score: 85,
                  match_score: 78,
                  experience_years: 5,
                  key_skills: ['JavaScript', 'React', 'Node.js'],
                  matched_skills: ['JavaScript', 'React'],
                  missing_skills: ['Python'],
                  strengths: ['Strong frontend experience'],
                  concerns: ['Limited backend experience'],
                  recommendation: 'INTERVIEW',
                  detailed_feedback: 'Good candidate with relevant skills.',
                  interview_questions: ['Tell me about your React experience?']
                }
              })
            }
          }]
        })
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await analyzeWithOpenAI('Resume text here', 'test-api-key', 'Job description here');

      expect(global.fetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/chat/completions',
        expect.objectContaining({
          body: expect.stringContaining('"max_tokens":1000')
        })
      );

      expect(result).toHaveProperty('contactInfo');
      expect(result).toHaveProperty('analysis');
      expect(result?.analysis?.overall_score).toBe(85);
    });

    it('should handle API errors gracefully', async () => {
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      const result = await analyzeWithOpenAI('Resume text here', 'invalid-api-key');
      expect(result).toBeNull();
    });

    it('should handle JSON parsing errors', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: 'Invalid JSON response'
            }
          }]
        })
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await analyzeWithOpenAI('Resume text here', 'test-api-key');
      expect(result).toBeNull();
    });

    it('should clean markdown-formatted JSON responses', async () => {
      const mockResponse = {
        ok: true,
        json: () => Promise.resolve({
          choices: [{
            message: {
              content: '```json\n{"name":"John Doe","email":"<EMAIL>"}\n```'
            }
          }]
        })
      };

      (global.fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await analyzeWithOpenAI('Resume text here', 'test-api-key');
      
      expect(result).toEqual({
        name: 'John Doe',
        email: '<EMAIL>'
      });
    });
  });

  describe('parseDocument', () => {
    it('should handle failed text extraction gracefully', async () => {
      const result = await parseDocument('invalid-base64', 'test.pdf', 'application/pdf');
      
      expect(result.contactInfo).toEqual({
        name: null,
        email: null,
        phone: null,
        location: null,
        linkedin: null
      });
      expect(result.extractedText).toBe('Failed to extract text from test.pdf');
      expect(result.method).toBe('Failed extraction');
      expect(result.hasJobAnalysis).toBe(false);
    });

    it('should use regex fallback when OpenAI fails', async () => {
      // Mock failed OpenAI call
      (global.fetch as jest.Mock).mockResolvedValue({
        ok: false,
        status: 500
      });

      const textWithContact = Buffer.from('John Doe\<EMAIL>\n************').toString('base64');
      const result = await parseDocument(textWithContact, 'test.txt', 'text/plain', 'test-key');
      
      expect(result.method).toBe('TypeScript regex parsing');
      expect(result.contactInfo.name).toBe('John Doe');
      expect(result.contactInfo.email).toBe('<EMAIL>');
    });
  });
});
