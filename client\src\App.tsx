
import React from 'react';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useNavigate, useLocation } from "react-router-dom";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { CandidateProvider } from "@/contexts/CandidateContext";
import AuthPage from "./pages/AuthPage";
import NotFound from "./pages/NotFound";
import EnhancedResumeScreening from "./components/EnhancedResumeScreening";
import EnhancedJobPostings from "./components/EnhancedJobPostings";
import InterviewScheduling from "./components/InterviewScheduling";
import CandidateTracker from "./components/CandidateTracker";
import TileJobSearch from "./components/TileJobSearch";
import SuperAdminPage from "./pages/SuperAdminPage";
import Sidebar from "./components/Sidebar";
import UserManagement from "./components/UserManagement";
import JobPostingCollaboration from "./pages/JobPostingCollaboration";
import InterviewAutomation from "./components/InterviewAutomation";
import CandidateJoinPage from "./components/CandidateJoinPage";
import SubscriptionManager from "./components/SubscriptionManager";
import HomePage from "./pages/HomePage";
import BookDemoPage from "./pages/BookDemoPage";
import LoginPage from "./pages/LoginPage";
import SignupPage from "./pages/SignupPage";
import CreateOrganizationPage from "./pages/CreateOrganizationPage";
import PublicJobPortal from "./pages/PublicJobPortal";
import Dashboard from "./components/Dashboard";
import AccessibilityProvider from "./components/AccessibilityProvider";

const queryClient = new QueryClient();

function AppContent() {
  const { user, isLoading, isAuthenticated, error } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();


  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Handle public routes first (accessible when not logged in)
  if (location.pathname === '/book-demo') {
    return <BookDemoPage />;
  }

  if (location.pathname === '/login') {
    return <LoginPage />;
  }

  if (location.pathname === '/signup') {
    return <SignupPage />;
  }

  if (location.pathname === '/create-organization') {
    return <CreateOrganizationPage />;
  }

  if (location.pathname === '/jobs') {
    return <PublicJobPortal />;
  }

  // Handle home page route (public route - default landing page)
  if (location.pathname === '/' || location.pathname === '/home') {
    return <HomePage />;
  }

  // Handle candidate interview join page (public route)
  if (location.pathname.startsWith('/interview-join')) {
    const urlParams = new URLSearchParams(location.search);
    const interviewId = urlParams.get('interviewId');

    if (!interviewId) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Interview Link</h2>
            <p className="text-gray-600">The interview link is missing required information.</p>
          </div>
        </div>
      );
    }

    return <CandidateJoinPage interviewId={interviewId} />;
  }

  // Handle authentication page - only show when explicitly navigated to /auth
  if (location.pathname === '/auth') {
    if (isAuthenticated && user) {
      // User is already authenticated, redirect to dashboard
      navigate('/dashboard');
      return null; // Return null while redirecting
    }
    // 🎯 PASS SESSION ERROR TO AUTH PAGE
    return <AuthPage sessionError={error} />;
  }

  // For all other routes, check authentication
  if (!isAuthenticated || !user) {
    if (error && error.includes('Session expired')) {
      // 🎯 REDIRECT TO AUTH WITH SESSION ERROR
      navigate('/auth', { replace: true, state: { sessionError: error } });
      return null;
    }
    // Redirect to home page for unauthenticated users trying to access protected routes
    navigate('/', { replace: true });
    return null;
  }

  // Show pending approval message - only if user data is fully loaded and definitely not approved
  if (!user.isApproved && !isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center p-8">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto">
            <h2 className="text-xl font-semibold text-yellow-800 mb-2">Account Pending Approval</h2>
            <p className="text-yellow-700">
              Your account is awaiting admin approval. Please contact your organization administrator.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const getCurrentPage = () => {
    const path = location.pathname;
    if (path === '/') return 'dashboard';
    if (path === '/resume-screening') return 'screening';
    if (path === '/candidate-search') return 'career-site';
    if (path === '/job-postings') return 'job-postings';
    if (path === '/tracker') return 'candidates';
    if (path === '/scheduling') return 'calendar';
    if (path === '/interview-automation') return 'interview-automation';
    if (path === '/users') return 'users';
    if (path === '/sourcing') return 'sourcing';
    if (path === '/onboarding') return 'onboarding';
    if (path === '/super-admin') return 'super-admin';
    if (path === '/subscription') return 'subscription';
    return 'dashboard';
  };

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <div
      className="min-h-screen"
      style={{ backgroundColor: '#FBFAFF' }}
    >
      <Sidebar
        currentPage={getCurrentPage()}
        onNavigate={handleNavigation}
      />
      {/* Main content with left margin to account for fixed sidebar */}
      <div className="ml-64 min-h-screen">
        <main id="main-content" tabIndex={-1} role="main" aria-label="Main content" className="h-full">
          <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/resume-screening" element={<EnhancedResumeScreening />} />
          <Route path="/candidate-search" element={<TileJobSearch />} />
          <Route path="/job-postings" element={<EnhancedJobPostings />} />
          <Route path="/job-postings/:id/collaborate" element={<JobPostingCollaboration />} />
          <Route path="/scheduling" element={<InterviewScheduling />} />
          <Route path="/interview-automation" element={<InterviewAutomation />} />
          <Route path="/tracker" element={<CandidateTracker />} />
          <Route path="/sourcing" element={<div className="container mx-auto px-4 py-8"><h1 className="text-2xl font-bold">Job Sourcing - Coming Soon</h1></div>} />
          <Route path="/users" element={<UserManagement />} />
          <Route path="/onboarding" element={<div className="container mx-auto px-4 py-8"><h1 className="text-2xl font-bold">Onboarding - Coming Soon</h1></div>} />
          <Route path="/super-admin" element={<SuperAdminPage />} />
          <Route path="/subscription" element={<SubscriptionManager />} />
          <Route path="*" element={<NotFound />} />
          </Routes>
        </main>
      </div>
    </div>
  );
}

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <BrowserRouter>
          <AuthProvider>
            <CandidateProvider>
              <AccessibilityProvider>
                <AppContent />
                <Toaster />
                <Sonner />
              </AccessibilityProvider>
            </CandidateProvider>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
