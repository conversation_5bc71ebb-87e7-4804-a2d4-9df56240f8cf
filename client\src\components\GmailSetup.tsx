import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mail, 
  CheckCircle, 
  AlertCircle, 
  ExternalLink,
  RefreshCw,
  Settings
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function GmailSetup() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authUrl, setAuthUrl] = useState('');
  const [authCode, setAuthCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    checkGmailStatus();
  }, []);

  const checkGmailStatus = async () => {
    try {
      const response = await fetch('/api/gmail/status');
      if (response.ok) {
        const data = await response.json();
        setIsAuthenticated(data.authenticated);
      }
    } catch (error) {
      console.error('Error checking Gmail status:', error);
    }
  };

  const getAuthUrl = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/gmail/auth-url');
      if (response.ok) {
        const data = await response.json();
        setAuthUrl(data.authUrl);
        toast({
          title: "Authorization URL generated",
          description: "Click the link to authorize Gmail access",
        });
      } else {
        throw new Error('Failed to get authorization URL');
      }
    } catch (error) {
      console.error('Error getting auth URL:', error);
      toast({
        title: "Error",
        description: "Failed to generate authorization URL",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const submitAuthCode = async () => {
    if (!authCode.trim()) {
      toast({
        title: "Missing code",
        description: "Please enter the authorization code",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/gmail/auth-callback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: authCode })
      });

      if (response.ok) {
        setIsAuthenticated(true);
        setAuthCode('');
        toast({
          title: "Gmail connected",
          description: "Email monitoring is now active",
        });
      } else {
        throw new Error('Failed to authorize Gmail');
      }
    } catch (error) {
      console.error('Error submitting auth code:', error);
      toast({
        title: "Error",
        description: "Failed to authorize Gmail access",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkEmails = async () => {
    setIsChecking(true);
    try {
      const response = await fetch('/api/gmail/check-emails', {
        method: 'POST'
      });

      if (response.ok) {
        toast({
          title: "Email check completed",
          description: "Checked for new candidate responses",
        });
      } else {
        throw new Error('Failed to check emails');
      }
    } catch (error) {
      console.error('Error checking emails:', error);
      toast({
        title: "Error",
        description: "Failed to check emails",
        variant: "destructive"
      });
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="w-5 h-5" />
          Gmail Integration Setup
          {isAuthenticated && (
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="w-3 h-3 mr-1" />
              Connected
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {!isAuthenticated ? (
          <>
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Gmail integration allows automatic monitoring of candidate email responses. 
                You need to authorize access to your Gmail account.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Step 1: Generate Authorization URL</h4>
                <Button
                  onClick={getAuthUrl}
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Settings className="w-4 h-4 mr-2" />
                      Generate Authorization URL
                    </>
                  )}
                </Button>
              </div>

              {authUrl && (
                <div>
                  <h4 className="font-medium mb-2">Step 2: Authorize Gmail Access</h4>
                  <Button
                    onClick={() => window.open(authUrl, '_blank')}
                    variant="outline"
                    className="w-full"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Open Authorization Page
                  </Button>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                    This will open Google's authorization page. Grant permission and copy the authorization code.
                  </p>
                </div>
              )}

              <div>
                <h4 className="font-medium mb-2">Step 3: Enter Authorization Code</h4>
                <div className="space-y-2">
                  <Label htmlFor="authCode">Authorization Code</Label>
                  <Input
                    id="authCode"
                    placeholder="Paste the authorization code here..."
                    value={authCode}
                    onChange={(e) => setAuthCode(e.target.value)}
                  />
                  <Button
                    onClick={submitAuthCode}
                    disabled={isLoading || !authCode.trim()}
                    className="w-full"
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Authorizing...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Complete Authorization
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Gmail integration is active! The system will automatically check for candidate email responses every 30 seconds.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Status:</span>
                  <span className="ml-2 font-medium text-green-600">Connected</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Auto-check:</span>
                  <span className="ml-2 font-medium">Every 30 seconds</span>
                </div>
              </div>

              <Button
                onClick={checkEmails}
                disabled={isChecking}
                variant="outline"
                className="w-full"
              >
                {isChecking ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Checking Emails...
                  </>
                ) : (
                  <>
                    <Mail className="w-4 h-4 mr-2" />
                    Check for New Responses
                  </>
                )}
              </Button>

              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                  How it works:
                </h4>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• Monitors your Gmail for emails with subject "Re: Interview Availability Request"</li>
                  <li>• Automatically extracts candidate responses and preferred times</li>
                  <li>• Updates the scheduling system with availability information</li>
                  <li>• Marks processed emails as read</li>
                </ul>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}