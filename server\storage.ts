import { users, type User, type InsertUser } from "@shared/schema";

// modify the interface with any CRUD methods
// you might need

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  currentId: number;

  constructor() {
    this.users = new Map();
    this.currentId = 1;
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }
}

export const storage = new MemStorage();

// Add some sample job postings for testing
const sampleJobs = [
  {
    id: 1,
    title: "Senior Software Engineer",
    description: "Looking for an experienced software engineer with expertise in React, Node.js, and TypeScript. Must have 5+ years of experience building scalable web applications.",
    requirements: "React, Node.js, TypeScript, 5+ years experience",
    department: "Engineering",
    location: "San Francisco, CA",
    salaryRange: "$120,000 - $160,000",
    employmentType: "Full-time",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 2,
    title: "Product Manager",
    description: "Seeking a product manager to lead our product development initiatives. Experience with agile methodologies and cross-functional team leadership required.",
    requirements: "Product management, Agile, Leadership, 3+ years experience",
    department: "Product",
    location: "Remote",
    salaryRange: "$100,000 - $140,000",
    employmentType: "Full-time",
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Initialize with sample data
sampleJobs.forEach(job => {
  (storage as any).jobs = (storage as any).jobs || new Map();
  (storage as any).jobs.set(job.id, job);
});
