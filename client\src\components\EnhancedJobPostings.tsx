import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TabsContent } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Building2, 
  MapPin, 
  DollarSign, 
  Clock, 
  Users, 
  Target,
  RefreshCw,
  UserCheck,
  Calendar,
  Award,
  ChevronRight,
  ChevronDown,
  Star,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
  Trash2,
  Plus
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import CandidateDetailsModal from './CandidateDetailsModal';
import JobPostingMatches from './JobPostingMatches';
import { JobDescriptionModal } from './JobDescriptionModal';
import { ARIA_LABELS, announceToScreenReader, formatStatusForScreenReader } from '@/utils/accessibility';

interface JobPosting {
  id: string;
  title: string;
  description: string;
  detailedDescription?: string;
  responsibilities?: string;
  requirements?: string;
  preferredQualifications?: string;
  benefits?: string;
  companyOverview?: string;
  workEnvironment?: string;
  growthOpportunities?: string;
  department?: string;
  location?: string;
  salaryRange?: string;
  employmentType: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  educationRequirement?: string;
  keywords?: string[];
  sourcePlatform?: string;
  sourceUrl?: string;
  isActive: boolean;
  candidateCount?: number;
  createdAt: string;
  updatedAt: string;
  lastSynced?: string;
  aiGeneratedSummary?: string;
}

interface Candidate {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  location?: string;
  skills: string[];
  experience?: string;
  experienceYears?: number;
  education?: string;
  status: 'pending_review' | 'approved_for_interview' | 'rejected' | 'hired';
  resumeUrl?: string;
  overallScore?: number;
  matchScore?: number;
  availability?: string;
  notes?: string;
  appliedDate?: string;
  lastContacted?: string;
  interviewDate?: string;
  summary?: string;
  createdAt: string;
  updatedAt: string;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending_review':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved_for_interview':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'hired':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending_review':
      return <AlertCircle className="w-4 h-4" />;
    case 'approved_for_interview':
      return <CheckCircle className="w-4 h-4" />;
    case 'rejected':
      return <XCircle className="w-4 h-4" />;
    case 'hired':
      return <Award className="w-4 h-4" />;
    default:
      return <AlertCircle className="w-4 h-4" />;
  }
};

const getInitials = (name: string) => {
  return name.split(' ').map(n => n[0]).join('').toUpperCase();
};

const getAvatarColor = (name: string) => {
  const colors = [
    'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500', 
    'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
  ];
  const index = name.length % colors.length;
  return colors[index];
};

export default function EnhancedJobPostings() {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [newJob, setNewJob] = useState({
    title: '',
    description: '',
    requirements: '',
    department: '',
    location: '',
    salaryRange: '',
    employmentType: 'full-time',
    skillsRequired: '',
    experienceLevel: '',
    educationRequirement: '',
    keywords: '',
    sourceUrl: '',
    sourcePlatform: ''
  });
  const [jobCandidates, setJobCandidates] = useState<Record<string, Candidate[]>>({});
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const [showCloseConfirm, setShowCloseConfirm] = useState(false);
  const [jobToClose, setJobToClose] = useState<JobPosting | null>(null);
  const { toast } = useToast();

  const handleCreateJob = async () => {
    if (!newJob.title || !newJob.description) {
      toast({
        title: "Error",
        description: "Please fill in required fields (Title and Description)",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    
    try {
      const response = await fetch('/api/job-postings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...newJob,
          skillsRequired: newJob.skillsRequired.split(',').map(s => s.trim()).filter(Boolean),
          keywords: newJob.keywords.split(',').map(s => s.trim()).filter(Boolean),
          isActive: true,
        }),
      });

      if (response.ok) {
        const createdJob = await response.json();
        setJobPostings(prev => [createdJob, ...prev]);
        setIsCreateModalOpen(false);
        setNewJob({
          title: '',
          description: '',
          requirements: '',
          department: '',
          location: '',
          salaryRange: '',
          employmentType: 'full-time',
          skillsRequired: '',
          experienceLevel: '',
          educationRequirement: '',
          keywords: '',
          sourceUrl: '',
          sourcePlatform: ''
        });
        toast({
          title: "Success",
          description: "Job posting created successfully",
        });
        announceToScreenReader('Job posting created successfully', 'polite');
      } else {
        throw new Error('Failed to create job posting');
      }
    } catch (error) {
      console.error('Error creating job posting:', error);
      toast({
        title: "Error",
        description: "Failed to create job posting. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  // Fetch job postings (including closed ones)
  const fetchJobPostings = useCallback(async () => {
    console.log('🔄 [JobPostings] Fetching job postings...');
    setIsLoading(true);
    try {
      const response = await fetch('/api/job-postings/all');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
        console.log('✅ [JobPostings] Loaded:', data.length, 'jobs');
      } else {
        toast({
          title: "Error",
          description: "Failed to fetch job postings",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error fetching job postings:', error);
      toast({
        title: "Error",
        description: "Failed to fetch job postings",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch candidates for a specific job posting
  const fetchJobCandidates = async (jobId: string) => {
    try {
      const response = await fetch(`/api/candidates/match-job/${jobId}`, {
        method: 'POST'
      });
      if (response.ok) {
        const candidates = await response.json();
        setJobCandidates(prev => ({
          ...prev,
          [jobId]: candidates
        }));
      }
    } catch (error) {
      console.error('Error fetching job candidates:', error);
    }
  };



  // Show close confirmation
  const handleClosePositionClick = (job: JobPosting) => {
    setJobToClose(job);
    setShowCloseConfirm(true);
  };

  // Confirm close position
  const confirmClosePosition = async () => {
    if (!jobToClose) return;
    
    try {
      const response = await fetch(`/api/job-postings/${jobToClose.id}/close`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Position closed successfully",
        });
        
        // Refresh job postings
        fetchJobPostings();
      } else {
        toast({
          title: "Error",
          description: "Failed to close position",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error closing position:', error);
      toast({
        title: "Error",
        description: "Failed to close position",
        variant: "destructive"
      });
    } finally {
      setShowCloseConfirm(false);
      setJobToClose(null);
    }
  };

  // Handle reopening a position
  const handleReopenPosition = async (jobId: string) => {
    try {
      const response = await fetch(`/api/job-postings/${jobId}/reopen`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Position reopened successfully",
        });
        
        // Refresh job postings
        fetchJobPostings();
      } else {
        toast({
          title: "Error",
          description: "Failed to reopen position",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error reopening position:', error);
      toast({
        title: "Error",
        description: "Failed to reopen position",
        variant: "destructive"
      });
    }
  };

  // Handle permanent deletion
  const handleDeletePosition = async (jobId: string) => {
    try {
      const response = await fetch(`/api/job-postings/${jobId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        toast({
          title: "Success",
          description: "Position deleted permanently",
        });
        
        // Refresh job postings
        fetchJobPostings();
      } else {
        toast({
          title: "Error",
          description: "Failed to delete position",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error deleting position:', error);
      toast({
        title: "Error",
        description: "Failed to delete position",
        variant: "destructive"
      });
    }
  };

  // Fetch all candidates for all jobs (to show correct counts)
  const fetchAllJobCandidates = useCallback(async () => {
    for (const job of jobPostings) {
      await fetchJobCandidates(job.id);
    }
  }, [jobPostings]);

  // 🎯 ONE-TIME INIT GUARD - Prevents React StrictMode duplicate calls
  const didInitJobPostings = useRef(false);
  useEffect(() => {
    if (didInitJobPostings.current) {
      console.log('🚫 [JobPostings] Init already done, skipping duplicate call');
      return;
    }
    didInitJobPostings.current = true;
    console.log('🔄 [JobPostings] First time init - fetching job postings');
    fetchJobPostings();
  }, [fetchJobPostings]);

  // 🎯 ONE-TIME CANDIDATES FETCH - Prevents duplicate candidate fetching
  const didFetchCandidates = useRef(false);
  useEffect(() => {
    if (jobPostings.length > 0 && !didFetchCandidates.current) {
      didFetchCandidates.current = true;
      console.log('🔄 [JobPostings] Fetching candidates for all', jobPostings.length, 'jobs');
      fetchAllJobCandidates();
    }
  }, [jobPostings, fetchAllJobCandidates]);

  // 🎯 REMOVED INFINITE LOOP - Use event-driven updates instead of polling
  // Listen for candidate addition events to refresh counts only when needed
  useEffect(() => {
    const handleCandidateAdded = (event: CustomEvent) => {
      console.log('🔄 Candidate added event received, refreshing job candidates');
      fetchAllJobCandidates();
    };

    window.addEventListener('candidateAdded', handleCandidateAdded as EventListener);
    return () => window.removeEventListener('candidateAdded', handleCandidateAdded as EventListener);
  }, [fetchAllJobCandidates]);

  // Filter job postings based on search query (default: show all)
  const filteredJobs = jobPostings.filter(job => {
    if (!searchQuery.trim()) return true;
    const q = searchQuery.trim().toLowerCase();
    return (
      (job.title || '').toLowerCase().includes(q) ||
      (job.description || '').toLowerCase().includes(q) ||
      (job.department || '').toLowerCase().includes(q) ||
      (job.location || '').toLowerCase().includes(q) ||
      (job.skillsRequired || []).join(' ').toLowerCase().includes(q)
    );
  });

  // If a job is selected, show the job matching view
  if (selectedJobId) {
    return <JobPostingMatches jobId={selectedJobId} onBack={() => setSelectedJobId(null)} />;
  }

  return (
    <div
      className="h-screen overflow-y-auto"
      style={{ backgroundColor: '#FBFAFF' }}
    >
      <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between pt-[-2px] pb-[-2px] mt-[18px] mb-[18px]">
        <div>
          <h1 className="font-bold text-[24px]" style={{ color: '#0152FF' }}>Job Postings</h1>
        </div>
        <div className="flex items-center gap-3">
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2 hover:bg-blue-50"
                style={{ borderColor: '#0152FF', color: '#0152FF' }}
              >
                <Plus className="w-4 h-4" />
                Create Job
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Job Posting</DialogTitle>
                <DialogDescription>
                  Create a new job posting with AI-powered candidate matching
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Job Title *</Label>
                    <Input
                      id="title"
                      value={newJob.title}
                      onChange={(e) => setNewJob({...newJob, title: e.target.value})}
                      placeholder="Senior Software Engineer"
                    />
                  </div>
                  <div>
                    <Label htmlFor="department">Department</Label>
                    <Input
                      id="department"
                      value={newJob.department}
                      onChange={(e) => setNewJob({...newJob, department: e.target.value})}
                      placeholder="Engineering"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Job Description *</Label>
                  <Textarea
                    id="description"
                    value={newJob.description}
                    onChange={(e) => setNewJob({...newJob, description: e.target.value})}
                    placeholder="Detailed job description..."
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="requirements">Requirements</Label>
                  <Textarea
                    id="requirements"
                    value={newJob.requirements}
                    onChange={(e) => setNewJob({...newJob, requirements: e.target.value})}
                    placeholder="Job requirements..."
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      value={newJob.location}
                      onChange={(e) => setNewJob({...newJob, location: e.target.value})}
                      placeholder="San Francisco, CA"
                    />
                  </div>
                  <div>
                    <Label htmlFor="salaryRange">Salary Range</Label>
                    <Input
                      id="salaryRange"
                      value={newJob.salaryRange}
                      onChange={(e) => setNewJob({...newJob, salaryRange: e.target.value})}
                      placeholder="$120k - $180k"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="employmentType">Employment Type</Label>
                    <Select value={newJob.employmentType} onValueChange={(value) => setNewJob({...newJob, employmentType: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select employment type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full-time">Full-time</SelectItem>
                        <SelectItem value="part-time">Part-time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="internship">Internship</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="experienceLevel">Experience Level</Label>
                    <Input
                      id="experienceLevel"
                      value={newJob.experienceLevel}
                      onChange={(e) => setNewJob({...newJob, experienceLevel: e.target.value})}
                      placeholder="Senior, Mid-level, Junior"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="skillsRequired">Skills Required (comma-separated)</Label>
                  <Input
                    id="skillsRequired"
                    value={newJob.skillsRequired}
                    onChange={(e) => setNewJob({...newJob, skillsRequired: e.target.value})}
                    placeholder="React, Node.js, Python, AWS"
                  />
                </div>

                <div>
                  <Label htmlFor="keywords">Keywords (comma-separated)</Label>
                  <Input
                    id="keywords"
                    value={newJob.keywords}
                    onChange={(e) => setNewJob({...newJob, keywords: e.target.value})}
                    placeholder="Software Engineer, Full Stack, Backend"
                  />
                </div>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                  Cancel
                </Button>
                <Button type="button" onClick={handleCreateJob} disabled={isCreating}>
                  {isCreating ? 'Creating...' : 'Create Job'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <Button
            onClick={() => window.location.reload()}
            variant="outline"
            className="flex items-center gap-2 hover:bg-blue-50"
            style={{ borderColor: '#0152FF', color: '#0152FF' }}
          >
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Search & Controls */}
      <div className="mb-6 flex items-center justify-between gap-4">
        <div className="flex items-center gap-3 w-full max-w-2xl">
          <Input
            placeholder="Search job postings by title, location, department, or skill..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
            aria-label={ARIA_LABELS.search}
          />
        </div>
    
      </div>

      <div className="space-y-4">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading job postings...</p>
            </div>
          ) : filteredJobs.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No job postings found</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredJobs.map((job) => (
              <Card key={job.id} className="hover:shadow-lg transition-shadow border border-gray-200 flex flex-col">
                <CardContent className="p-6 flex-1 bg-[#ffffff00]">
                  {/* Job Title and Status */}
                  <div className="flex items-center gap-3 mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 flex-1">{job.title}</h3>
                    <Badge 
                      variant={job.isActive ? "default" : "secondary"}
                      className={job.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}
                    >
                      {job.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  
                  {/* Job Information - Stacked layout */}
                  <div className="space-y-2 text-sm text-gray-600 mb-4">
                    {job.department && (
                      <div className="flex items-center gap-1">
                        <Building2 className="w-4 h-4" />
                        {job.department}
                      </div>
                    )}
                    {job.location && (
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        {job.location}
                      </div>
                    )}
                    <div className="flex items-center gap-4">
                      {job.salaryRange && (
                        <div className="flex items-center gap-1">
                          <DollarSign className="w-4 h-4" />
                          {job.salaryRange}
                        </div>
                      )}
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {job.employmentType}
                      </div>
                    </div>
                  </div>

                  {/* Skills Tags */}
                  {job.skillsRequired && job.skillsRequired.length > 0 && (
                    <div className="flex flex-wrap gap-2 mb-6">
                      {job.skillsRequired.slice(0, 4).map((skill, index) => (
                        <Badge key={index} variant="outline" className="text-xs bg-gray-50 text-gray-700">
                          {skill}
                        </Badge>
                      ))}
                      {job.skillsRequired.length > 4 && (
                        <Badge variant="outline" className="text-xs bg-gray-50 text-gray-700">
                          +{job.skillsRequired.length - 4} more
                        </Badge>
                      )}
                    </div>
                  )}
                  
                  {/* Action Buttons - Moved to bottom */}
                  <div className="flex flex-wrap gap-2 mt-auto pt-4 border-t border-gray-100">
                    <Button
                      onClick={() => setSelectedJobId(job.id)}
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1 text-xs border-blue-200 hover:bg-blue-50 text-[#141417] font-medium"
                    >
                      <Users className="w-3 h-3" />
                      View Details
                    </Button>
                    
                    <JobDescriptionModal 
                      job={job}
                      trigger={
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-1 text-xs border-green-200 hover:bg-green-50 text-[#141417] font-medium"
                        >
                          <Target className="w-3 h-3" />
                          View JD
                        </Button>
                      }
                    />
                    
                    {job.isActive ? (
                      <Button
                        onClick={() => handleClosePositionClick(job)}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-1 text-xs text-red-600 border-red-200 hover:bg-red-50"
                      >
                        <XCircle className="w-3 h-3" />
                        Close Position
                      </Button>
                    ) : (
                      <>
                        <Button
                          onClick={() => handleReopenPosition(job.id)}
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-1 text-xs text-green-600 border-green-200 hover:bg-green-50"
                        >
                          <CheckCircle className="w-3 h-3" />
                          Reopen
                        </Button>
                        <Button
                          onClick={() => handleDeletePosition(job.id)}
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-1 text-xs text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <Trash2 className="w-3 h-3" />
                          Delete
                        </Button>
                      </>
                    )}
                    
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-1 text-xs"
                      disabled
                    >
                      <Users className="w-3 h-3" />
                      {jobCandidates[job.id]?.length || 0} Candidates
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
            </div>
          )}
      </div>

      {/* Candidate Details Modal */}
      {selectedCandidate && (
        <CandidateDetailsModal
          candidate={selectedCandidate}
          isOpen={!!selectedCandidate}
          onClose={() => setSelectedCandidate(null)}
        />
      )}

      {/* Close Position Confirmation Dialog */}
      <Dialog open={showCloseConfirm} onOpenChange={setShowCloseConfirm}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Close Position</DialogTitle>
            <DialogDescription>
              Are you sure you want to close the position "{jobToClose?.title}"? 
              This will move the position to the closed tab where you can reopen it later or delete it permanently.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setShowCloseConfirm(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmClosePosition}
            >
              Close Position
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </div>
  );
}