import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CollaborationAnnotations from '@/components/CollaborationAnnotations';
import PositionedAnnotations from '@/components/PositionedAnnotations';
import { useCollaboration } from '@/hooks/useCollaboration';
import { 
  ArrowLeft, 
  MapPin, 
  DollarSign, 
  Clock, 
  Users, 
  MessageCircle,
  Eye,
  Edit,
  Share
} from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock job posting data - in a real app, this would come from an API
const mockJobPosting = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  title: 'Senior Full Stack Developer',
  company: 'TechCorp Inc.',
  location: 'San Francisco, CA',
  salaryRange: '$120,000 - $160,000',
  employmentType: 'Full-time',
  department: 'Engineering',
  postedDate: '2024-01-15',
  description: `We are seeking a talented Senior Full Stack Developer to join our growing engineering team. 

Key Responsibilities:
• Develop and maintain web applications using modern frameworks
• Collaborate with cross-functional teams to deliver high-quality software
• Participate in code reviews and architectural discussions
• Mentor junior developers and contribute to team growth

Required Qualifications:
• 5+ years of experience in full-stack development
• Proficiency in React, Node.js, and TypeScript
• Experience with cloud platforms (AWS, Azure, or GCP)
• Strong understanding of database design and optimization
• Excellent communication and problem-solving skills

Preferred Qualifications:
• Experience with microservices architecture
• Knowledge of DevOps practices and CI/CD pipelines
• Previous experience in a leadership or mentoring role`,
  requirements: [
    '5+ years of full-stack development experience',
    'React, Node.js, TypeScript expertise',
    'Cloud platform experience (AWS/Azure/GCP)',
    'Database design and optimization',
    'Strong communication skills'
  ],
  benefits: [
    'Competitive salary and equity package',
    'Comprehensive health, dental, and vision insurance',
    'Flexible work arrangements',
    'Professional development budget',
    'Unlimited PTO policy'
  ],
  status: 'active'
};

export default function JobPostingCollaboration() {
  const { id } = useParams();
  const [selectedAnnotation, setSelectedAnnotation] = useState<any>(null);
  
  // Initialize collaboration features
  const collaboration = useCollaboration({
    entityType: 'job_posting',
    entityId: id || mockJobPosting.id,
    enabled: true
  });

  // Mock positioned annotations for demo
  const positionedAnnotations = [
    {
      id: '1',
      content: 'Should we increase the salary range to be more competitive?',
      type: 'question' as const,
      position: { x: 300, y: 150, selectedText: '$120,000 - $160,000' },
      authorName: 'Sarah Johnson',
      createdAt: new Date().toISOString()
    },
    {
      id: '2',
      content: 'This requirement might be too strict for our market',
      type: 'concern' as const,
      position: { x: 200, y: 250, selectedText: '5+ years of experience' },
      authorName: 'Mike Chen',
      createdAt: new Date().toISOString()
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link to="/job-postings">
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  Back to Job Postings
                </Button>
              </Link>
              <div className="h-6 border-l border-gray-300" />
              <h1 className="text-xl font-semibold text-gray-900">
                Collaborative Job Review
              </h1>
            </div>
            
            <div className="flex items-center gap-3">
              {collaboration.isConnected && (
                <div className="flex items-center gap-2 text-sm text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  Live collaboration active
                </div>
              )}
              
              {collaboration.activeUsers.length > 0 && (
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    {collaboration.activeUsers.length} viewing
                  </span>
                </div>
              )}
              
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Share className="w-4 h-4" />
                Share
              </Button>
              
              <Button size="sm" className="flex items-center gap-2">
                <Edit className="w-4 h-4" />
                Edit Job
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Job Details */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Job Header */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl font-bold mb-2">
                      {mockJobPosting.title}
                    </CardTitle>
                    <div className="flex items-center gap-4 text-gray-600">
                      <span className="font-medium">{mockJobPosting.company}</span>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        {mockJobPosting.location}
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-4 h-4" />
                        {mockJobPosting.salaryRange}
                      </div>
                    </div>
                  </div>
                  <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                    {mockJobPosting.status}
                  </Badge>
                </div>
              </CardHeader>
            </Card>

            {/* Job Content with Positioned Annotations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Job Description
                  <Badge variant="secondary" className="ml-2">
                    Click or select text to annotate
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <PositionedAnnotations
                  entityType="job_posting"
                  entityId={mockJobPosting.id}
                  annotations={positionedAnnotations}
                  onAnnotationSelect={setSelectedAnnotation}
                >
                  <div className="prose max-w-none">
                    <div className="whitespace-pre-wrap text-gray-700 leading-relaxed">
                      {mockJobPosting.description}
                    </div>
                    
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">Requirements</h3>
                      <ul className="space-y-2">
                        {mockJobPosting.requirements.map((req, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                            <span className="text-gray-700">{req}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="mt-6">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">Benefits</h3>
                      <ul className="space-y-2">
                        {mockJobPosting.benefits.map((benefit, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                            <span className="text-gray-700">{benefit}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </PositionedAnnotations>
              </CardContent>
            </Card>
          </div>

          {/* Collaboration Sidebar */}
          <div className="space-y-6">
            
            {/* Job Meta Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Job Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Posted</p>
                    <p className="font-medium">{new Date(mockJobPosting.postedDate).toLocaleDateString()}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-600">Department</p>
                    <p className="font-medium">{mockJobPosting.department}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    {mockJobPosting.employmentType}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Real-time Collaboration Panel */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageCircle className="w-5 h-5" />
                  Team Collaboration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CollaborationAnnotations 
                  entityType="job_posting" 
                  entityId={mockJobPosting.id}
                />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}