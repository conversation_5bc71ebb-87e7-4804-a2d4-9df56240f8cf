
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Settings, Key, Check, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface ApiKeySettingsProps {
  apiKey: string;
  onApiKeyChange: (key: string) => void;
}

const ApiKeySettings: React.FC<ApiKeySettingsProps> = ({ apiKey, onApiKeyChange }) => {
  const [tempKey, setTempKey] = useState(apiKey);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    // Load saved API key from localStorage on component mount
    const savedKey = localStorage.getItem('openai_api_key');
    if (savedKey && !apiKey) {
      onApiKeyChange(savedKey);
      setTempKey(savedKey);
    }
  }, []);

  const saveApiKey = () => {
    if (!tempKey.trim()) {
      toast({
        title: "Invalid API Key",
        description: "Please enter a valid OpenAI API key",
        variant: "destructive",
      });
      return;
    }

    if (!tempKey.startsWith('sk-')) {
      toast({
        title: "Invalid API Key Format",
        description: "OpenAI API keys should start with 'sk-'",
        variant: "destructive",
      });
      return;
    }

    setIsSaving(true);
    
    // Save to localStorage for persistence
    localStorage.setItem('openai_api_key', tempKey);
    onApiKeyChange(tempKey);
    
    setTimeout(() => {
      setIsSaving(false);
      toast({
        title: "API Key Saved",
        description: "Your OpenAI API key has been saved securely",
      });
    }, 500);
  };

  const clearApiKey = () => {
    localStorage.removeItem('openai_api_key');
    setTempKey('');
    onApiKeyChange('');
    toast({
      title: "API Key Cleared",
      description: "Your OpenAI API key has been removed",
    });
  };

  const isKeyValid = apiKey && apiKey.startsWith('sk-');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            API Key Settings
          </span>
          {isKeyValid && (
            <Badge variant="default" className="bg-green-100 text-green-800">
              <Check className="w-3 h-3 mr-1" />
              Configured
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Configure your OpenAI API key for resume analysis. Your key is stored locally and securely.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">OpenAI API Key</label>
          <div className="flex space-x-2">
            <Input
              type="password"
              placeholder="sk-..."
              value={tempKey}
              onChange={(e) => setTempKey(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={saveApiKey} 
              disabled={isSaving || !tempKey.trim()}
              variant="default"
            >
              {isSaving ? (
                <>
                  <Key className="w-4 h-4 mr-2 animate-pulse" />
                  Saving...
                </>
              ) : (
                <>
                  <Key className="w-4 h-4 mr-2" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>

        {!isKeyValid && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
            <div className="flex items-center text-orange-800">
              <AlertCircle className="w-4 h-4 mr-2" />
              <span className="text-sm font-medium">API Key Required</span>
            </div>
            <p className="text-sm text-orange-700 mt-1">
              You need an OpenAI API key to use the resume analysis features. 
              <a 
                href="https://platform.openai.com/api-keys" 
                target="_blank" 
                rel="noopener noreferrer" 
                className="underline ml-1"
              >
                Get your API key here
              </a>
            </p>
          </div>
        )}

        {isKeyValid && (
          <div className="flex justify-between items-center p-3 bg-green-50 border border-green-200 rounded-lg">
            <span className="text-sm text-green-800">
              API key is configured and ready to use
            </span>
            <Button 
              onClick={clearApiKey} 
              variant="outline" 
              size="sm"
              className="text-red-600 border-red-600 hover:bg-red-50"
            >
              Clear Key
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ApiKeySettings;
