import { zoomVideoSDKService } from '../../../server/services/zoomVideoSDKService';

describe('ZoomVideoSDKService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateToken', () => {
    it('should generate tokens for host and participant', async () => {
      const result = await zoomVideoSDKService.generateToken(
        'test-session',
        'test-candidate-id',
        'host'
      );

      expect(result).toHaveProperty('hostToken');
      expect(result).toHaveProperty('participantToken');
      expect(result).toHaveProperty('sessionName');
      expect(result.sessionName).toBe('test-session');
    });

    it('should generate different tokens for different roles', async () => {
      const hostResult = await zoomVideoSDKService.generateToken(
        'test-session',
        'test-candidate-id',
        'host'
      );

      const participantResult = await zoomVideoSDKService.generateToken(
        'test-session',
        'test-candidate-id',
        'participant'
      );

      expect(hostResult.hostToken).toBeDefined();
      expect(participantResult.participantToken).toBeDefined();
    });

    it('should handle invalid session names', async () => {
      await expect(
        zoomVideoSDKService.generateToken('', 'test-candidate-id', 'host')
      ).rejects.toThrow();
    });
  });

  describe('createSession', () => {
    it('should create a new session', async () => {
      const result = await zoomVideoSDKService.createSession(
        'test-interview-id',
        'test-candidate-id'
      );

      expect(result).toHaveProperty('sessionId');
      expect(result).toHaveProperty('sessionName');
      expect(result).toHaveProperty('hostToken');
      expect(result.sessionId).toBeDefined();
    });

    it('should generate unique session names', async () => {
      const session1 = await zoomVideoSDKService.createSession(
        'interview-1',
        'candidate-1'
      );

      const session2 = await zoomVideoSDKService.createSession(
        'interview-2',
        'candidate-2'
      );

      expect(session1.sessionName).not.toBe(session2.sessionName);
    });
  });

  describe('validateToken', () => {
    it('should validate a valid token', () => {
      const validToken = 'mock-host-token';
      const result = zoomVideoSDKService.validateToken(validToken);
      expect(result).toBe(true);
    });

    it('should reject invalid tokens', () => {
      const invalidToken = 'invalid-token';
      const result = zoomVideoSDKService.validateToken(invalidToken);
      expect(result).toBe(false);
    });

    it('should reject empty tokens', () => {
      const result = zoomVideoSDKService.validateToken('');
      expect(result).toBe(false);
    });
  });

  describe('cleanupExpiredSessions', () => {
    it('should clean up expired sessions', async () => {
      // Create some test sessions
      await zoomVideoSDKService.createSession('test-1', 'candidate-1');
      await zoomVideoSDKService.createSession('test-2', 'candidate-2');

      // Clean up expired sessions
      await expect(
        zoomVideoSDKService.cleanupExpiredSessions()
      ).resolves.not.toThrow();
    });
  });

  describe('getActiveSessionCount', () => {
    it('should return the number of active sessions', () => {
      const count = zoomVideoSDKService.getActiveSessionCount();
      expect(typeof count).toBe('number');
      expect(count).toBeGreaterThanOrEqual(0);
    });
  });
});
