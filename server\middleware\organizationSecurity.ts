import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../auth';
import { db } from '../db';
import { organizations, authUsers, candidates, jobPostings, applications } from '@shared/schema';
import { eq, and } from 'drizzle-orm';
import { logSecurityViolation } from './auditLogger';

/**
 * Enhanced organization security middleware
 * Validates and enforces organization-scoped data access across all endpoints
 */

export interface SecureOrganizationRequest extends AuthenticatedRequest {
  organizationData?: {
    id: string;
    name: string;
    isActive: boolean;
  };
}

/**
 * Validates organization access and loads organization data
 */
export async function validateOrganizationAccess(req: SecureOrganizationRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Super admin bypasses organization validation
  if (req.user.role === 'super_admin') {
    return next();
  }

  if (!req.user.organizationId) {
    await logSecurityViolation(req, 'missing_organization', {
      userId: req.user.id,
      userRole: req.user.role
    });
    return res.status(403).json({ 
      error: 'Access denied: User must be associated with an organization',
      code: 'NO_ORGANIZATION'
    });
  }

  try {
    // Load and validate organization
    const [organization] = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        isActive: organizations.isActive,
      })
      .from(organizations)
      .where(eq(organizations.id, req.user.organizationId))
      .limit(1);

    if (!organization) {
      await logSecurityViolation(req, 'invalid_organization', {
        organizationId: req.user.organizationId,
        userId: req.user.id
      });
      return res.status(403).json({ 
        error: 'Access denied: Invalid organization',
        code: 'INVALID_ORGANIZATION'
      });
    }

    if (!organization.isActive) {
      await logSecurityViolation(req, 'inactive_organization', {
        organizationId: req.user.organizationId,
        organizationName: organization.name
      });
      return res.status(403).json({ 
        error: 'Access denied: Organization is inactive',
        code: 'INACTIVE_ORGANIZATION'
      });
    }

    // Attach organization data to request
    req.organizationData = organization;
    next();

  } catch (error) {
    console.error('Organization validation error:', error);
    res.status(500).json({ error: 'Organization validation failed' });
  }
}

/**
 * Validates that a specific resource belongs to the user's organization
 */
export function validateResourceOwnership(resourceType: 'candidate' | 'job_posting' | 'application', resourceIdParam: string = 'id') {
  return async (req: SecureOrganizationRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Super admin bypasses resource ownership validation
    if (req.user.role === 'super_admin') {
      return next();
    }

    const resourceId = req.params[resourceIdParam];
    const organizationId = req.user.organizationId;

    if (!resourceId) {
      return res.status(400).json({ error: 'Resource ID is required' });
    }

    if (!organizationId) {
      return res.status(403).json({ error: 'Organization access required' });
    }

    try {
      let resourceExists = false;
      
      switch (resourceType) {
        case 'candidate': {
          const [candidate] = await db
            .select({ id: candidates.id })
            .from(candidates)
            .where(and(
              eq(candidates.id, resourceId),
              eq(candidates.organizationId, organizationId)
            ))
            .limit(1);
          resourceExists = !!candidate;
          break;
        }
        case 'job_posting': {
          const [jobPosting] = await db
            .select({ id: jobPostings.id })
            .from(jobPostings)
            .where(and(
              eq(jobPostings.id, resourceId),
              eq(jobPostings.organizationId, organizationId)
            ))
            .limit(1);
          resourceExists = !!jobPosting;
          break;
        }
        case 'application': {
          const [application] = await db
            .select({ id: applications.id })
            .from(applications)
            .where(and(
              eq(applications.id, resourceId),
              eq(applications.organizationId, organizationId)
            ))
            .limit(1);
          resourceExists = !!application;
          break;
        }
      }

      if (!resourceExists) {
        await logSecurityViolation(req, 'unauthorized_resource_access', {
          resourceType,
          resourceId,
          attemptedOrganizationId: organizationId,
          userRole: req.user.role
        });
        return res.status(404).json({ 
          error: 'Resource not found or access denied',
          code: 'RESOURCE_NOT_FOUND'
        });
      }

      next();

    } catch (error) {
      console.error('Resource ownership validation error:', error);
      res.status(500).json({ error: 'Resource validation failed' });
    }
  };
}

/**
 * Admin-only organization management validation
 * Ensures only organization admins can manage users within their organization
 */
export async function validateAdminOrganizationManagement(req: SecureOrganizationRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Super admin bypasses all restrictions
  if (req.user.role === 'super_admin') {
    return next();
  }

  // Only admins can manage organization
  if (req.user.role !== 'admin') {
    await logSecurityViolation(req, 'insufficient_admin_privileges', {
      requiredRole: 'admin',
      currentRole: req.user.role,
      action: req.originalUrl
    });
    return res.status(403).json({ 
      error: 'Admin privileges required for organization management',
      code: 'INSUFFICIENT_PRIVILEGES'
    });
  }

  // If managing a specific user, ensure they belong to the same organization
  const targetUserId = req.params.userId || req.body.userId;
  if (targetUserId && req.user.organizationId) {
    try {
      const [targetUser] = await db
        .select({ id: authUsers.id, organizationId: authUsers.organizationId })
        .from(authUsers)
        .where(eq(authUsers.id, targetUserId))
        .limit(1);

      if (!targetUser) {
        return res.status(404).json({ error: 'User not found' });
      }

      if (targetUser.organizationId !== req.user.organizationId) {
        await logSecurityViolation(req, 'cross_organization_user_management', {
          targetUserId,
          targetUserOrganization: targetUser.organizationId,
          adminOrganization: req.user.organizationId
        });
        return res.status(403).json({ 
          error: 'Cannot manage users from other organizations',
          code: 'CROSS_ORGANIZATION_ACCESS'
        });
      }
    } catch (error) {
      console.error('Admin validation error:', error);
      return res.status(500).json({ error: 'User validation failed' });
    }
  }

  next();
}

/**
 * Data modification security middleware
 * Ensures all create/update operations include proper organization context
 */
export function validateDataModification(req: SecureOrganizationRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Skip for super admin (they can work across organizations)
  if (req.user.role === 'super_admin') {
    return next();
  }

  const method = req.method.toLowerCase();
  if (['post', 'put', 'patch'].includes(method)) {
    const organizationId = req.user.organizationId;
    
    if (!organizationId) {
      return res.status(403).json({ error: 'Organization context required for data modification' });
    }

    // Ensure request body includes correct organizationId (if not already set)
    if (req.body && typeof req.body === 'object') {
      // If organizationId is provided in body, validate it matches user's org
      if (req.body.organizationId && req.body.organizationId !== organizationId) {
        return res.status(403).json({ 
          error: 'Cannot create/modify data for other organizations',
          code: 'INVALID_ORGANIZATION_ID'
        });
      }
      
      // Automatically set organizationId if not provided
      if (!req.body.organizationId) {
        req.body.organizationId = organizationId;
      }
    }
  }

  next();
}