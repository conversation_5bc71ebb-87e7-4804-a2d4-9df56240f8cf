import React from 'react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

/**
 * HomePage Component
 *
 * Main landing page for Steorra AI - Voice AI Agent for Talent Acquisition
 * Features:
 * - Hero section with animated elements
 * - Features showcase with interactive cards
 * - Responsive design with Tailwind CSS
 * - Clean architecture with proper routing integration
 *
 * @returns {JSX.Element} HomePage component
 */
const HomePage = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col bg-white">
      {/* ==================== NAVIGATION BAR ==================== */}
      <nav className="w-full bg-white px-6 md:px-12 lg:px-20 py-6 flex items-center justify-between sticky top-0 z-50">
        {/* Logo with Text */}
        <div className="flex items-center space-x-3">
          <img
            src="/Hero/Steorra.svg"
            alt="Steorra Logo"
            className="h-8 w-auto"
          />
          <span className="text-xl font-semibold text-[#0152FF]">Steorra</span>
        </div>

        {/* Navigation Links + CTA Buttons - Right Side */}
        <div className="flex items-center space-x-8">
          {/* Navigation Links - Desktop (Right Side, adjacent to Login) */}
          <div className="hidden md:flex items-center space-x-8">
            <button className="text-[#151518] text-sm font-medium hover:text-[#0152FF] transition">
              Product
            </button>
            <button className="text-[#151518] text-sm font-medium hover:text-[#0152FF] transition">
              Pricing
            </button>
            <button className="text-[#151518] text-sm font-medium hover:text-[#0152FF] transition">
              Resources
            </button>
            <button
              onClick={() => navigate('/login')}
              className="text-[#151518] text-sm font-medium hover:text-[#0152FF] transition cursor-pointer"
            >
              Login
            </button>
          </div>

          {/* Book a Demo Button */}
          <Button
            onClick={() => navigate('/book-demo')}
            className="bg-[#0152FF] text-white text-sm font-medium px-6 py-3 rounded-full hover:bg-[#0142CC] transition-colors flex items-center space-x-2"
          >
            <span className="flex items-center space-x-2">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 0L9.5 6.5L16 8L9.5 9.5L8 16L6.5 9.5L0 8L6.5 6.5L8 0Z" fill="white"/>
              </svg>
              <span>Book a Demo</span>
            </span>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </Button>
        </div>
      </nav>

      {/* ==================== HERO SECTION ==================== */}
      <section
        className="w-full min-h-[90vh] flex items-center justify-center px-6 md:px-12 lg:px-20 py-12 relative overflow-hidden"
        style={{
          background: 'linear-gradient(135deg, #FFF5F0 0%, #FFE8E0 30%, #F0E8FF 70%, #E8F0FF 100%)'
        }}
      >
        <div className="max-w-7xl w-full mx-auto relative">
          {/* Centered Circular Hero with Layered Circles */}
          <div className="relative flex items-center justify-center min-h-[700px]">

            {/* Third Outer Circle - 1504px (Largest, cropped at top/bottom) */}
            <div
              className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full"
              style={{
                width: '1504px',
                height: '1504px',
                background: 'rgba(255, 255, 255, 0.04)',
                boxShadow: '0 0 108px 0 rgba(0, 0, 0, 0.06)',
              }}
            >
              {/* Feature Labels on 3rd Layer - Outside visible circle */}
              {/* Left Bottom - Feature List */}
              <div className="absolute left-[8%] bottom-[32%] hidden lg:block animate-fadeInUp">
                <div className="flex flex-col space-y-3">
                  <div className="flex items-center space-x-3">
                    <img src="/Hero/Frame 1618877697.svg" alt="check" className="w-5 h-5" />
                    <span className="text-[#4D4D54] text-sm font-medium">AI voice interviews in 10X</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <img src="/Hero/Frame 1618877697.svg" alt="check" className="w-5 h-5" />
                    <span className="text-[#4D4D54] text-sm font-medium">Instant insights in seconds.</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Second Outer Circle - 1208px (cropped at top/bottom) */}
            <div
              className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full"
              style={{
                width: '1208px',
                height: '1208px',
                background: 'rgba(255, 255, 255, 0.04)',
                boxShadow: '0 0 108px 0 rgba(0, 0, 0, 0.06)',
              }}
            />

            {/* First Outer Circle - 880px */}
            <div
              className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full"
              style={{
                width: '880px',
                height: '880px',
                background: 'rgba(255, 255, 255, 0.04)',
                boxShadow: '0 0 108px 0 rgba(0, 0, 0, 0.06)',
              }}
            />

            {/* Lady Image - 630px (Center) */}
            <div
              className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full overflow-hidden"
              style={{
                width: '630px',
                height: '630px',
                aspectRatio: '1/1',
                boxShadow: '0 0 60px 0 rgba(0, 0, 0, 0.1)',
              }}
            >
              <img
                src="/Hero/image 210292.png"
                alt="AI Voice Agent Interview"
                className="w-full h-full object-cover"
                style={{
                  aspectRatio: '1/1',
                }}
              />

              {/* Call Control Buttons - OVERLAY inside lady image */}
              <div className="absolute bottom-[15%] left-1/2 transform -translate-x-1/2 flex items-center space-x-3 bg-white rounded-full px-5 py-3 shadow-2xl z-20">
                <button className="w-10 h-10 rounded-full bg-white border-2 border-gray-300 flex items-center justify-center hover:bg-gray-50 transition">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 1C12 1 8 1 8 5V12C8 16 12 16 12 16C12 16 16 16 16 12V5C16 1 12 1 12 1Z" stroke="#4D4D54" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M19 10V12C19 15.866 15.866 19 12 19M12 19C8.13401 19 5 15.866 5 12V10M12 19V23M8 23H16" stroke="#4D4D54" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <button className="w-12 h-12 rounded-full bg-[#FF3B30] flex items-center justify-center hover:bg-[#E6342A] transition shadow-lg">
                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3 5.5C3 14.0604 9.93959 21 18.5 21C18.8862 21 19.2691 20.9859 19.6483 20.9581C20.0834 20.9262 20.3009 20.9103 20.499 20.7963C20.663 20.7019 20.8185 20.5345 20.9007 20.364C21 20.1582 21 19.9181 21 19.438V16.6207C21 16.2169 21 16.015 20.9335 15.842C20.8749 15.6891 20.7795 15.553 20.6559 15.4456C20.516 15.324 20.3262 15.255 19.9468 15.117L16.74 13.9509C16.2985 13.7904 16.0777 13.7101 15.8683 13.7237C15.6836 13.7357 15.5059 13.7988 15.3549 13.9058C15.1837 14.0271 15.0629 14.2285 14.8212 14.6314L14 16C11.3501 14.7999 9.2019 12.6489 8 10L9.36863 9.17882C9.77145 8.93713 9.97286 8.81628 10.0942 8.64506C10.2012 8.49408 10.2643 8.31637 10.2763 8.1317C10.2899 7.92227 10.2096 7.70153 10.0491 7.26005L8.88299 4.05321C8.745 3.67376 8.67601 3.48403 8.55442 3.3441C8.44701 3.22049 8.31089 3.12515 8.15802 3.06645C7.98496 3 7.78308 3 7.37932 3H4.56201C4.08188 3 3.84181 3 3.63598 3.09925C3.4655 3.18146 3.29814 3.33701 3.2037 3.50103C3.08968 3.69907 3.07375 3.91662 3.04189 4.35173C3.01413 4.73086 3 5.11378 3 5.5Z" fill="white"/>
                  </svg>
                </button>
                <button className="w-10 h-10 rounded-full bg-white border-2 border-gray-300 flex items-center justify-center hover:bg-gray-50 transition">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11 5L6 9H2V15H6L11 19V5Z" stroke="#4D4D54" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M15.54 8.46C16.4774 9.39764 17.0039 10.6692 17.0039 11.995C17.0039 13.3208 16.4774 14.5924 15.54 15.53M19.07 4.93C20.9447 6.80528 21.9979 9.34836 21.9979 12C21.9979 14.6516 20.9447 17.1947 19.07 19.07" stroke="#4D4D54" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>

            {/* Left Top - Heading and CTA Button */}
            <div className="absolute left-0 top-[10%] max-w-[320px] hidden lg:block animate-fadeInUp z-10">
              {/* Main Heading */}
              <h1 className="text-4xl xl:text-5xl font-bold text-[#000000] leading-tight mb-6">
                Interview smarter,<br />
                with AI voice agent<br />
                at scale.
              </h1>

              {/* CTA Button */}
              <Button
                onClick={() => navigate('/auth')}
                className="bg-[#0152FF] text-white font-semibold px-8 py-3 text-base rounded-full hover:bg-[#0142CC] transition-all duration-300 hover:shadow-lg flex items-center space-x-2"
              >
                <span>Start free trial</span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Button>
            </div>

            {/* Right Bottom - Info Text */}
            <div className="absolute right-0 bottom-[10%] max-w-[320px] hidden lg:block animate-fadeInUpDelayed z-10">
              <div className="flex items-start space-x-3">
                <img src="/Hero/Frame 1618877697.svg" alt="Steorra Icon" className="w-6 h-6 flex-shrink-0 mt-1" />
                <p className="text-sm text-[#4D4D54] leading-relaxed">
                  Our AI Voice Agent engages candidates like a real recruiter—asking and responding in real time. Create a human interview experience while saving hours of effort.
                </p>
              </div>
            </div>
          </div>

          {/* Mobile Layout - Stacked */}
          <div className="lg:hidden space-y-8 mt-8">
            {/* Heading */}
            <h1 className="text-4xl md:text-5xl font-bold text-[#000000] leading-tight text-center">
              Interview smarter,<br />
              with AI voice agent<br />
              at scale.
            </h1>

            {/* CTA Button */}
            <div className="flex justify-center">
              <Button
                onClick={() => navigate('/auth')}
                className="bg-[#0152FF] text-white font-semibold px-8 py-3 text-base rounded-full hover:bg-[#0142CC] transition-all duration-300 hover:shadow-lg flex items-center space-x-2"
              >
                <span>Start free trial</span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Button>
            </div>

            {/* Feature List */}
            <div className="flex flex-col space-y-3 max-w-md mx-auto">
              <div className="flex items-center space-x-3">
                <img src="/Hero/Frame 1618877697.svg" alt="check" className="w-5 h-5" />
                <span className="text-[#4D4D54] text-sm">AI voice interviews in 10X</span>
              </div>
              <div className="flex items-center space-x-3">
                <img src="/Hero/Frame 1618877697.svg" alt="check" className="w-5 h-5" />
                <span className="text-[#4D4D54] text-sm">Instant insights in seconds.</span>
              </div>
            </div>

            {/* Info Text */}
            <div className="flex items-start space-x-3 bg-white rounded-2xl p-5 shadow-lg max-w-md mx-auto">
              <img src="/Hero/Frame 1618877697.svg" alt="Steorra Icon" className="w-6 h-6 flex-shrink-0 mt-1" />
              <p className="text-sm text-[#4D4D54] leading-relaxed">
                Our AI Voice Agent engages candidates like a real recruiter—asking and responding in real time. Create a human interview experience while saving hours of effort.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* ==================== FEATURES SECTION ==================== */}
      <section
        className="w-full min-h-screen px-6 md:px-12 lg:px-20 py-20"
        style={{ background: '#FBFAFF' }}
      >
        <div className="max-w-7xl w-full mx-auto">
          {/* 30%-70% Layout: Left Content + Right Features */}
          <div className="flex flex-col lg:flex-row gap-12 lg:gap-16">

            {/* Left Side - 30% - Main Heading and Description */}
            <div className="lg:w-[30%] flex-shrink-0">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#151518] mb-6">
                AI that works like a true — Hiring Partner!
              </h2>
              <p className="text-[#4B4B52] leading-relaxed mb-8">
                Discover the essential features of Steorra AI, which include automating repetitive tasks and prioritizing human interaction to enhance productivity.
              </p>
              <Button
                onClick={() => navigate('/book-demo')}
                className="bg-[#0152FF] text-white text-sm font-medium px-6 py-3 rounded-full hover:bg-[#0142CC] transition-colors flex items-center space-x-2"
              >
                <span>Get demo</span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </Button>
            </div>

            {/* Right Side - 70% - Feature Cards Stacked Vertically */}
            <div className="lg:w-[70%] space-y-8">

              {/* Feature 1: Automated Sourcing from Job Boards */}
              <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-md hover:shadow-lg transition-shadow">
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Feature Content */}
                  <div className="lg:w-[45%]">
                    <h3 className="text-xl lg:text-2xl font-bold text-[#151518] mb-4">
                      Automated sourcing from job boards.
                    </h3>

                    {/* Feature List */}
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Pulls applicants directly from LinkedIn, Naukri, and more</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Consolidates all applicants into a single dashboard</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Centralizes data for recruiters in one place</span>
                      </li>
                    </ul>

                    <button className="flex items-center space-x-2 text-[#0152FF] font-semibold hover:underline">
                      <span>Start free trial</span>
                      <img src="/Features/arrow-narrow-right.svg" alt="arrow" className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Feature GIF */}
                  <div className="lg:w-[55%]">
                    <div className="rounded-xl overflow-hidden bg-gradient-to-br from-[#F8F9FA] to-[#E9ECEF]">
                      <img
                        src="/Features/Automated sourcing from job boards.gif"
                        alt="Automated Sourcing Demo"
                        className="w-full h-auto"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Feature 2: Unified Job Board Insights */}
              <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-md hover:shadow-lg transition-shadow">
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Feature Content */}
                  <div className="lg:w-[45%]">
                    <div className="flex items-start justify-between mb-4">
                      <h3 className="text-xl lg:text-2xl font-bold text-[#151518]">
                        Unified job board insights
                      </h3>
                      <p className="text-xs text-[#4B4B52] italic whitespace-nowrap ml-4">
                        Steorra attributed hires by Job level*
                      </p>
                    </div>

                    {/* Feature List */}
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Identify effective channels by tracking applicants from each platform.</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Respond to applicants directly from the dashboard.</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Post once to multiple boards, saving time.</span>
                      </li>
                    </ul>

                    <button className="flex items-center space-x-2 text-[#0152FF] font-semibold hover:underline">
                      <span>Start free trial</span>
                      <img src="/Features/arrow-narrow-right.svg" alt="arrow" className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Feature GIF */}
                  <div className="lg:w-[55%]">
                    <div className="rounded-xl overflow-hidden bg-gradient-to-br from-[#F8F9FA] to-[#E9ECEF]">
                      <img
                        src="/Features/Unified job board insights.gif"
                        alt="Unified Job Board Insights Demo"
                        className="w-full h-auto"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Feature 3: Automated Resume Screening */}
              <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-md hover:shadow-lg transition-shadow">
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Feature Content */}
                  <div className="lg:w-[45%]">
                    <h3 className="text-xl lg:text-2xl font-bold text-[#151518] mb-4">
                      Automated resume screening
                    </h3>

                    {/* Feature List */}
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Smart skill matching against job requirements</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Bias-free evaluation of candidates</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Instant candidate ranking and insights</span>
                      </li>
                    </ul>

                    <button className="flex items-center space-x-2 text-[#0152FF] font-semibold hover:underline">
                      <span>Start free trial</span>
                      <img src="/Features/arrow-narrow-right.svg" alt="arrow" className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Feature GIF */}
                  <div className="lg:w-[55%]">
                    <div className="rounded-xl overflow-hidden bg-gradient-to-br from-[#F8F9FA] to-[#E9ECEF]">
                      <img
                        src="/Features/Automated resume screening.gif"
                        alt="Automated Resume Screening Demo"
                        className="w-full h-auto"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Feature 4: Instant Interview Scheduling */}
              <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-md hover:shadow-lg transition-shadow">
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Feature Content */}
                  <div className="lg:w-[45%]">
                    <h3 className="text-xl lg:text-2xl font-bold text-[#151518] mb-4">
                      Instant interview scheduling
                    </h3>

                    {/* Feature List */}
                    <ul className="space-y-3 mb-6">
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Automated calendar sync with candidates</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Smart conflict resolution and rescheduling</span>
                      </li>
                      <li className="flex items-start space-x-3">
                        <img src="/Features/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-1 flex-shrink-0" />
                        <span className="text-[#4B4B52] text-sm">Instant confirmations and reminders</span>
                      </li>
                    </ul>

                    <button className="flex items-center space-x-2 text-[#0152FF] font-semibold hover:underline">
                      <span>Start free trial</span>
                      <img src="/Features/arrow-narrow-right.svg" alt="arrow" className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Feature GIF */}
                  <div className="lg:w-[55%]">
                    <div className="rounded-xl overflow-hidden bg-gradient-to-br from-[#F8F9FA] to-[#E9ECEF]">
                      <img
                        src="/Features/Instant interview scheduling_3.gif"
                        alt="Instant Interview Scheduling Demo"
                        className="w-full h-auto"
                      />
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>
      </section>

      {/* ==================== HOW IT WORKS SECTION ==================== */}
      <section
        className="w-full min-h-screen px-6 md:px-12 lg:px-20 py-20"
        style={{ background: '#FFFFFF' }}
      >
        <div className="max-w-7xl w-full mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#151518] mb-4">
              Transform hiring with Steorra AI
            </h2>
            <p className="text-[#4B4B52] text-lg max-w-3xl mx-auto">
              Recruiters maintain control while our AI assistant delivers quick results and ROI.
            </p>
          </div>

          {/* Content Layout: Left Steps + Right Visual */}
          <div className="flex flex-col lg:flex-row items-center gap-12 lg:gap-16">

            {/* Left Side - Steps */}
            <div className="lg:w-[55%] space-y-8">

              {/* Step 1 */}
              <div className="flex items-start gap-4 p-6 rounded-2xl bg-gradient-to-br from-[#F0F4FF] to-[#FFFFFF] hover:shadow-md transition-shadow">
                <div className="flex-shrink-0">
                  <img src="/HowItWorks/Frame 1618874234.svg" alt="check" className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-[#151518] mb-2">
                    Find what others miss
                  </h3>
                  <p className="text-[#4B4B52] leading-relaxed">
                    Steorra searches the open web and your ATS, surfacing qualified candidates LinkedIn can't touch.
                  </p>
                </div>
              </div>

              {/* Step 2 */}
              <div className="flex items-start gap-4 p-6 rounded-2xl bg-gradient-to-br from-[#F0F4FF] to-[#FFFFFF] hover:shadow-md transition-shadow">
                <div className="flex-shrink-0">
                  <img src="/HowItWorks/Frame 1618874234.svg" alt="check" className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-[#151518] mb-2">
                    Match with meaning
                  </h3>
                  <p className="text-[#4B4B52] leading-relaxed">
                    It reads every profile in full, ranks by role fit, and instantly delivers only the most relevant candidates.
                  </p>
                </div>
              </div>

              {/* Step 3 */}
              <div className="flex items-start gap-4 p-6 rounded-2xl bg-gradient-to-br from-[#F0F4FF] to-[#FFFFFF] hover:shadow-md transition-shadow">
                <div className="flex-shrink-0">
                  <img src="/HowItWorks/Frame 1618874234.svg" alt="check" className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-[#151518] mb-2">
                    Engage like you would
                  </h3>
                  <p className="text-[#4B4B52] leading-relaxed">
                    Our AI Agent automates outreach in your voice from personalized emails to scalable drip campaigns.
                  </p>
                </div>
              </div>

              {/* Step 4 */}
              <div className="flex items-start gap-4 p-6 rounded-2xl bg-gradient-to-br from-[#F0F4FF] to-[#FFFFFF] hover:shadow-md transition-shadow">
                <div className="flex-shrink-0">
                  <img src="/HowItWorks/Frame 1618874234.svg" alt="check" className="w-6 h-6" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-[#151518] mb-2">
                    Manage proactively
                  </h3>
                  <p className="text-[#4B4B52] leading-relaxed">
                    Our agent fits into your workflow, tracks every step, and takes precise action to help you hire smarter.
                  </p>
                </div>
              </div>

            </div>

            {/* Right Side - Visual/GIF */}
            <div className="lg:w-[45%] flex items-center justify-center">
              <div className="relative">
                <img
                  src="/HowItWorks/Transform hiring with Steorra AI_1.gif"
                  alt="How Steorra AI Works"
                  className="w-full h-auto rounded-2xl"
                />
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* ==================== PRICING SECTION ==================== */}
      <section
        className="w-full min-h-screen px-6 md:px-12 lg:px-20 py-20 relative overflow-hidden"
        style={{
          background: 'linear-gradient(180deg, #FFFFFF 0%, #E8EFFF 50%, #0152FF 100%)'
        }}
      >
        <div className="max-w-7xl w-full mx-auto relative z-10">
          {/* Section Header */}
          <div className="text-center mb-16">
            {/* Trusted by Top Companies - Avatar Group */}
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="flex -space-x-2">
                <img src="/Pricing/Teammate → 3MNAsmAUyQj4yv25ZYxoJUmWFs.jpg.png" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                <img src="/Pricing/Teammate → 7TSYiPQk4RvdUXd8Y3m2JipPfg.png.png" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                <img src="/Pricing/Teammate → B02JFNtiBbAL48HYWXWfhktv7j4.png.png" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                <img src="/Pricing/Teammate → QrngTS3Z3aN6GbaP3uV6lDUjW4.jpg.png" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
                <img src="/Pricing/Teammate → vh31Zl7h6VwTcXwgh3HFKXeBcY.jpg.png" alt="User" className="w-10 h-10 rounded-full border-2 border-white" />
              </div>
              <span className="text-[#4B4B52] text-sm font-medium">Trusted by Top companies</span>
            </div>

            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#151518] mb-4">
              Pricing built to scale with your business
            </h2>
          </div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 mb-12">

            {/* Standard Plan */}
            <div className="bg-white rounded-3xl p-8 shadow-md hover:shadow-xl transition-shadow">
              <h3 className="text-2xl font-bold text-[#151518] mb-2">Standard</h3>
              <p className="text-[#4B4B52] text-sm mb-6">
                Perfect for businesses interested in basic sourcing and ATS features.
              </p>

              <div className="mb-6">
                <span className="text-5xl font-bold text-[#151518]">$20</span>
                <span className="text-[#4B4B52] text-sm ml-2">per job/month</span>
              </div>

              <button className="w-full py-3 px-6 rounded-full border-2 border-[#E5E7EB] text-[#151518] font-semibold hover:border-[#0152FF] hover:text-[#0152FF] transition-colors mb-8">
                Get Started
              </button>

              <div>
                <h4 className="text-sm font-bold text-[#151518] mb-4">Features</h4>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Up to 15 active jobs</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Basic Multiposting</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Pipeline Management</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Automated Messages</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Advanced Plan - Popular */}
            <div className="bg-white rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-shadow relative border-2 border-[#0152FF]">
              {/* Popular Badge */}
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-[#151518] text-white text-xs font-semibold px-4 py-1 rounded-full flex items-center gap-1">
                  <img src="/Pricing/Vector.svg" alt="star" className="w-3 h-3" />
                  Popular
                </span>
              </div>

              <h3 className="text-2xl font-bold text-[#151518] mb-2">Advanced</h3>
              <p className="text-[#4B4B52] text-sm mb-6">
                Ideal to attract candidates faster and manage them more efficiently.
              </p>

              <div className="mb-6">
                <span className="text-5xl font-bold text-[#151518]">$30</span>
                <span className="text-[#4B4B52] text-sm ml-2">per job/month</span>
              </div>

              <button className="w-full py-3 px-6 rounded-full bg-[#0152FF] text-white font-semibold hover:bg-[#0142CC] transition-colors mb-8 flex items-center justify-center gap-2">
                <span>Get Started</span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>

              <div>
                <h4 className="text-sm font-bold text-[#151518] mb-4">Features</h4>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">3-15 active jobs</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Advanced Multiposting</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Custom Screening Questions</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">API & ATS Integrations</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Spontaneous Applications</span>
                  </li>
                </ul>
              </div>
            </div>

            {/* Enterprise Plan */}
            <div className="bg-white rounded-3xl p-8 shadow-md hover:shadow-xl transition-shadow">
              <h3 className="text-2xl font-bold text-[#151518] mb-2">Enterprise</h3>
              <p className="text-[#4B4B52] text-sm mb-6">
                Tailored for businesses, offering custom solutions and support.
              </p>

              <div className="mb-6">
                <span className="text-3xl font-bold text-[#B0B0B0]">Get a quote</span>
              </div>

              <button className="w-full py-3 px-6 rounded-full border-2 border-[#E5E7EB] text-[#151518] font-semibold hover:border-[#0152FF] hover:text-[#0152FF] transition-colors mb-8">
                Get Started
              </button>

              <div>
                <h4 className="text-sm font-bold text-[#151518] mb-4">Features</h4>
                <ul className="space-y-3">
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Custom amount</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Unlimited users</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Custom integrations</span>
                  </li>
                  <li className="flex items-start gap-3">
                    <img src="/Pricing/Frame 1618874234.svg" alt="check" className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <span className="text-[#4B4B52] text-sm">Chat Support</span>
                  </li>
                </ul>
              </div>
            </div>

          </div>

          {/* Free Trial Banner */}
          <div className="bg-white rounded-3xl p-8 shadow-xl">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              <div>
                <h3 className="text-2xl font-bold text-[#151518] mb-2">
                  Start for free with 2 job postings
                </h3>
                <p className="text-[#4B4B52]">
                  Enjoy 2 free active jobs forever on our Standard Plan—no fees, no commitment!
                </p>
              </div>
              <button className="flex-shrink-0 py-3 px-8 rounded-full bg-[#0152FF] text-white font-semibold hover:bg-[#0142CC] transition-colors flex items-center gap-2">
                <span>Get Started free</span>
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </button>
            </div>
          </div>

        </div>
      </section>

      {/* ==================== STEPS SECTION ==================== */}
      <section
        className="w-full min-h-screen px-6 md:px-12 lg:px-20 py-20"
        style={{ background: '#FBFAFF' }}
      >
        <div className="max-w-7xl w-full mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-[#151518] mb-4">
              Four steps for better hiring
            </h2>
          </div>

          {/* Steps Timeline */}
          <div className="relative mb-16">
            {/* Connecting Line */}
            <div className="hidden lg:block absolute top-8 left-0 right-0 h-0.5 bg-gradient-to-r from-[#FFA500] via-[#0152FF] via-[#9333EA] to-[#10B981]" style={{ top: '32px' }}></div>

            {/* Steps Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10">

              {/* Step 1 */}
              <div className="flex flex-col items-center text-center">
                {/* Step Number Badge */}
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-[#FFA500] to-[#FF8C00] flex items-center justify-center mb-6 shadow-lg">
                  <span className="text-white text-2xl font-bold">1</span>
                </div>

                {/* Step Card */}
                <div className="bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-shadow w-full">
                  <div className="w-12 h-12 mx-auto mb-4 flex items-center justify-center bg-[#EFF6FF] rounded-lg">
                    <img src="/Steps/document-1 1.svg" alt="Document" className="w-6 h-6" />
                  </div>

                  <h3 className="text-lg font-bold text-[#151518] mb-3">
                    Skip the endless resume sorting
                  </h3>

                  <ul className="space-y-2 text-left">
                    <li className="flex items-start gap-2 text-sm text-[#4B4B52]">
                      <img src="/Steps/Vector.svg" alt="check" className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>AI scans applications for you</span>
                    </li>
                    <li className="flex items-start gap-2 text-sm text-[#4B4B52]">
                      <img src="/Steps/Vector.svg" alt="check" className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>Highlights only the best fits</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Step 2 */}
              <div className="flex flex-col items-center text-center">
                {/* Step Number Badge */}
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-[#0152FF] to-[#0142CC] flex items-center justify-center mb-6 shadow-lg">
                  <span className="text-white text-2xl font-bold">2</span>
                </div>

                {/* Step Card */}
                <div className="bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-shadow w-full">
                  <div className="w-12 h-12 mx-auto mb-4 flex items-center justify-center bg-[#EFF6FF] rounded-lg">
                    <img src="/Steps/email 1.svg" alt="Email" className="w-6 h-6" />
                  </div>

                  <h3 className="text-lg font-bold text-[#151518] mb-3">
                    Forget the back-and-forth emails.
                  </h3>

                  <ul className="space-y-2 text-left">
                    <li className="flex items-start gap-2 text-sm text-[#4B4B52]">
                      <img src="/Steps/Vector.svg" alt="check" className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>Auto-schedules interviews</span>
                    </li>
                    <li className="flex items-start gap-2 text-sm text-[#4B4B52]">
                      <img src="/Steps/Vector.svg" alt="check" className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>Syncs directly with your calendar</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Step 3 */}
              <div className="flex flex-col items-center text-center">
                {/* Step Number Badge */}
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-[#9333EA] to-[#7C3AED] flex items-center justify-center mb-6 shadow-lg">
                  <span className="text-white text-2xl font-bold">3</span>
                </div>

                {/* Step Card */}
                <div className="bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-shadow w-full">
                  <div className="w-12 h-12 mx-auto mb-4 flex items-center justify-center bg-[#EFF6FF] rounded-lg">
                    <img src="/Steps/user 1.svg" alt="User" className="w-6 h-6" />
                  </div>

                  <h3 className="text-lg font-bold text-[#151518] mb-3">
                    Bring great talent onboard.
                  </h3>

                  <ul className="space-y-2 text-left">
                    <li className="flex items-start gap-2 text-sm text-[#4B4B52]">
                      <img src="/Steps/Vector.svg" alt="check" className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>Faster from application to offer</span>
                    </li>
                    <li className="flex items-start gap-2 text-sm text-[#4B4B52]">
                      <img src="/Steps/Vector.svg" alt="check" className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>AI handles the heavy lifting</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Step 4 */}
              <div className="flex flex-col items-center text-center">
                {/* Step Number Badge */}
                <div className="w-16 h-16 rounded-full bg-gradient-to-br from-[#10B981] to-[#059669] flex items-center justify-center mb-6 shadow-lg">
                  <span className="text-white text-2xl font-bold">4</span>
                </div>

                {/* Step Card */}
                <div className="bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-shadow w-full">
                  <div className="w-12 h-12 mx-auto mb-4 flex items-center justify-center bg-[#EFF6FF] rounded-lg">
                    <img src="/Steps/workbench 1.svg" alt="Workbench" className="w-6 h-6" />
                  </div>

                  <h3 className="text-lg font-bold text-[#151518] mb-3">
                    Let candidates feel heard
                  </h3>

                  <ul className="space-y-2 text-left">
                    <li className="flex items-start gap-2 text-sm text-[#4B4B52]">
                      <img src="/Steps/Vector.svg" alt="check" className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>AI voice agent engages naturally</span>
                    </li>
                    <li className="flex items-start gap-2 text-sm text-[#4B4B52]">
                      <img src="/Steps/Vector.svg" alt="check" className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <span>Keeps conversations warm & real</span>
                    </li>
                  </ul>
                </div>
              </div>

            </div>
          </div>

          {/* CTA Button */}
          <div className="flex justify-center">
            <button className="py-3 px-8 rounded-full bg-[#0152FF] text-white font-semibold hover:bg-[#0142CC] transition-colors flex items-center gap-2 shadow-lg">
              <img src="/Steps/Frame 1618877697.svg" alt="calendar" className="w-5 h-5" />
              <span>Book a Demo</span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
      </section>

      {/* ==================== FOOTER SECTION ==================== */}
      <footer
        className="w-full px-6 md:px-12 lg:px-20 py-16"
        style={{ background: '#0A0A0A' }}
      >
        <div className="max-w-7xl w-full mx-auto">
          {/* Top Section - CTA */}
          <div className="mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-8">
              Tailor-made inference awaits you!
            </h2>

            <div className="flex flex-col sm:flex-row gap-4 max-w-2xl">
              <button className="flex-1 py-4 px-8 rounded-full bg-[#0152FF] text-white font-semibold hover:bg-[#0142CC] transition-colors">
                Start free trial
              </button>
              <button className="flex-1 py-4 px-8 rounded-full border-2 border-white text-white font-semibold hover:bg-white hover:text-[#0A0A0A] transition-colors flex items-center justify-center gap-2">
                <img src="/Footer/Frame 1618877697.svg" alt="calendar" className="w-5 h-5" />
                <span>Book a Demo</span>
              </button>
            </div>
          </div>

          {/* Middle Section - Logo and Links */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12 pb-12 border-b border-[#2A2A2A]">
            {/* Logo and Social Links */}
            <div className="md:col-span-1">
              <div className="flex items-center space-x-2 mb-6">
                <img src="/Footer/Vector.svg" alt="Steorra Logo" className="w-8 h-8" />
                <span className="text-white text-xl font-bold">Steorra</span>
              </div>

              <div className="flex items-center space-x-4">
                <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" className="w-10 h-10 rounded-lg bg-[#1A1A1A] hover:bg-[#2A2A2A] transition-colors flex items-center justify-center">
                  <img src="/Footer/Frame 1261156364.svg" alt="LinkedIn" className="w-5 h-5" />
                </a>
                <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="w-10 h-10 rounded-lg bg-[#1A1A1A] hover:bg-[#2A2A2A] transition-colors flex items-center justify-center">
                  <img src="/Footer/Frame 1261156365.svg" alt="Twitter" className="w-5 h-5" />
                </a>
                <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="w-10 h-10 rounded-lg bg-[#1A1A1A] hover:bg-[#2A2A2A] transition-colors flex items-center justify-center">
                  <img src="/Footer/Frame 1261156366.svg" alt="GitHub" className="w-5 h-5" />
                </a>
                <a href="#" className="w-10 h-10 rounded-lg bg-[#1A1A1A] hover:bg-[#2A2A2A] transition-colors flex items-center justify-center">
                  <img src="/Footer/Frame 1261156367.svg" alt="Slack" className="w-5 h-5" />
                </a>
              </div>
            </div>

            {/* Product Links */}
            <div>
              <h3 className="text-white font-semibold mb-4 uppercase text-sm tracking-wider">
                PRODUCT
              </h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Integrations
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Updates
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    FAQ
                  </a>
                </li>
              </ul>
            </div>

            {/* Pricing Links */}
            <div>
              <h3 className="text-white font-semibold mb-4 uppercase text-sm tracking-wider">
                PRICING
              </h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Plans
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Enterprise
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Compare
                  </a>
                </li>
              </ul>
            </div>

            {/* Resources Links */}
            <div>
              <h3 className="text-white font-semibold mb-4 uppercase text-sm tracking-wider">
                RESOURCES
              </h3>
              <ul className="space-y-3">
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Documentation
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Help Center
                  </a>
                </li>
                <li>
                  <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                    Contact
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Bottom Section - Copyright and Legal */}
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-[#6B7280] text-sm">
              2025 Steorra Technologies Private Limited
            </p>

            <div className="flex items-center gap-6">
              <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                Privacy policy
              </a>
              <a href="#" className="text-[#9CA3AF] hover:text-white transition-colors text-sm">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;