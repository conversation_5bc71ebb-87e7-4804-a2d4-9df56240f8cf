import { Router } from 'express';
import { authenticateToken } from '../auth';

const router = Router();

/**
 * @swagger
 * /twilio-setup/info:
 *   get:
 *     summary: Get Twilio account information
 *     description: Retrieve Twilio account details, balance, and phone numbers
 *     tags: [Twilio Setup]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Twilio account information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 account:
 *                   type: object
 *                   properties:
 *                     sid:
 *                       type: string
 *                     friendlyName:
 *                       type: string
 *                     status:
 *                       type: string
 *                     type:
 *                       type: string
 *                 balance:
 *                   type: object
 *                   properties:
 *                     currency:
 *                       type: string
 *                     balance:
 *                       type: string
 *                 phoneNumbers:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       sid:
 *                         type: string
 *                       phoneNumber:
 *                         type: string
 *                       friendlyName:
 *                         type: string
 *                       capabilities:
 *                         type: object
 *       400:
 *         description: Twilio credentials not configured
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Failed to fetch Twilio information
 */
router.get('/info', authenticateToken, async (req, res) => {
  try {
    const accountSid = process.env.TWILIO_ACCOUNT_SID || '**********************************';
    const authToken = process.env.TWILIO_AUTH_TOKEN || '0f24194f994e0ea432adb93db4352448';
    
    if (!accountSid || !authToken) {
      return res.status(400).json({ 
        error: 'Twilio credentials not configured',
        message: 'TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN environment variables are required'
      });
    }

    const twilioModule = await import('twilio');
    const twilio = twilioModule.default;
    const client = twilio(accountSid, authToken);

    // Get account information
    const account = await client.api.accounts(accountSid).fetch();
    
    // Get phone numbers
    const phoneNumbers = await client.incomingPhoneNumbers.list();
    
    // Get balance
    const balance = await client.balance.fetch();

    res.json({
      account: {
        sid: account.sid,
        friendlyName: account.friendlyName,
        status: account.status,
        type: account.type
      },
      balance: {
        currency: balance.currency,
        balance: balance.balance
      },
      phoneNumbers: phoneNumbers.map((number: any) => ({
        sid: number.sid,
        phoneNumber: number.phoneNumber,
        friendlyName: number.friendlyName,
        capabilities: number.capabilities
      }))
    });
  } catch (error: any) {
    console.error('Twilio setup error:', error);
    res.status(500).json({ 
      error: 'Failed to fetch Twilio information',
      details: error.message 
    });
  }
});

// Buy a phone number (if needed)
router.post('/buy-number', authenticateToken, async (req, res) => {
  try {
    const { countryCode = 'US', areaCode } = req.body;
    
    const accountSid = process.env.TWILIO_ACCOUNT_SID || '**********************************';
    const authToken = process.env.TWILIO_AUTH_TOKEN || '0f24194f994e0ea432adb93db4352448';
    
    if (!accountSid || !authToken) {
      return res.status(400).json({ error: 'Twilio credentials not configured' });
    }

    const twilioModule = await import('twilio');
    const twilio = twilioModule.default;
    const client = twilio(accountSid, authToken);

    // Search for available numbers
    const numbers = await client.availablePhoneNumbers(countryCode)
      .local
      .list({
        areaCode: areaCode,
        voiceEnabled: true,
        limit: 5
      });

    if (numbers.length === 0) {
      return res.status(404).json({ error: 'No available phone numbers found' });
    }

    // Buy the first available number
    const purchasedNumber = await client.incomingPhoneNumbers.create({
      phoneNumber: numbers[0].phoneNumber,
      voiceUrl: `${process.env.BASE_URL || 'https://yourapp.replit.dev'}/api/voice-agent/twiml/webhook`,
      voiceMethod: 'POST'
    });

    res.json({
      success: true,
      phoneNumber: purchasedNumber.phoneNumber,
      sid: purchasedNumber.sid,
      message: 'Phone number purchased successfully'
    });
  } catch (error: any) {
    console.error('Twilio number purchase error:', error);
    res.status(500).json({ 
      error: 'Failed to purchase phone number',
      details: error.message 
    });
  }
});

export default router;