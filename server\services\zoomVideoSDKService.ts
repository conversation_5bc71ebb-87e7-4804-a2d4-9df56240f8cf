import jwt from 'jsonwebtoken';
import crypto from 'crypto';

export interface ZoomSDKTokenOptions {
  role: 'host' | 'participant';
  sessionName: string;
  userIdentity: string;
  sessionKey?: string;
  expirationTime?: number; // in seconds, default 2 hours
}

export interface ZoomSDKSession {
  sessionName: string;
  sessionKey: string;
  sessionPassword?: string;
  createdAt: Date;
  expiresAt: Date;
}

export class ZoomVideoSDKService {
  private sdkKey: string;
  private sdkSecret: string;
  private activeSessions: Map<string, ZoomSDKSession> = new Map();

  constructor() {
    this.sdkKey = process.env.ZOOM_SDK_KEY || '';
    this.sdkSecret = process.env.ZOOM_SDK_SECRET || '';

    console.log('🎥 Zoom Video SDK initialization:', {
      hasKey: !!this.sdkKey,
      hasSecret: !!this.sdkSecret,
      keyLength: this.sdkKey.length,
      secretLength: this.sdkSecret.length
    });

    if (!this.sdkKey || !this.sdkSecret) {
      console.warn('⚠️ Zoom Video SDK credentials not configured. Set ZOOM_SDK_KEY and ZOOM_SDK_SECRET environment variables.');
    } else {
      console.log('✅ Zoom Video SDK credentials configured successfully');
    }
  }

  /**
   * Check if Zoom Video SDK is properly configured
   */
  isConfigured(): boolean {
    return !!(this.sdkKey && this.sdkSecret);
  }

  /**
   * Generate JWT token for Zoom Video SDK
   */
  generateSDKToken(options: ZoomSDKTokenOptions): string {
    if (!this.isConfigured()) {
      throw new Error('Zoom Video SDK not configured. Please set ZOOM_SDK_KEY and ZOOM_SDK_SECRET.');
    }

    const {
      role,
      sessionName,
      userIdentity,
      sessionKey,
      expirationTime = 7200 // 2 hours default
    } = options;

    const now = Math.floor(Date.now() / 1000);
    const exp = now + expirationTime;

    const payload = {
      iss: this.sdkKey,
      exp: exp,
      alg: 'HS256',
      aud: 'zoom',
      app_key: this.sdkKey, // Correct format: app_key instead of appKey
      tpc: sessionName, // Correct format: tpc instead of sessionName
      role_type: role === 'host' ? 1 : 0, // Correct format: role_type instead of role
      user_identity: userIdentity, // Correct format: user_identity instead of userIdentity
      version: 1, // Required field
      iat: now, // Issue time
      ...(sessionKey && { session_key: sessionKey }) // Correct format: session_key instead of sessionKey
    };

    console.log('🎥 JWT Payload:', JSON.stringify(payload, null, 2));

    const token = jwt.sign(payload, this.sdkSecret, {
      algorithm: 'HS256',
      header: {
        alg: 'HS256',
        typ: 'JWT'
      }
    });

    console.log(`🎥 Generated Zoom Video SDK token for ${role}: ${userIdentity} in session: ${sessionName}`);
    console.log(`🎥 Token length: ${token.length} characters`);

    return token;
  }

  /**
   * Create a new video session
   */
  createSession(sessionName: string, password?: string): ZoomSDKSession {
    const sessionKey = crypto.randomBytes(16).toString('hex');
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 4 * 60 * 60 * 1000); // 4 hours

    const session: ZoomSDKSession = {
      sessionName,
      sessionKey,
      sessionPassword: password,
      createdAt: now,
      expiresAt
    };

    this.activeSessions.set(sessionName, session);
    console.log(`🎥 Created Zoom Video SDK session: ${sessionName}`);
    
    return session;
  }

  /**
   * Get session information
   */
  getSession(sessionName: string): ZoomSDKSession | null {
    const session = this.activeSessions.get(sessionName);
    
    if (session && session.expiresAt > new Date()) {
      return session;
    }
    
    if (session) {
      // Clean up expired session
      this.activeSessions.delete(sessionName);
    }
    
    return null;
  }

  /**
   * Generate tokens for both host and participant
   */
  generateSessionTokens(sessionName: string, candidateId: string, interviewerId?: string) {
    const session = this.getSession(sessionName);
    if (!session) {
      throw new Error(`Session ${sessionName} not found or expired`);
    }

    const hostToken = this.generateSDKToken({
      role: 'host',
      sessionName,
      userIdentity: interviewerId || 'interviewer-bot',
      sessionKey: session.sessionKey
    });

    const candidateToken = this.generateSDKToken({
      role: 'participant',
      sessionName,
      userIdentity: candidateId,
      sessionKey: session.sessionKey
    });

    return {
      hostToken,
      candidateToken,
      sessionKey: session.sessionKey,
      sessionName
    };
  }

  /**
   * Clean up expired sessions
   */
  cleanupExpiredSessions(): void {
    const now = new Date();
    const expiredSessions: string[] = [];

    for (const [sessionName, session] of this.activeSessions.entries()) {
      if (session.expiresAt <= now) {
        expiredSessions.push(sessionName);
      }
    }

    expiredSessions.forEach(sessionName => {
      this.activeSessions.delete(sessionName);
      console.log(`🧹 Cleaned up expired Zoom session: ${sessionName}`);
    });

    if (expiredSessions.length > 0) {
      console.log(`🧹 Cleaned up ${expiredSessions.length} expired Zoom sessions`);
    }
  }

  /**
   * Get all active sessions
   */
  getActiveSessions(): ZoomSDKSession[] {
    this.cleanupExpiredSessions();
    return Array.from(this.activeSessions.values());
  }

  /**
   * End a session
   */
  endSession(sessionName: string): boolean {
    const deleted = this.activeSessions.delete(sessionName);
    if (deleted) {
      console.log(`🎥 Ended Zoom Video SDK session: ${sessionName}`);
    }
    return deleted;
  }

  /**
   * Generate session name for interview
   */
  generateInterviewSessionName(interviewId: string): string {
    return `interview-${interviewId}-${Date.now()}`;
  }

  /**
   * Validate token (for webhook verification)
   */
  validateToken(token: string): any {
    try {
      return jwt.verify(token, this.sdkSecret);
    } catch (error) {
      console.error('Invalid Zoom SDK token:', error);
      return null;
    }
  }
}

export const zoomVideoSDKService = new ZoomVideoSDKService();

// Clean up expired sessions every hour
setInterval(() => {
  zoomVideoSDKService.cleanupExpiredSessions();
}, 60 * 60 * 1000);
