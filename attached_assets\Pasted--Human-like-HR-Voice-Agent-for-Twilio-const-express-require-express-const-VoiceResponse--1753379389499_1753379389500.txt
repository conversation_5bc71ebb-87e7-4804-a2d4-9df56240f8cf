// Human-like HR Voice Agent for Twilio
const express = require('express');
const { VoiceResponse } = require('twilio').twiml;
const OpenAI = require('openai');

class HumanLikeHRAgent {
  constructor() {
    this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    this.conversations = new Map(); // Store conversation state
    this.usedPhrases = new Map(); // Track used phrases to avoid repetition
  }

  // Core personality and conversation prompts
  getSystemPrompt(candidate, conversationState) {
    return `You are <PERSON>, a warm and experienced HR recruiter at TechFlow Solutions. You're calling about the ${candidate.position} position.

PERSONALITY TRAITS:
- Naturally enthusiastic but professional
- Use conversational fillers like "you know", "um", "that's fantastic", "I see"
- Speak with genuine curiosity about the candidate's experience
- Show authentic interest in their career goals
- Use their name occasionally (not every response)
- Vary your language - never repeat exact phrases

CONVERSATION GUIDELINES:
- Keep responses under 30 words MAXIMUM
- Ask ONE question at a time
- Use natural transitions like "That's interesting", "Oh great", "I love that"
- Show empathy and understanding
- Avoid HR jargon - speak like a real person
- If they mention challenges, acknowledge them genuinely

CURRENT CONVERSATION CONTEXT:
- Stage: ${conversationState.stage}
- Previous topics discussed: ${conversationState.topicsDiscussed.join(', ') || 'none'}
- Candidate's mood: ${conversationState.candidateMood || 'neutral'}
- Call duration: ${conversationState.duration || 'just started'}

RESPONSE STYLE EXAMPLES:
Instead of: "Thank you for that information"
Say: "That's really helpful to know" or "I appreciate you sharing that"

Instead of: "Can you tell me about your experience"
Say: "I'd love to hear about what you've been working on" or "What's been keeping you busy lately?"

Remember: You're having a genuine conversation with a person about their career - be human, be real, be interested.`;
  }

  // Dynamic conversation stages with varied responses
  getStagePrompts() {
    return {
      greeting: {
        options: [
          "Hi {name}! This is Maya from TechFlow. I hope I'm catching you at a good time?",
          "Hey {name}, Maya here from TechFlow Solutions. Do you have a quick moment to chat?",
          "Hi there {name}! It's Maya calling about the {position} role. Is now an okay time?",
          "{name}? Hi, this is Maya from TechFlow. I was hoping to catch up with you about an opportunity."
        ],
        followUp: [
          "Perfect! I'm excited to learn more about you.",
          "Wonderful! I've been looking forward to this conversation.",
          "Great! I'd love to hear what you've been up to lately."
        ]
      },
      
      experience: {
        questions: [
          "What kind of projects have you been diving into recently?",
          "I'd love to hear about what's been exciting you at work lately.",
          "What's the most interesting thing you've worked on this year?",
          "Tell me about something you built that you're really proud of.",
          "What technologies have you been exploring lately?"
        ],
        reactions: [
          "That sounds fascinating!",
          "Oh wow, that's exactly what we're looking for.",
          "I can tell you're passionate about this stuff.",
          "That experience would be perfect for what we're building.",
          "You know, that's really impressive."
        ]
      },
      
      interest: {
        questions: [
          "So what caught your eye about this opportunity?",
          "What made you interested in potentially making a move?",
          "Are you actively looking or just curious about what's out there?",
          "What would make you excited about a new challenge?",
          "What's missing in your current role that you'd want in your next one?"
        ],
        responses: [
          "I totally get that.",
          "That makes complete sense.",
          "Yeah, I hear that a lot actually.",
          "That's a really good way to think about it.",
          "I can definitely understand wanting that."
        ]
      },
      
      scheduling: {
        options: [
          "I'd love to set up time for you to meet our team. What does your calendar look like?",
          "This sounds like a great fit! When would be good for a deeper conversation?",
          "I'm thinking we should get you talking with our tech lead. Are you free this week?",
          "Let's get something on the books. Do mornings or afternoons work better for you?",
          "How about we schedule a proper interview? What days work well for you?"
        ],
        confirmations: [
          "Perfect, I'll send you a calendar invite right after this call.",
          "Great! You'll get an email confirmation in just a few minutes.",
          "Awesome, I'll make sure you have all the details today.",
          "Sounds good! I'll get that scheduled and send you the info."
        ]
      }
    };
  }

  // Generate contextual, non-repetitive responses
  async generateResponse(conversation, userInput) {
    const { candidate, stage, history, usedPhrases } = conversation;
    
    // Update conversation context
    this.updateConversationContext(conversation, userInput);
    
    // Get available response options for current stage
    const stagePrompts = this.getStagePrompts()[stage];
    
    // Filter out recently used phrases
    const availableOptions = this.getUnusedOptions(stagePrompts, usedPhrases);
    
    const contextPrompt = `
${this.getSystemPrompt(candidate, conversation)}

RECENT CONVERSATION:
${history.slice(-3).map(h => `${h.speaker}: ${h.message}`).join('\n')}

USER JUST SAID: "${userInput}"

INSTRUCTIONS:
- Respond naturally to what they just said
- ${this.getStageSpecificInstructions(stage)}
- Don't repeat phrases you've used before in this call
- Be conversational and warm
- Keep it under 30 words
- End with a natural question or statement that moves the conversation forward

AVOID THESE PHRASES (already used): ${Array.from(usedPhrases).join(', ')}
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          { role: "system", content: contextPrompt },
          { role: "user", content: userInput }
        ],
        temperature: 0.8, // Higher creativity
        max_tokens: 50,   // Keep responses short
        frequency_penalty: 0.5, // Reduce repetition
        presence_penalty: 0.3   // Encourage new topics
      });

      const aiResponse = response.choices[0].message.content.trim();
      
      // Track used phrases
      this.trackUsedPhrases(conversation.callSid, aiResponse);
      
      // Update conversation history
      conversation.history.push(
        { speaker: 'candidate', message: userInput },
        { speaker: 'maya', message: aiResponse }
      );

      return aiResponse;
    } catch (error) {
      console.error('OpenAI error:', error);
      return this.getFallbackResponse(stage);
    }
  }

  // Update conversation context based on user input
  updateConversationContext(conversation, userInput) {
    // Detect sentiment/mood
    if (userInput.toLowerCase().includes('excited') || userInput.includes('love')) {
      conversation.candidateMood = 'enthusiastic';
    } else if (userInput.toLowerCase().includes('busy') || userInput.includes('stress')) {
      conversation.candidateMood = 'busy';
    } else if (userInput.toLowerCase().includes('not sure') || userInput.includes('maybe')) {
      conversation.candidateMood = 'hesitant';
    }

    // Track topics discussed
    if (userInput.toLowerCase().includes('react') || userInput.includes('javascript')) {
      conversation.topicsDiscussed.push('frontend development');
    }
    if (userInput.toLowerCase().includes('python') || userInput.includes('django')) {
      conversation.topicsDiscussed.push('backend development');
    }
    
    // Update call duration
    conversation.duration = Date.now() - conversation.startTime;
  }

  // Get stage-specific instructions
  getStageSpecificInstructions(stage) {
    const instructions = {
      greeting: "Be warm and check if it's a good time to talk",
      experience: "Show genuine interest in their technical experience",
      interest: "Understand their motivations and career goals", 
      availability: "Find out when they're free for interviews",
      scheduling: "Confirm interview times and next steps",
      closing: "End warmly and confirm next actions"
    };
    return instructions[stage] || "Continue the natural conversation flow";
  }

  // Track and avoid repeated phrases
  trackUsedPhrases(callSid, response) {
    if (!this.usedPhrases.has(callSid)) {
      this.usedPhrases.set(callSid, new Set());
    }
    
    // Extract key phrases (3+ words)
    const phrases = response.toLowerCase().match(/\b\w+\s+\w+\s+\w+/g) || [];
    phrases.forEach(phrase => {
      this.usedPhrases.get(callSid).add(phrase);
    });
  }

  // Get unused response options
  getUnusedOptions(stagePrompts, usedPhrases) {
    if (!stagePrompts) return [];
    
    const allOptions = [
      ...(stagePrompts.options || []),
      ...(stagePrompts.questions || []),
      ...(stagePrompts.reactions || [])
    ];
    
    return allOptions.filter(option => {
      const optionPhrases = option.toLowerCase().match(/\b\w+\s+\w+\s+\w+/g) || [];
      return !optionPhrases.some(phrase => usedPhrases.has(phrase));
    });
  }

  // Fallback responses if AI fails
  getFallbackResponse(stage) {
    const fallbacks = {
      greeting: "Hi there! This is Maya from TechFlow. Hope you're having a good day!",
      experience: "That's really interesting. Tell me more about that.",
      interest: "I'd love to hear your thoughts on that.",
      availability: "When would be a good time to continue this conversation?",
      closing: "Thanks so much for your time today!"
    };
    return fallbacks[stage] || "That's great to hear. What else can you tell me?";
  }

  // Main call handler
  async handleCall(req, res) {
    const callSid = req.body.CallSid;
    const from = req.body.From;
    
    // Initialize conversation if new
    if (!this.conversations.has(callSid)) {
      const candidate = await this.getCandidateInfo(from); // Your existing function
      
      this.conversations.set(callSid, {
        callSid,
        candidate,
        stage: 'greeting',
        history: [],
        topicsDiscussed: [],
        candidateMood: 'neutral',
        startTime: Date.now(),
        usedPhrases: new Set()
      });
      
      const greeting = this.getStagePrompts().greeting.options[0]
        .replace('{name}', candidate.name)
        .replace('{position}', candidate.position);
      
      const twiml = new VoiceResponse();
      twiml.say({
        voice: 'Polly.Joanna-Neural'
      }, greeting);
      
      twiml.gather({
        input: 'speech',
        speechTimeout: 'auto',
        action: '/handle-response'
      });
      
      res.type('text/xml');
      res.send(twiml.toString());
      return;
    }
  }

  // Handle ongoing conversation
  async handleResponse(req, res) {
    const callSid = req.body.CallSid;
    const userInput = req.body.SpeechResult;
    const conversation = this.conversations.get(callSid);
    
    if (!conversation) {
      res.status(400).send('Conversation not found');
      return;
    }

    // Generate human-like response
    const aiResponse = await this.generateResponse(conversation, userInput);
    
    // Determine next stage based on conversation flow
    this.updateConversationStage(conversation, userInput);
    
    const twiml = new VoiceResponse();
    twiml.say({
      voice: 'Polly.Joanna-Neural'
    }, aiResponse);
    
    // Continue conversation or end call
    if (conversation.stage !== 'closing') {
      twiml.gather({
        input: 'speech',
        speechTimeout: 'auto',
        action: '/handle-response'
      });
    }
    
    res.type('text/xml');
    res.send(twiml.toString());
  }

  // Update conversation stage based on natural flow
  updateConversationStage(conversation, userInput) {
    const input = userInput.toLowerCase();
    
    if (conversation.stage === 'greeting' && input.includes('yes')) {
      conversation.stage = 'experience';
    } else if (conversation.stage === 'experience' && conversation.history.length > 6) {
      conversation.stage = 'interest';
    } else if (conversation.stage === 'interest' && (input.includes('interested') || input.includes('yes'))) {
      conversation.stage = 'scheduling';
    } else if (conversation.stage === 'scheduling' && (input.includes('monday') || input.includes('tuesday') || input.includes('available'))) {
      conversation.stage = 'closing';
    }
  }

  // Your existing candidate lookup function
  async getCandidateInfo(phone) {
    // Return candidate info from your database
    return {
      name: "John", // Get from DB
      position: "Senior React Developer", // Get from DB
      phone: phone,
      experience: "5 years" // Get from DB
    };
  }
}

// Express app setup
const app = express();
app.use(express.urlencoded({ extended: false }));

const hrAgent = new HumanLikeHRAgent();

app.post('/voice', (req, res) => hrAgent.handleCall(req, res));
app.post('/handle-response', (req, res) => hrAgent.handleResponse(req, res));

app.listen(3000, () => {
  console.log('Human-like HR Agent running on port 3000');
});

module.exports = HumanLikeHRAgent;