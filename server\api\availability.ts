import { Router } from 'express';
import { db } from '../db';
import { candidateAvailability, candidates } from '@shared/schema';
import { eq } from 'drizzle-orm';

const router = Router();

/**
 * @swagger
 * /availability/request:
 *   post:
 *     summary: Request interview availability
 *     description: Send availability request email to candidate for interview scheduling
 *     tags: [Availability]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - candidateId
 *             properties:
 *               candidateId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of the candidate to request availability from
 *     responses:
 *       200:
 *         description: Availability request sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       404:
 *         description: Candidate not found
 *       500:
 *         description: Failed to send availability request
 */
router.post('/request', async (req, res) => {
  try {
    const { candidateId } = req.body;
    
    // Get candidate details
    const [candidate] = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId))
      .limit(1);

    if (!candidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    console.log('📧 Processing availability request for:', candidate.email);

    // Send email notification to candidate
    try {
      const { sendAvailabilityEmail } = await import('../services/emailService');
      const emailSent = await sendAvailabilityEmail(candidate);
      
      if (emailSent) {
        console.log('✅ Availability request email sent successfully to:', candidate.email);
      } else {
        console.log('❌ Failed to send availability request email to:', candidate.email);
      }
    } catch (emailError) {
      console.error('💥 Error sending availability request email:', emailError);
    }

    console.log('Availability request processing completed');
    res.json({ success: true, message: 'Availability request sent successfully' });
  } catch (error) {
    console.error('Error requesting availability:', error);
    res.status(500).json({ error: 'Failed to send availability request' });
  }
});

// Update candidate availability response
router.post('/:candidateId/respond', async (req, res) => {
  try {
    const { candidateId } = req.params;
    const { availableSlots, selectedSlot } = req.body;

    // Find existing availability request
    const [existingRequest] = await db
      .select()
      .from(candidateAvailability)
      .where(eq(candidateAvailability.candidateId, candidateId))
      .orderBy(candidateAvailability.createdAt)
      .limit(1);

    if (existingRequest) {
      // Update existing request
      const [updated] = await db
        .update(candidateAvailability)
        .set({
          respondedAt: new Date(),
          status: 'received',
          availableSlots: JSON.stringify(availableSlots),
          selectedSlot: JSON.stringify(selectedSlot),
          updatedAt: new Date()
        })
        .where(eq(candidateAvailability.id, existingRequest.id))
        .returning();

      res.json(updated);
    } else {
      // Create new availability response
      const [availability] = await db
        .insert(candidateAvailability)
        .values({
          candidateId,
          respondedAt: new Date(),
          status: 'received',
          availableSlots: JSON.stringify(availableSlots),
          selectedSlot: JSON.stringify(selectedSlot)
        })
        .returning();

      res.json(availability);
    }
  } catch (error) {
    console.error('Error updating availability:', error);
    res.status(500).json({ error: 'Failed to update availability' });
  }
});

// Get candidate availability
router.get('/:candidateId', async (req, res) => {
  try {
    const { candidateId } = req.params;
    
    const availability = await db
      .select()
      .from(candidateAvailability)
      .where(eq(candidateAvailability.candidateId, candidateId))
      .orderBy(candidateAvailability.createdAt);

    res.json(availability);
  } catch (error) {
    console.error('Error fetching availability:', error);
    res.status(500).json({ error: 'Failed to fetch availability' });
  }
});

export default router;