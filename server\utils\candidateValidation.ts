import { db } from '../db';
import { candidates } from '@shared/schema';
import { eq, and, gte, desc } from 'drizzle-orm';

// Environment variables with defaults
export const CANDIDATE_APPLICATION_CONFIG = {
  REAPPLICATION_COOLDOWN_HOURS: parseInt(process.env.CANDIDATE_REAPPLICATION_COOLDOWN_HOURS || '4320'), // 6 months default
  CROSS_JOB_COOLDOWN_HOURS: parseInt(process.env.CANDIDATE_CROSS_JOB_COOLDOWN_HOURS || '4320'), // 6 months default
};

export interface CandidateValidationResult {
  isValid: boolean;
  errorCode: string;
  message: string;
  data?: any;
}

/**
 * Validates if a candidate can apply for a job based on business rules:
 * 1. Only one active application at a time
 * 2. Cannot reapply to same job for specified period after rejection
 * 3. Cannot apply to different job for specified period after rejection
 */
export async function validateCandidateApplication(
  email: string, 
  appliedJobId: string, 
  organizationId: string
): Promise<CandidateValidationResult> {
  try {
    // Check for any existing applications for this email in this organization
    const existingApplications = await db
      .select()
      .from(candidates)
      .where(and(
        eq(candidates.email, email),
        eq(candidates.organizationId, organizationId)
      ))
      .orderBy(desc(candidates.createdAt));

    if (existingApplications.length === 0) {
      // First time applying - always allowed
      return { isValid: true, errorCode: '', message: 'Valid application' };
    }

    // Check for active applications (pending_review, approved_for_interview, interview_scheduled, interview_completed)
    const activeStatuses = ['pending_review', 'approved_for_interview', 'interview_scheduled', 'interview_completed'];
    const activeApplications = existingApplications.filter(app => 
      activeStatuses.includes(app.status || '')
    );

    if (activeApplications.length > 0) {
      const activeApp = activeApplications[0];
      return {
        isValid: false,
        errorCode: 'ACTIVE_APPLICATION_EXISTS',
        message: `You already have an active application for another position. Please wait for the current application to be processed before applying to new positions.`,
        data: { 
          activeJobId: activeApp.appliedJobId,
          status: activeApp.status,
          appliedAt: activeApp.createdAt 
        }
      };
    }

    // Check for recent rejections
    const rejectedApplications = existingApplications.filter(app => 
      app.status === 'rejected' || app.status === 'not_selected'
    );

    if (rejectedApplications.length > 0) {
      const cooldownDate = new Date();
      
      // Check for same job reapplication
      const sameJobRejection = rejectedApplications.find(app => app.appliedJobId === appliedJobId);
      if (sameJobRejection) {
        cooldownDate.setHours(cooldownDate.getHours() - CANDIDATE_APPLICATION_CONFIG.REAPPLICATION_COOLDOWN_HOURS);
        
        if (sameJobRejection.createdAt && new Date(sameJobRejection.createdAt) > cooldownDate) {
          const hoursRemaining = Math.ceil(
            (CANDIDATE_APPLICATION_CONFIG.REAPPLICATION_COOLDOWN_HOURS - 
             ((Date.now() - new Date(sameJobRejection.createdAt).getTime()) / (1000 * 60 * 60)))
          );
          
          return {
            isValid: false,
            errorCode: 'SAME_JOB_COOLDOWN_ACTIVE',
            message: `You cannot reapply for this position yet. Please wait ${Math.ceil(hoursRemaining / 24)} more days before reapplying.`,
            data: { 
              canReapplyAt: new Date(new Date(sameJobRejection.createdAt).getTime() + 
                           CANDIDATE_APPLICATION_CONFIG.REAPPLICATION_COOLDOWN_HOURS * 60 * 60 * 1000),
              previousApplicationDate: sameJobRejection.createdAt
            }
          };
        }
      }

      // Check for cross-job application cooldown
      const mostRecentRejection = rejectedApplications[0]; // Already sorted by createdAt desc
      cooldownDate.setHours(cooldownDate.getHours() - CANDIDATE_APPLICATION_CONFIG.CROSS_JOB_COOLDOWN_HOURS);
      
      if (mostRecentRejection.appliedJobId !== appliedJobId && 
          mostRecentRejection.createdAt && 
          new Date(mostRecentRejection.createdAt) > cooldownDate) {
        
        const hoursRemaining = Math.ceil(
          (CANDIDATE_APPLICATION_CONFIG.CROSS_JOB_COOLDOWN_HOURS - 
           ((Date.now() - new Date(mostRecentRejection.createdAt).getTime()) / (1000 * 60 * 60)))
        );
        
        return {
          isValid: false,
          errorCode: 'CROSS_JOB_COOLDOWN_ACTIVE',
          message: `You cannot apply for new positions yet due to a recent application. Please wait ${Math.ceil(hoursRemaining / 24)} more days before applying to other positions.`,
          data: { 
            canApplyAt: new Date(new Date(mostRecentRejection.createdAt).getTime() + 
                        CANDIDATE_APPLICATION_CONFIG.CROSS_JOB_COOLDOWN_HOURS * 60 * 60 * 1000),
            previousRejectionDate: mostRecentRejection.createdAt,
            previousJobId: mostRecentRejection.appliedJobId
          }
        };
      }
    }

    // All validations passed
    return { isValid: true, errorCode: '', message: 'Valid application' };

  } catch (error) {
    console.error('Error validating candidate application:', error);
    return {
      isValid: false,
      errorCode: 'VALIDATION_ERROR',
      message: 'Unable to validate application. Please try again.',
      data: { error: error instanceof Error ? error.message : 'Unknown error' }
    };
  }
}

/**
 * Business exception class for candidate validation errors
 */
export class CandidateValidationError extends Error {
  constructor(
    public errorCode: string,
    message: string,
    public data?: any
  ) {
    super(message);
    this.name = 'CandidateValidationError';
  }
}