import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { authenticateToken } from '../auth';
import { db } from '../db';
import { candidates } from '../../shared/schema';
import { eq, and } from 'drizzle-orm';
import { validateCandidateApplication, CandidateValidationError } from '../utils/candidateValidation';
import { sendCandidateValidationFailureEmail, sendCandidateApplicationSuccessEmail } from '../services/candidateEmailService';

const router = express.Router();

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(process.cwd(), 'uploads', 'resumes');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, uploadsDir);
    },
    filename: (req, file, cb) => {
      // Generate unique filename with timestamp
      const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}-${file.originalname}`;
      cb(null, uniqueName);
    }
  }),
  fileFilter: (req, file, cb) => {
    // Allow only PDF and DOC files
    const allowedTypes = ['.pdf', '.doc', '.docx'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(null, false);
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  }
});

/**
 * @swagger
 * /files/public-upload/{organizationId}:
 *   post:
 *     summary: Upload resume file (Public endpoint for job applications)
 *     description: Public endpoint for uploading resume files during job application process
 *     tags: [Files]
 *     parameters:
 *       - name: organizationId
 *         in: path
 *         required: true
 *         description: Organization ID
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               resume:
 *                 type: string
 *                 format: binary
 *                 description: Resume file (PDF, DOC, DOCX)
 *     responses:
 *       200:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 filePath:
 *                   type: string
 *                 fileName:
 *                   type: string
 *                 fileSize:
 *                   type: number
 *       400:
 *         description: No file uploaded or invalid file type
 *       500:
 *         description: Upload failed
 */
router.post('/public-submit-application', upload.single('resume'), async (req: any, res) => {
  try {
    const file = req.file;
    const { 
      organizationId, 
      jobId, 
      candidateName, 
      email, 
      phone, 
      coverLetter 
    } = req.body;

    if (!file) {
      return res.status(400).json({ error: 'No resume file uploaded' });
    }

    if (!organizationId || !jobId || !candidateName || !email) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    console.log(`Application submitted for organization ${organizationId}: ${file.originalname} (${file.size} bytes)`);

    // Generate application reference ID immediately
    const applicationId = Math.random().toString(36).substring(2, 10).toUpperCase();

    // Return success immediately to public user
    res.json({
      success: true,
      message: 'Application submitted successfully! We will review your application and get back to you soon.',
      applicationId: applicationId
    });

    // Process application asynchronously (don't await)
    processApplicationAsync({
      file,
      organizationId,
      jobId,
      candidateName,
      email,
      phone,
      coverLetter,
      applicationId
    }).catch(error => {
      console.error('Async application processing failed:', error);
      // Could log to error tracking service here
    });

  } catch (error) {
    console.error('Error in public application submission:', error);
    res.status(500).json({ error: 'Failed to submit application' });
  }
});

// Async function to process application in background
async function processApplicationAsync(applicationData: {
  file: any;
  organizationId: string;
  jobId: string;
  candidateName: string;
  email: string;
  phone: string;
  coverLetter: string;
  applicationId: string;
}) {
  const { file, organizationId, jobId, candidateName, email, phone, coverLetter, applicationId } = applicationData;
  
  try {
    console.log(`Processing application ${applicationId} asynchronously...`);

    // 1. Get job details
    const { db } = await import('../db');
    const { jobPostings, candidates } = await import('../../shared/schema');
    const { eq } = await import('drizzle-orm');
    
    const [job] = await db.select().from(jobPostings).where(eq(jobPostings.id, jobId));
    if (!job) {
      console.error(`Job posting not found for application ${applicationId}: ${jobId}`);
      return;
    }

    // CRITICAL SECURITY FIX: Validate organizationId matches job posting
    if (job.organizationId !== organizationId) {
      console.error(`🚨 SECURITY VIOLATION: Organization mismatch for application ${applicationId}. Job org: ${job.organizationId}, provided org: ${organizationId}`);
      return;
    }

    const jobDescription = `${job.title}\n\n${job.description}\n\nRequirements: ${job.requirements || 'Not specified'}`;

    // 2. CRITICAL: Business validation BEFORE expensive operations - STRICT ENFORCEMENT
    const validationResult = await validateCandidateApplication(email, jobId, job.organizationId);
    if (!validationResult.isValid) {
      console.error(`🚫 VALIDATION BLOCKED: Application ${applicationId} rejected for ${email}:`, {
        errorCode: validationResult.errorCode,
        message: validationResult.message,
        data: validationResult.data
      });
      
      // Send email notification to candidate about validation failure
      try {
        await sendCandidateValidationFailureEmail({
          candidateName,
          candidateEmail: email,
          errorCode: validationResult.errorCode,
          errorMessage: validationResult.message,
          data: validationResult.data,
          jobTitle: job.title,
          organizationName: job.organizationId // You might want to fetch organization name from organizations table
        });
        console.log(`📧 Validation failure email sent to ${email} for ${validationResult.errorCode}`);
      } catch (emailError) {
        console.error(`📧 Failed to send validation failure email to ${email}:`, emailError);
      }
      
      // STRICT ENFORCEMENT: Stop all processing for invalid applications
      return;
    }
    
    console.log(`✅ Validation passed for application ${applicationId}, proceeding with processing...`);

    // 3. Import resume analysis functions AFTER validation
    const resumeModule = await import('./resume');
    
    const fileContent = file.buffer ? file.buffer.toString('base64') : Buffer.from('').toString('base64');
    const fileName = file.originalname;
    const fileType = file.mimetype;

    console.log(`Analyzing resume for application ${applicationId}...`);
    const resumeData = await resumeModule.parseResumeWithTypeScript(fileContent, fileName, fileType, jobDescription);

    let analysis = null;
    if (process.env.OPENAI_API_KEY && jobDescription && resumeData.extractedText) {
      try {
        analysis = await resumeModule.analyzeWithOpenAI(resumeData.extractedText, jobDescription, process.env.OPENAI_API_KEY, jobId);
      } catch (openaiError) {
        console.log(`OpenAI analysis failed for application ${applicationId}, using fallback`);
        analysis = resumeModule.generateIntelligentFallback(resumeData.extractedText, jobDescription);
      }
    } else {
      analysis = resumeModule.generateIntelligentFallback(resumeData.extractedText || '', jobDescription || 'General position analysis');
    }

    // 4. Save resume file
    const resumeUrl = `/api/files/download-public/${file.filename}`;

    // 5. Create or update candidate record
    const candidateData = {
      email,
      fullName: candidateName,
      phone: phone || null,
      resumeUrl,
      resumeFileName: fileName,
      resumeFileSize: file.size,
      resumeText: resumeData.extractedText,
      skills: resumeData.skills || null,
      organizationId: job.organizationId, // SECURITY: Use job's org ID, not client-provided
      appliedJobId: jobId,
      analysisResult: analysis,
      overallScore: analysis?.overallScore || null,
      matchScore: analysis?.matchScore || null,
      recommendation: analysis?.recommendation || null,
      status: 'pending_review' as const,
      sourceType: 'direct_application' as const,
      sourceChannel: 'public_portal',
      notes: `Application submitted via public portal. Reference ID: ${applicationId}`
    };

    // Check if candidate already exists for this specific job posting and organization
    const [existingCandidate] = await db.select().from(candidates).where(and(
      eq(candidates.email, email),
      eq(candidates.appliedJobId, jobId),
      eq(candidates.organizationId, organizationId)
    ));
    
    let savedCandidate;
    if (existingCandidate) {
      // Update existing candidate
      [savedCandidate] = await db
        .update(candidates)
        .set({
          ...candidateData,
          updatedAt: new Date()
        })
        .where(eq(candidates.id, existingCandidate.id))
        .returning();
      
      console.log(`Updated existing candidate for application ${applicationId}: ${savedCandidate.id}`);
    } else {
      // Create new candidate
      [savedCandidate] = await db
        .insert(candidates)
        .values(candidateData)
        .returning();
      
      console.log(`Created new candidate for application ${applicationId}: ${savedCandidate.id}`);
    }

    // Send success email notification to candidate
    try {
      await sendCandidateApplicationSuccessEmail(
        candidateName,
        email,
        job.title,
        job.organizationId // You might want to fetch organization name from organizations table
      );
      console.log(`📧 Application success email sent to ${email}`);
    } catch (emailError) {
      console.error(`📧 Failed to send application success email to ${email}:`, emailError);
    }

    console.log(`Application ${applicationId} processed successfully`);

  } catch (processingError) {
    console.error(`Error processing application ${applicationId}:`, processingError);
    // Could implement retry logic or error notification here
  }
}

/**
 * @swagger
 * /files/upload/{candidateId}:
 *   post:
 *     summary: Upload resume file
 *     description: Upload resume file for a specific candidate
 *     tags: [Files]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: candidateId
 *         in: path
 *         required: true
 *         description: Candidate ID
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               resume:
 *                 type: string
 *                 format: binary
 *                 description: Resume file (PDF, DOC, DOCX)
 *     responses:
 *       200:
 *         description: File uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 resumeFilePath:
 *                   type: string
 *                 originalName:
 *                   type: string
 *                 fileSize:
 *                   type: number
 *       400:
 *         description: No file uploaded or invalid file type
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Candidate not found
 *       500:
 *         description: Upload failed
 */
router.post('/upload/:candidateId', authenticateToken, upload.single('resume'), async (req: any, res) => {
  try {
    const { candidateId } = req.params;
    const file = req.file;

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Update candidate with resume file info
    const resumeUrl = `/api/files/download/${candidateId}/${file.filename}`;
    
    await db.update(candidates)
      .set({
        resumeUrl,
        resumeFileName: file.originalname,
        resumeFileSize: file.size
      })
      .where(and(
        eq(candidates.id, candidateId),
        eq(candidates.organizationId, req.user.organizationId)
      ));

    console.log(`Resume file uploaded for candidate ${candidateId}: ${file.originalname} (${file.size} bytes)`);

    res.json({
      message: 'Resume uploaded successfully',
      resumeUrl,
      fileName: file.originalname,
      fileSize: file.size
    });

  } catch (error) {
    console.error('Error uploading resume:', error);
    res.status(500).json({ error: 'Failed to upload resume' });
  }
});

// Download resume file
router.get('/download/:candidateId/:filename', authenticateToken, async (req: any, res) => {
  try {
    const { candidateId, filename } = req.params;
    
    // Verify candidate belongs to user's organization
    const [candidate] = await db.select()
      .from(candidates)
      .where(and(
        eq(candidates.id, candidateId),
        eq(candidates.organizationId, req.user.organizationId)
      ));
      
    if (!candidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    const filePath = path.join(uploadsDir, filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'File not found' });
    }

    // Set appropriate headers for file download
    res.setHeader('Content-Disposition', `attachment; filename="${candidate.resumeFileName || filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    
    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    console.error('Error downloading resume:', error);
    res.status(500).json({ error: 'Failed to download resume' });
  }
});

// Get resume info for candidate
router.get('/info/:candidateId', authenticateToken, async (req: any, res) => {
  try {
    const { candidateId } = req.params;
    
    console.log(`Getting resume info for candidate: ${candidateId}, organization: ${req.user.organizationId}`);
    
    const candidate = await db.select({
      id: candidates.id,
      resumeUrl: candidates.resumeUrl,
      resumeFileName: candidates.resumeFileName,
      resumeFileSize: candidates.resumeFileSize,
      organizationId: candidates.organizationId
    })
      .from(candidates)
      .where(and(
        eq(candidates.id, candidateId),
        eq(candidates.organizationId, req.user.organizationId)
      ))
      .limit(1);
      
    console.log(`Query result:`, candidate);
      
    if (!candidate.length) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    const candidateData = candidate[0];

    if (!candidateData.resumeUrl) {
      return res.json({
        hasResume: false,
        resumeUrl: null,
        fileName: null,
        fileSize: 0
      });
    }

    res.json({
      hasResume: true,
      resumeUrl: candidateData.resumeUrl,
      fileName: candidateData.resumeFileName || 'resume.pdf',
      fileSize: candidateData.resumeFileSize || 0
    });

  } catch (error) {
    console.error('Error fetching resume info:', error);
    res.status(500).json({ error: 'Failed to fetch resume info' });
  }
});

export default router;