import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Building2, 
  MapPin, 
  DollarSign, 
  Clock, 
  Users, 
  Target,
  ExternalLink,
  RefreshCw,
  Search,
  Filter,
  BarChart3,
  User<PERSON>he<PERSON>,
  UserX,
  Calendar,
  Award
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  department?: string;
  location?: string;
  salaryRange?: string;
  employmentType: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  educationRequirement?: string;
  keywords?: string[];
  sourcePlatform?: string;
  sourceUrl?: string;
  isActive: boolean;
  candidateCount?: number;
  createdAt: string;
  updatedAt: string;
  lastSynced?: string;
  aiGeneratedSummary?: string;
}

interface JobAnalytics {
  totalApplications: number;
  pendingReview: number;
  approvedForInterview: number;
  rejected: number;
  hired: number;
  averageScore: number;
}

interface AutoMatchResult {
  jobPostingId: string;
  matchedCandidates: number;
  matches: Array<{
    candidateId: string;
    candidateName: string;
    matchScore: number;
    matchedSkills: string[];
  }>;
}

export default function JobPostingManager() {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [selectedJob, setSelectedJob] = useState<JobPosting | null>(null);
  const [jobAnalytics, setJobAnalytics] = useState<JobAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('all');
  const [autoMatchResults, setAutoMatchResults] = useState<AutoMatchResult | null>(null);
  const { toast } = useToast();

  // Form state for creating new job posting
  const [newJob, setNewJob] = useState({
    title: '',
    description: '',
    requirements: '',
    department: '',
    location: '',
    salaryRange: '',
    employmentType: 'full-time',
    skillsRequired: '',
    experienceLevel: '',
    educationRequirement: '',
    keywords: '',
    sourceUrl: '',
    sourcePlatform: '',
  });

  useEffect(() => {
    fetchJobPostings();
  }, []);

  const fetchJobPostings = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/job-postings');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
      } else {
        throw new Error('Failed to fetch job postings');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load job postings",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchJobDetails = async (jobId: string) => {
    try {
      const response = await fetch(`/api/job-postings/${jobId}`);
      if (response.ok) {
        const data = await response.json();
        setSelectedJob(data);
        setJobAnalytics(data.analytics);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load job details",
        variant: "destructive",
      });
    }
  };

  const createJobPosting = async () => {
    setIsCreating(true);
    try {
      const payload = {
        ...newJob,
        skillsRequired: newJob.skillsRequired.split(',').map(s => s.trim()).filter(Boolean),
        keywords: newJob.keywords.split(',').map(s => s.trim()).filter(Boolean),
      };

      const response = await fetch('/api/job-postings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Job posting created successfully",
        });
        fetchJobPostings();
        setNewJob({
          title: '', description: '', requirements: '', department: '',
          location: '', salaryRange: '', employmentType: 'full-time',
          skillsRequired: '', experienceLevel: '', educationRequirement: '',
          keywords: '', sourceUrl: '', sourcePlatform: '',
        });
      } else {
        throw new Error('Failed to create job posting');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create job posting",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  const autoMatchCandidates = async (jobId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/job-postings/${jobId}/auto-match`, {
        method: 'POST',
      });

      if (response.ok) {
        const matchResults = await response.json();
        setAutoMatchResults(matchResults);
        toast({
          title: "Auto-Match Complete",
          description: `Found ${matchResults.matchedCandidates} potential matches`,
        });
        fetchJobPostings(); // Refresh to show updated candidate counts
      } else {
        throw new Error('Failed to auto-match candidates');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to auto-match candidates",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredJobPostings = jobPostings.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         job.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDepartment = filterDepartment === 'all' || job.department === filterDepartment;
    return matchesSearch && matchesDepartment;
  });

  const departments = Array.from(new Set(jobPostings.map(job => job.department).filter(Boolean)));

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
              Job Posting Manager
            </h1>
            <p className="text-gray-600 mt-2">Manage job postings and auto-match candidates with AI-powered insights</p>
          </div>
          
          <Dialog>
            <DialogTrigger asChild>
              <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700">
                <Plus className="w-4 h-4 mr-2" />
                Create Job Posting
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Create New Job Posting</DialogTitle>
                <DialogDescription>
                  Create a new job posting with AI-powered candidate matching
                </DialogDescription>
              </DialogHeader>
              
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Job Title *</Label>
                    <Input
                      id="title"
                      value={newJob.title}
                      onChange={(e) => setNewJob({...newJob, title: e.target.value})}
                      placeholder="Senior Software Engineer"
                    />
                  </div>
                  <div>
                    <Label htmlFor="department">Department</Label>
                    <Input
                      id="department"
                      value={newJob.department}
                      onChange={(e) => setNewJob({...newJob, department: e.target.value})}
                      placeholder="Engineering"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Job Description *</Label>
                  <Textarea
                    id="description"
                    value={newJob.description}
                    onChange={(e) => setNewJob({...newJob, description: e.target.value})}
                    placeholder="Detailed job description..."
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="requirements">Requirements</Label>
                  <Textarea
                    id="requirements"
                    value={newJob.requirements}
                    onChange={(e) => setNewJob({...newJob, requirements: e.target.value})}
                    placeholder="Job requirements..."
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Location</Label>
                    <Input
                      id="location"
                      value={newJob.location}
                      onChange={(e) => setNewJob({...newJob, location: e.target.value})}
                      placeholder="San Francisco, CA"
                    />
                  </div>
                  <div>
                    <Label htmlFor="salaryRange">Salary Range</Label>
                    <Input
                      id="salaryRange"
                      value={newJob.salaryRange}
                      onChange={(e) => setNewJob({...newJob, salaryRange: e.target.value})}
                      placeholder="$120k - $180k"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="employmentType">Employment Type</Label>
                    <Select value={newJob.employmentType} onValueChange={(value) => setNewJob({...newJob, employmentType: value})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full-time">Full-time</SelectItem>
                        <SelectItem value="part-time">Part-time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="internship">Internship</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="experienceLevel">Experience Level</Label>
                    <Select value={newJob.experienceLevel} onValueChange={(value) => setNewJob({...newJob, experienceLevel: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="entry">Entry Level</SelectItem>
                        <SelectItem value="mid">Mid Level</SelectItem>
                        <SelectItem value="senior">Senior Level</SelectItem>
                        <SelectItem value="executive">Executive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="skillsRequired">Required Skills (comma-separated)</Label>
                  <Input
                    id="skillsRequired"
                    value={newJob.skillsRequired}
                    onChange={(e) => setNewJob({...newJob, skillsRequired: e.target.value})}
                    placeholder="React, TypeScript, Node.js, PostgreSQL"
                  />
                </div>

                <div>
                  <Label htmlFor="keywords">Keywords (comma-separated)</Label>
                  <Input
                    id="keywords"
                    value={newJob.keywords}
                    onChange={(e) => setNewJob({...newJob, keywords: e.target.value})}
                    placeholder="frontend, backend, full-stack, remote"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="sourcePlatform">Source Platform</Label>
                    <Input
                      id="sourcePlatform"
                      value={newJob.sourcePlatform}
                      onChange={(e) => setNewJob({...newJob, sourcePlatform: e.target.value})}
                      placeholder="company_website, linkedin, indeed"
                    />
                  </div>
                  <div>
                    <Label htmlFor="sourceUrl">Source URL</Label>
                    <Input
                      id="sourceUrl"
                      value={newJob.sourceUrl}
                      onChange={(e) => setNewJob({...newJob, sourceUrl: e.target.value})}
                      placeholder="https://company.com/careers/123"
                    />
                  </div>
                </div>

                <Button 
                  onClick={createJobPosting} 
                  disabled={isCreating || !newJob.title || !newJob.description}
                  className="w-full"
                >
                  {isCreating ? 'Creating...' : 'Create Job Posting'}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
          <CardContent className="p-4">
            <div className="flex gap-4 items-center">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search job postings..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-48">
                <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                  <SelectTrigger>
                    <Filter className="w-4 h-4 mr-2" />
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.map(dept => (
                      <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Job Postings List */}
          <div className="lg:col-span-2">
            <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="w-5 h-5" />
                  Active Job Postings ({filteredJobPostings.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[600px]">
                  <div className="space-y-4">
                    {filteredJobPostings.map((job) => (
                      <Card 
                        key={job.id} 
                        className={`cursor-pointer transition-all hover:shadow-md ${
                          selectedJob?.id === job.id ? 'ring-2 ring-indigo-500' : ''
                        }`}
                        onClick={() => fetchJobDetails(job.id)}
                      >
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <h3 className="font-semibold text-lg">{job.title}</h3>
                            <div className="flex gap-2">
                              {job.sourcePlatform && (
                                <Badge variant="outline">{job.sourcePlatform}</Badge>
                              )}
                              <Badge className="bg-green-100 text-green-800">
                                {job.candidateCount || 0} candidates
                              </Badge>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 mb-3">
                            {job.department && (
                              <div className="flex items-center gap-1">
                                <Building2 className="w-4 h-4" />
                                {job.department}
                              </div>
                            )}
                            {job.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="w-4 h-4" />
                                {job.location}
                              </div>
                            )}
                            {job.salaryRange && (
                              <div className="flex items-center gap-1">
                                <DollarSign className="w-4 h-4" />
                                {job.salaryRange}
                              </div>
                            )}
                            <div className="flex items-center gap-1">
                              <Clock className="w-4 h-4" />
                              {job.employmentType}
                            </div>
                          </div>

                          {job.skillsRequired && job.skillsRequired.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-3">
                              {job.skillsRequired.slice(0, 4).map((skill, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {skill}
                                </Badge>
                              ))}
                              {job.skillsRequired.length > 4 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{job.skillsRequired.length - 4} more
                                </Badge>
                              )}
                            </div>
                          )}

                          {job.aiGeneratedSummary && (
                            <p className="text-sm text-gray-700 bg-blue-50 p-2 rounded">
                              <Award className="w-4 h-4 inline mr-1" />
                              AI: {job.aiGeneratedSummary}
                            </p>
                          )}

                          <div className="flex justify-between items-center mt-3">
                            <span className="text-xs text-gray-500">
                              Created {new Date(job.createdAt).toLocaleDateString()}
                            </span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={(e) => {
                                e.stopPropagation();
                                autoMatchCandidates(job.id);
                              }}
                              disabled={isLoading}
                            >
                              <Target className="w-4 h-4 mr-1" />
                              Auto-Match
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Job Details & Analytics */}
          <div className="space-y-6">
            {selectedJob ? (
              <>
                <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      Job Analytics
                      <BarChart3 className="w-5 h-5" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {jobAnalytics && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="text-center p-3 bg-blue-50 rounded">
                            <div className="text-2xl font-bold text-blue-600">
                              {jobAnalytics.totalApplications}
                            </div>
                            <div className="text-sm text-gray-600">Total Applications</div>
                          </div>
                          <div className="text-center p-3 bg-green-50 rounded">
                            <div className="text-2xl font-bold text-green-600">
                              {jobAnalytics.averageScore}%
                            </div>
                            <div className="text-sm text-gray-600">Avg Score</div>
                          </div>
                        </div>

                        <Separator />

                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="flex items-center gap-2">
                              <UserCheck className="w-4 h-4 text-green-600" />
                              Approved for Interview
                            </span>
                            <Badge className="bg-green-100 text-green-800">
                              {jobAnalytics.approvedForInterview}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="flex items-center gap-2">
                              <Calendar className="w-4 h-4 text-blue-600" />
                              Pending Review
                            </span>
                            <Badge className="bg-blue-100 text-blue-800">
                              {jobAnalytics.pendingReview}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="flex items-center gap-2">
                              <UserX className="w-4 h-4 text-red-600" />
                              Rejected
                            </span>
                            <Badge className="bg-red-100 text-red-800">
                              {jobAnalytics.rejected}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="flex items-center gap-2">
                              <Award className="w-4 h-4 text-purple-600" />
                              Hired
                            </span>
                            <Badge className="bg-purple-100 text-purple-800">
                              {jobAnalytics.hired}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {selectedJob.sourceUrl && (
                  <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                    <CardContent className="p-4">
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => window.open(selectedJob.sourceUrl, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4 mr-2" />
                        View Original Posting
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            ) : (
              <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                <CardContent className="p-8 text-center">
                  <Building2 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-600">Select a job posting to view details and analytics</p>
                </CardContent>
              </Card>
            )}

            {/* Auto-Match Results */}
            {autoMatchResults && (
              <Card className="backdrop-blur-sm bg-white/70 border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Auto-Match Results
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <p className="text-sm text-gray-600">
                      Found {autoMatchResults.matchedCandidates} potential matches
                    </p>
                    {autoMatchResults.matches.slice(0, 5).map((match, index) => (
                      <div key={match.candidateId} className="p-3 bg-gray-50 rounded">
                        <div className="flex justify-between items-center mb-1">
                          <span className="font-medium">{match.candidateName}</span>
                          <Badge className="bg-indigo-100 text-indigo-800">
                            {match.matchScore}% match
                          </Badge>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {match.matchedSkills.map((skill, skillIndex) => (
                            <Badge key={skillIndex} variant="secondary" className="text-xs">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}