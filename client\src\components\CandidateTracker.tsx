import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Filter, 
  MapPin, 
  Calendar,
  Mail,
  Phone,
  CheckCircle,
  Clock,
  XCircle,
  AlertCircle,
  Eye,
  UserCheck,
  UserX
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import CandidateDetailsModal from './CandidateDetailsModal';

interface Candidate {
  id: string;
  fullName: string;
  email: string;
  phone?: string;
  location?: string;
  status: string;
  overallScore?: number;
  matchScore?: number;
  experienceYears?: number;
  analysisResult?: any;
  recommendation?: string;
  createdAt: string;
  updatedAt: string;
  appliedJobId?: string;
  skills?: string[];
  currentCompany?: string;
  currentPosition?: string;
  sourceType?: 'direct_application' | 'internal_search' | 'referral' | 'headhunter' | 'job_board';
  sourceChannel?: string;
}

interface JobPosting {
  id: string;
  title: string;
  department: string;
  location: string;
  salaryRange: string;
  employmentType: string;
  skillsRequired: string[];
  experienceLevel: string;
  isActive: boolean;
}

interface FilterState {
  categories: string[];
  locations: string[];
  jobTypes: string[];
}

export default function CandidateTracker() {
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [filteredCandidates, setFilteredCandidates] = useState<Candidate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('all');
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    categories: [],
    locations: [],
    jobTypes: []
  });
  const { toast } = useToast();

  const categories = ['Engineering', 'IT', 'Marketing', 'Product', 'Operations', 'Sales', 'Administrative'];
  const locations = ['Toronto, Canada', 'New York, USA', 'Lead, UK', 'India', 'Remote'];
  const jobTypes = ['Full-Time', 'Part-Time', 'Contractor'];

  useEffect(() => {
    fetchAllCandidates();
    fetchJobPostings();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [candidates, activeTab, filters]);

  const fetchAllCandidates = async () => {
    try {
      const response = await fetch('/api/candidates');
      if (response.ok) {
        const data = await response.json();
        setCandidates(data);
      } else {
        console.error('Error fetching candidates:', await response.text());
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
      toast({
        title: "Error",
        description: "Failed to load candidates",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchJobPostings = async () => {
    try {
      const response = await fetch('/api/job-postings/all');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
      }
    } catch (error) {
      console.error('Error fetching job postings:', error);
    }
  };

  const applyFilters = () => {
    let filtered = candidates;

    // Tab filter
    if (activeTab !== 'all') {
      const statusMap = {
        'open': ['pending_review', 'approved_for_interview'],
        'hold': ['on_hold'],
        'closed': ['rejected', 'hired'],
        'drafts': ['draft']
      };
      if (statusMap[activeTab as keyof typeof statusMap]) {
        filtered = filtered.filter(candidate => 
          statusMap[activeTab as keyof typeof statusMap].includes(candidate.status)
        );
      }
    }

    // Category filter
    if (filters.categories.length > 0) {
      filtered = filtered.filter(candidate => {
        const candidateCategory = getCandidateCategory(candidate);
        return filters.categories.includes(candidateCategory);
      });
    }

    // Location filter
    if (filters.locations.length > 0) {
      filtered = filtered.filter(candidate => 
        candidate.location && filters.locations.some(loc => 
          candidate.location?.toLowerCase().includes(loc.toLowerCase())
        )
      );
    }

    setFilteredCandidates(filtered);
  };

  const getJobTitle = (candidate: Candidate) => {
    if (!candidate.appliedJobId) return 'General Application';
    const jobPosting = jobPostings.find(job => job.id === candidate.appliedJobId);
    return jobPosting?.title || 'Unknown Position';
  };

  const getCandidateCategory = (candidate: Candidate) => {
    // First try to get category from the job posting they applied to
    if (candidate.appliedJobId) {
      const jobPosting = jobPostings.find(job => job.id === candidate.appliedJobId);
      if (jobPosting?.department) {
        return jobPosting.department;
      }
    }

    // Fallback to skills-based categorization
    if (!candidate.skills) return 'Administrative';
    const skills = candidate.skills.join(' ').toLowerCase();
    
    // Enhanced ERP/Management categorization
    if (skills.includes('erp') || skills.includes('sap') || skills.includes('oracle') || skills.includes('director') || skills.includes('management') || skills.includes('enterprise') || skills.includes('crm') || skills.includes('salesforce')) {
      return 'IT';
    } else if (skills.includes('react') || skills.includes('javascript') || skills.includes('python') || skills.includes('engineering') || skills.includes('software') || skills.includes('developer') || skills.includes('programming') || skills.includes('ai') || skills.includes('machine learning') || skills.includes('ml') || skills.includes('artificial intelligence')) {
      return 'Engineering';
    } else if (skills.includes('it') || skills.includes('information technology') || skills.includes('network') || skills.includes('system') || skills.includes('database') || skills.includes('server') || skills.includes('infrastructure') || skills.includes('technical support')) {
      return 'IT';
    } else if (skills.includes('marketing') || skills.includes('seo') || skills.includes('content') || skills.includes('social media') || skills.includes('advertising')) {
      return 'Marketing';
    } else if (skills.includes('product') || skills.includes('design') || skills.includes('ux') || skills.includes('ui') || skills.includes('user experience')) {
      return 'Product';
    } else if (skills.includes('sales') || skills.includes('business') || skills.includes('account') || skills.includes('customer relationship')) {
      return 'Sales';
    } else if (skills.includes('operations') || skills.includes('project') || skills.includes('logistics') || skills.includes('supply chain')) {
      return 'Operations';
    }
    return 'Administrative';
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending_review':
      case 'approved_for_interview':
        return {
          label: 'Open',
          className: 'inline-flex items-center px-3 py-1 rounded-full border border-[#a5b4fc] bg-[#f5f3ff] text-[#5f3dc4] text-sm font-semibold',
          dotColor: 'bg-[#5f3dc4]'
        };
      case 'on_hold':
        return {
          label: 'Hold',
          className: 'inline-flex items-center px-3 py-1 rounded-full border border-[#fde68a] bg-[#fef9c3] text-[#eab308] text-sm font-semibold',
          dotColor: 'bg-[#eab308]'
        };
      case 'rejected':
      case 'hired':
        return {
          label: 'Closed',
          className: 'inline-flex items-center px-3 py-1 rounded-full border border-[#fca5a5] bg-[#fef2f2] text-[#ef4444] text-sm font-semibold',
          dotColor: 'bg-[#ef4444]'
        };
      default:
        return {
          label: 'Draft',
          className: 'inline-flex items-center px-3 py-1 rounded-full border border-[#d1d5db] bg-[#f9fafb] text-[#6b7280] text-sm font-semibold',
          dotColor: 'bg-[#6b7280]'
        };
    }
  };

  const handleFilterChange = (filterType: keyof FilterState, value: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: checked 
        ? [...prev[filterType], value]
        : prev[filterType].filter(item => item !== value)
    }));
  };

  const getTabCount = (tabName: string) => {
    if (tabName === 'drafts') {
      return candidates.filter(c => c.status === 'draft').length;
    }
    return null;
  };

  const getSalaryRange = (candidate: Candidate) => {
    // Generate a salary range based on experience and role
    const baseMin = 60000;
    const baseMax = 80000;
    const experienceMultiplier = (candidate.experienceYears || 0) * 5000;
    
    const min = Math.round((baseMin + experienceMultiplier) / 1000);
    const max = Math.round((baseMax + experienceMultiplier + 20000) / 1000);
    
    return `$${min}K - $${max}K`;
  };

  const getSourceTypeInfo = (sourceType?: string) => {
    switch (sourceType) {
      case 'internal_search':
        return {
          label: 'Internal Search',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-blue-100 text-blue-800 text-xs font-medium',
          icon: '🔍'
        };
      case 'direct_application':
        return {
          label: 'Direct Application',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium',
          icon: '📝'
        };
      case 'referral':
        return {
          label: 'Referral',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-purple-100 text-purple-800 text-xs font-medium',
          icon: '👥'
        };
      case 'headhunter':
        return {
          label: 'Headhunter',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-orange-100 text-orange-800 text-xs font-medium',
          icon: '🎯'
        };
      case 'job_board':
        return {
          label: 'Job Board',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-800 text-xs font-medium',
          icon: '📋'
        };
      default:
        return {
          label: 'Unknown',
          className: 'inline-flex items-center px-2 py-1 rounded-full bg-gray-100 text-gray-600 text-xs font-medium',
          icon: '❓'
        };
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading candidates...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      className="h-screen overflow-y-auto"
      style={{ backgroundColor: '#FBFAFF' }}
    >
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2" style={{ color: '#0152FF' }}>Applications</h1>
          <p className="text-gray-600">Review and manage candidate applications</p>
        </div>

        {/* Horizontal Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
          <div className="flex items-center gap-6">
            {/* Category Filter */}
            <div className="flex-1">
              <label className="block text-sm font-semibold text-gray-700 mb-2">Category</label>
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <label
                    key={category}
                    className={`inline-flex items-center px-3 py-1.5 rounded-full border cursor-pointer transition-all ${
                      filters.categories.includes(category)
                        ? 'bg-blue-100 border-blue-500 text-blue-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:border-blue-400'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={filters.categories.includes(category)}
                      onChange={(e) => handleFilterChange('categories', category, e.target.checked)}
                      className="sr-only"
                    />
                    <span className="text-sm font-medium">{category}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Location Filter */}
            <div className="flex-1">
              <label className="block text-sm font-semibold text-gray-700 mb-2">Location</label>
              <div className="flex flex-wrap gap-2">
                {locations.map((location) => (
                  <label
                    key={location}
                    className={`inline-flex items-center px-3 py-1.5 rounded-full border cursor-pointer transition-all ${
                      filters.locations.includes(location)
                        ? 'bg-green-100 border-green-500 text-green-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:border-green-400'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={filters.locations.includes(location)}
                      onChange={(e) => handleFilterChange('locations', location, e.target.checked)}
                      className="sr-only"
                    />
                    <span className="text-sm font-medium">{location}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Job Type Filter */}
            <div className="flex-1">
              <label className="block text-sm font-semibold text-gray-700 mb-2">Job Type</label>
              <div className="flex flex-wrap gap-2">
                {jobTypes.map((jobType) => (
                  <label
                    key={jobType}
                    className={`inline-flex items-center px-3 py-1.5 rounded-full border cursor-pointer transition-all ${
                      filters.jobTypes.includes(jobType)
                        ? 'bg-purple-100 border-purple-500 text-purple-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:border-purple-400'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={filters.jobTypes.includes(jobType)}
                      onChange={(e) => handleFilterChange('jobTypes', jobType, e.target.checked)}
                      className="sr-only"
                    />
                    <span className="text-sm font-medium">{jobType}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Clear Filters Button */}
          {(filters.categories.length > 0 || filters.locations.length > 0 || filters.jobTypes.length > 0) && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <button
                onClick={() => setFilters({ categories: [], locations: [], jobTypes: [] })}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="flex items-center border-b border-gray-200 px-4">
            {[
              { id: 'all', label: 'All' },
              { id: 'open', label: 'Open' },
              { id: 'hold', label: 'Hold' },
              { id: 'closed', label: 'Closed' },
              { id: 'drafts', label: 'Drafts' }
            ].map((tab) => {
              const count = getTabCount(tab.id);
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-3 text-sm font-medium focus:outline-none transition-colors ${
                    activeTab === tab.id
                      ? 'text-blue-600 font-semibold border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  {tab.label}
                  {count !== null && (
                    <span className="ml-1 text-xs text-gray-500">({count})</span>
                  )}
                </button>
              );
            })}
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Candidate Name
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Job Title
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Source
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Experience
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Salary Range
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    Location
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredCandidates.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-4 py-8 text-center text-gray-500">
                      No candidates found matching the selected filters.
                    </td>
                  </tr>
                ) : (
                  filteredCandidates.map((candidate) => {
                    const statusInfo = getStatusInfo(candidate.status);
                    const category = getCandidateCategory(candidate);
                    const jobTitle = getJobTitle(candidate);
                    const sourceInfo = getSourceTypeInfo(candidate.sourceType);

                    return (
                      <tr
                        key={candidate.id}
                        className="hover:bg-gray-50 cursor-pointer transition-colors"
                        onClick={() => {
                          setSelectedCandidate(candidate);
                          setShowModal(true);
                        }}
                      >
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="font-medium text-gray-900">{candidate.fullName}</div>
                          <div className="text-sm text-gray-500">{candidate.email}</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                          {jobTitle}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                          {category}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={sourceInfo.className}>
                            <span className="mr-1">{sourceInfo.icon}</span>
                            {sourceInfo.label}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <span className={statusInfo.className}>
                            <span className={`w-1.5 h-1.5 rounded-full ${statusInfo.dotColor} mr-1.5`}></span>
                            {statusInfo.label}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                          {candidate.experienceYears ? `${candidate.experienceYears} years` : 'Not specified'}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700">
                          {getSalaryRange(candidate)}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-700">
                            <MapPin className="w-4 h-4 mr-1 text-gray-400" />
                            {candidate.location || 'Not specified'}
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Candidate Details Modal */}
      {showModal && selectedCandidate && (
        <CandidateDetailsModal
          candidate={selectedCandidate}
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setSelectedCandidate(null);
          }}
          onCandidateUpdate={(updatedCandidate) => {
            setCandidates(prev =>
              prev.map(c => c.id === updatedCandidate.id ? updatedCandidate : c)
            );
          }}
        />
      )}
    </div>
  );
}