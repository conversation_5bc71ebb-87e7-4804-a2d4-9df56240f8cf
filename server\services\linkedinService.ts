import axios from 'axios';

interface LinkedInProfile {
  id: string;
  firstName: string;
  lastName: string;
  headline: string;
  location: {
    country: string;
    region: string;
  };
  industry: string;
  summary: string;
  positions: Array<{
    title: string;
    company: {
      name: string;
    };
    startDate: {
      year: number;
      month?: number;
    };
    endDate?: {
      year: number;
      month?: number;
    };
    description?: string;
  }>;
  skills: Array<{
    name: string;
    endorsementCount?: number;
  }>;
  educations: Array<{
    schoolName: string;
    degree?: string;
    fieldOfStudy?: string;
    startDate?: {
      year: number;
    };
    endDate?: {
      year: number;
    };
  }>;
  contactInfo?: {
    emailAddress?: string;
    phoneNumbers?: Array<{
      type: string;
      number: string;
    }>;
  };
}

interface LinkedInSearchParams {
  keywords: string;
  location?: string;
  industry?: string;
  currentCompany?: string;
  pastCompany?: string;
  title?: string;
  school?: string;
  start?: number;
  count?: number;
}

class LinkedInService {
  private baseURL = 'https://api.linkedin.com/v2';
  private accessToken: string;
  private clientId: string;
  private clientSecret: string;

  constructor() {
    this.accessToken = process.env.LINKEDIN_ACCESS_TOKEN || '';
    this.clientId = process.env.LINKEDIN_CLIENT_ID || '';
    this.clientSecret = process.env.LINKEDIN_CLIENT_SECRET || '';
  }

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Content-Type': 'application/json',
      'X-Restli-Protocol-Version': '2.0.0'
    };
  }

  async refreshAccessToken(): Promise<string> {
    try {
      console.log('Attempting to refresh LinkedIn access token...');
      
      const response = await axios.post('https://www.linkedin.com/oauth/v2/accessToken', 
        new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.clientId,
          client_secret: this.clientSecret,
          scope: 'r_liteprofile r_emailaddress'
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      console.log('LinkedIn token response:', response.data);
      this.accessToken = response.data.access_token;
      return this.accessToken;
    } catch (error: any) {
      console.error('Failed to refresh LinkedIn access token:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      
      if (error.response?.status === 401) {
        throw new Error('LinkedIn credentials invalid. Please check your Client ID and Client Secret.');
      }
      
      throw new Error(`LinkedIn authentication failed: ${error.response?.data?.error_description || error.message}`);
    }
  }

  async searchPeople(params: LinkedInSearchParams): Promise<LinkedInProfile[]> {
    try {
      // For development, we need to implement OAuth flow first
      // LinkedIn requires proper OAuth authentication for People Search API
      console.log('LinkedIn search attempt with params:', params);
      
      if (!this.accessToken) {
        // Generate access token using client credentials flow for testing
        console.log('No access token, attempting to generate one...');
        try {
          await this.refreshAccessToken();
        } catch (tokenError) {
          console.error('Failed to get access token:', tokenError);
          throw new Error('LinkedIn authentication required. Please complete OAuth flow first.');
        }
      }

      // Build search query parameters
      const searchParams = new URLSearchParams();
      
      if (params.keywords) searchParams.append('keywords', params.keywords);
      if (params.location) searchParams.append('location', params.location);
      if (params.industry) searchParams.append('industry', params.industry);
      if (params.currentCompany) searchParams.append('currentCompany', params.currentCompany);
      if (params.pastCompany) searchParams.append('pastCompany', params.pastCompany);
      if (params.title) searchParams.append('title', params.title);
      if (params.school) searchParams.append('school', params.school);
      
      searchParams.append('start', (params.start || 0).toString());
      searchParams.append('count', (params.count || 25).toString());

      // Try different LinkedIn API endpoints based on available scopes
      // First try basic people search (may not be available with current scopes)
      let response;
      
      try {
        // Try LinkedIn Talent Solutions API first
        response = await axios.get(
          `${this.baseURL}/talentSolutions/peopleSearch?${searchParams.toString()}`,
          { headers: this.getHeaders() }
        );
      } catch (talentError: any) {
        console.log('Talent Solutions API not available, trying basic search...', talentError.response?.status);
        
        // Fallback to basic profile search with current scopes
        try {
          // Get user's own profile first to test token validity
          const profileResponse = await axios.get(
            `${this.baseURL}/me?projection=(id,firstName,lastName,headline,positions,skills)`,
            { headers: this.getHeaders() }
          );
          
          console.log('LinkedIn profile response:', profileResponse.data);
          
          // For demo purposes, return user's own profile as a search result
          // In real implementation, this would require different API endpoints
          const userProfile = this.transformBasicProfile(profileResponse.data);
          return [userProfile];
          
        } catch (profileError: any) {
          console.error('Profile API also failed:', profileError.response?.status, profileError.response?.data);
          throw new Error(`LinkedIn API access failed: ${profileError.response?.data?.message || profileError.message}`);
        }
      }

      const profiles = response.data.elements || [];
      
      // Enrich profiles with additional details
      const enrichedProfiles = await Promise.all(
        profiles.map(async (profile: any) => {
          try {
            return await this.getProfileDetails(profile.id);
          } catch (error) {
            console.warn(`Failed to enrich profile ${profile.id}:`, error);
            return this.transformBasicProfile(profile);
          }
        })
      );

      return enrichedProfiles.filter(Boolean);
    } catch (error: any) {
      console.error('LinkedIn people search failed:', error);
      
      if (error.response?.status === 401) {
        // Try to refresh token and retry
        try {
          await this.refreshAccessToken();
          return this.searchPeople(params);
        } catch (refreshError) {
          throw new Error('LinkedIn authentication failed. Please check your API credentials.');
        }
      }
      
      throw new Error(`LinkedIn search failed: ${error.message}`);
    }
  }

  async getProfileDetails(profileId: string): Promise<LinkedInProfile> {
    try {
      const [basicProfile, positions, skills, educations] = await Promise.all([
        this.getBasicProfile(profileId),
        this.getProfilePositions(profileId),
        this.getProfileSkills(profileId),
        this.getProfileEducations(profileId)
      ]);

      return {
        ...basicProfile,
        positions: positions || [],
        skills: skills || [],
        educations: educations || []
      };
    } catch (error) {
      console.error(`Failed to get profile details for ${profileId}:`, error);
      throw error;
    }
  }

  private async getBasicProfile(profileId: string): Promise<LinkedInProfile> {
    try {
      const response = await axios.get(
        `${this.baseURL}/people/${profileId}?projection=(id,firstName,lastName,headline,location,industry,summary)`,
        { headers: this.getHeaders() }
      );

      return this.transformBasicProfile(response.data);
    } catch (error: any) {
      console.warn(`Failed to get basic profile for ${profileId}:`, error.response?.status);
      
      // If specific profile access fails, try getting current user's profile
      if (profileId === 'me' || error.response?.status === 403) {
        const response = await axios.get(
          `${this.baseURL}/me?projection=(id,firstName,lastName,headline)`,
          { headers: this.getHeaders() }
        );
        
        return this.transformBasicProfile(response.data);
      }
      
      throw error;
    }
  }

  private async getProfilePositions(profileId: string): Promise<any[]> {
    try {
      const response = await axios.get(
        `${this.baseURL}/people/${profileId}/positions?projection=(elements*(title,company,startDate,endDate,description))`,
        { headers: this.getHeaders() }
      );

      return response.data.elements || [];
    } catch (error) {
      console.warn(`Failed to get positions for ${profileId}:`, error);
      return [];
    }
  }

  private async getProfileSkills(profileId: string): Promise<any[]> {
    try {
      const response = await axios.get(
        `${this.baseURL}/people/${profileId}/skills?projection=(elements*(name,endorsementCount))`,
        { headers: this.getHeaders() }
      );

      return response.data.elements || [];
    } catch (error) {
      console.warn(`Failed to get skills for ${profileId}:`, error);
      return [];
    }
  }

  private async getProfileEducations(profileId: string): Promise<any[]> {
    try {
      const response = await axios.get(
        `${this.baseURL}/people/${profileId}/educations?projection=(elements*(schoolName,degree,fieldOfStudy,startDate,endDate))`,
        { headers: this.getHeaders() }
      );

      return response.data.elements || [];
    } catch (error) {
      console.warn(`Failed to get educations for ${profileId}:`, error);
      return [];
    }
  }

  private transformBasicProfile(data: any): LinkedInProfile {
    return {
      id: data.id,
      firstName: data.firstName?.localized?.en_US || data.firstName || '',
      lastName: data.lastName?.localized?.en_US || data.lastName || '',
      headline: data.headline?.localized?.en_US || data.headline || '',
      location: {
        country: data.location?.country || '',
        region: data.location?.region || ''
      },
      industry: data.industry || '',
      summary: data.summary?.localized?.en_US || data.summary || '',
      positions: [],
      skills: [],
      educations: []
    };
  }

  // Convert LinkedIn profile to our internal candidate format
  transformToCandidate(profile: LinkedInProfile, matchScore: number = 0): any {
    const currentPosition = profile.positions?.[0];
    const experienceYears = this.calculateExperienceYears(profile.positions);
    
    return {
      id: `linkedin_${profile.id}`,
      name: `${profile.firstName} ${profile.lastName}`.trim(),
      title: profile.headline || currentPosition?.title || 'Professional',
      company: currentPosition?.company?.name || 'Not specified',
      location: `${profile.location.region}, ${profile.location.country}`.replace(/^, |, $/, ''),
      experience: experienceYears.toString(),
      skills: profile.skills.map(skill => skill.name),
      education: this.formatEducation(profile.educations),
      profileUrl: `https://linkedin.com/in/${profile.id}`,
      summary: profile.summary || this.generateSummaryFromProfile(profile),
      matchScore,
      availability: 'unknown' as const,
      contactInfo: {
        email: profile.contactInfo?.emailAddress,
        linkedin: `https://linkedin.com/in/${profile.id}`
      },
      source: 'linkedin' as const
    };
  }

  private calculateExperienceYears(positions: any[]): number {
    if (!positions || positions.length === 0) return 0;

    const currentYear = new Date().getFullYear();
    let totalMonths = 0;

    positions.forEach(position => {
      const startYear = position.startDate?.year || currentYear;
      const startMonth = position.startDate?.month || 1;
      const endYear = position.endDate?.year || currentYear;
      const endMonth = position.endDate?.month || 12;

      const monthsInPosition = (endYear - startYear) * 12 + (endMonth - startMonth);
      totalMonths += Math.max(monthsInPosition, 0);
    });

    return Math.floor(totalMonths / 12);
  }

  private formatEducation(educations: any[]): string {
    if (!educations || educations.length === 0) return 'Not specified';

    const primary = educations[0];
    let education = primary.schoolName || '';
    
    if (primary.degree) {
      education += `, ${primary.degree}`;
    }
    
    if (primary.fieldOfStudy) {
      education += ` in ${primary.fieldOfStudy}`;
    }

    return education || 'Not specified';
  }

  private generateSummaryFromProfile(profile: LinkedInProfile): string {
    if (profile.summary) return profile.summary;

    const parts = [];
    if (profile.headline) parts.push(profile.headline);
    if (profile.industry) parts.push(`Experience in ${profile.industry}`);
    
    const currentPosition = profile.positions?.[0];
    if (currentPosition) {
      parts.push(`Currently ${currentPosition.title} at ${currentPosition.company.name}`);
    }

    if (profile.skills.length > 0) {
      const topSkills = profile.skills.slice(0, 3).map(s => s.name).join(', ');
      parts.push(`Skills include ${topSkills}`);
    }

    return parts.join('. ') || 'Professional with LinkedIn profile';
  }

  isConfigured(): boolean {
    const hasCredentials = !!(this.clientId && this.clientSecret);
    console.log('LinkedIn configuration check:', {
      hasClientId: !!this.clientId,
      hasClientSecret: !!this.clientSecret,
      hasAccessToken: !!this.accessToken,
      isConfigured: hasCredentials
    });
    return hasCredentials;
  }
}

export const linkedinService = new LinkedInService();
export { LinkedInProfile, LinkedInSearchParams };