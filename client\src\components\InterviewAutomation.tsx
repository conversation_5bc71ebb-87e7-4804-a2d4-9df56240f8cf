import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users, Bot, Settings, Play, Square, MoreHorizontal } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import InterviewScheduler from './InterviewScheduler';
import AgentProfileManager from './AgentProfileManager';
import InterviewList from './InterviewList';
import BotSessionMonitor from './BotSessionMonitor';

interface Interview {
  id: string;
  candidateId: string;
  role: string;
  scheduledAt: string;
  durationMin: number;
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  sdkType: 'video' | 'meeting';
  roomOrMeetingId?: string;
  joinUrls?: {
    candidate: string;
    host: string;
  };
  agentProfileId?: string;
  candidate?: {
    id: string;
    fullName: string;
    email: string;
    phone?: string;
  };
  agentProfile?: {
    id: string;
    name: string;
    scriptVersion: string;
  };
}

interface AgentProfile {
  id: string;
  name: string;
  scriptVersion: string;
  rubricJson?: any;
  safetyJson?: any;
  promptTemplate?: string;
  voiceSettings?: any;
  isActive: boolean;
}

interface BotSession {
  sessionId: string;
  interviewId: string;
  status: 'starting' | 'connected' | 'in_progress' | 'ending' | 'ended' | 'failed';
  startedAt: string;
  zoomSessionName?: string;
}

const InterviewAutomation: React.FC = () => {
  const { user } = useAuth();
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [agentProfiles, setAgentProfiles] = useState<AgentProfile[]>([]);
  const [botSessions, setBotSessions] = useState<BotSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('interviews');

  useEffect(() => {
    if (user) {
      loadData();
      // Refresh data every 30 seconds
      const interval = setInterval(loadData, 30000);
      return () => clearInterval(interval);
    }
  }, [user?.id]); // Only re-run when user ID changes

  const loadData = async () => {
    try {
      await Promise.all([
        loadInterviews(),
        loadAgentProfiles(),
        loadBotSessions()
      ]);
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load interview data');
    } finally {
      setLoading(false);
    }
  };

  const loadInterviews = async () => {
    try {
      const response = await fetch('/api/interviews-v2', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setInterviews(data.interviews || []);
      } else {
        const errorData = await response.json();
        console.error('Error loading interviews:', errorData.error || 'Unknown error');
        setInterviews([]);
      }
    } catch (error) {
      console.error('Error loading interviews:', error);
      setInterviews([]);
    }
  };

  const loadAgentProfiles = async () => {
    try {
      const response = await fetch('/api/agent-profiles', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAgentProfiles(data.profiles || []);
      } else {
        const errorData = await response.json();
        console.error('Error loading agent profiles:', errorData.error || 'Unknown error');
        setAgentProfiles([]);
      }
    } catch (error) {
      console.error('Error loading agent profiles:', error);
      setAgentProfiles([]);
    }
  };

  const loadBotSessions = async () => {
    try {
      const response = await fetch('/api/bot-runner/sessions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setBotSessions(data.sessions || []);
      } else {
        const errorData = await response.json();
        console.error('Error loading bot sessions:', errorData.error || 'Unknown error');
        setBotSessions([]);
      }
    } catch (error) {
      console.error('Error loading bot sessions:', error);
      setBotSessions([]);
    }
  };

  const handleInterviewCreated = (newInterview: Interview) => {
    setInterviews(prev => [newInterview, ...prev]);
    toast.success('Interview scheduled successfully');
  };

  const handleInterviewUpdated = (updatedInterview: Interview) => {
    setInterviews(prev => prev.map(interview => 
      interview.id === updatedInterview.id ? updatedInterview : interview
    ));
    toast.success('Interview updated successfully');
  };

  const handleAgentProfileCreated = (newProfile: AgentProfile) => {
    setAgentProfiles(prev => [newProfile, ...prev]);
    toast.success('Agent profile created successfully');
  };

  const handleAgentProfileUpdated = (updatedProfile: AgentProfile) => {
    setAgentProfiles(prev => prev.map(profile => 
      profile.id === updatedProfile.id ? updatedProfile : profile
    ));
    toast.success('Agent profile updated successfully');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'rescheduled': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getBotStatusColor = (status: string) => {
    switch (status) {
      case 'starting': return 'bg-yellow-100 text-yellow-800';
      case 'connected': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-green-100 text-green-800';
      case 'ending': return 'bg-orange-100 text-orange-800';
      case 'ended': return 'bg-gray-100 text-gray-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div
      className="space-y-6 p-6 min-h-screen"
      style={{ backgroundColor: '#FBFAFF' }}
    >
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold" style={{ color: '#0152FF' }}>Interview Automation</h1>
          <p className="text-gray-600">Manage AI-powered interviews with Zoom Video SDK</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Bot className="w-4 h-4" />
            <span>{botSessions.filter(s => s.status === 'in_progress').length} Active Sessions</span>
          </div>
          <Button
            onClick={loadData}
            variant="outline"
            size="sm"
            style={{ borderColor: '#0152FF', color: '#0152FF' }}
            className="hover:bg-blue-50"
          >
            Refresh
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Scheduled</p>
                <p className="text-2xl font-bold">{interviews.filter(i => i.status === 'scheduled').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Completed</p>
                <p className="text-2xl font-bold">{interviews.filter(i => i.status === 'completed').length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Bot className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Agent Profiles</p>
                <p className="text-2xl font-bold">{agentProfiles.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Active Bots</p>
                <p className="text-2xl font-bold">{botSessions.filter(s => ['starting', 'connected', 'in_progress'].includes(s.status)).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="interviews">Interviews</TabsTrigger>
          <TabsTrigger value="schedule">Schedule New</TabsTrigger>
          <TabsTrigger value="agents">Agent Profiles</TabsTrigger>
          <TabsTrigger value="monitor">Bot Monitor</TabsTrigger>
        </TabsList>

        <TabsContent value="interviews" className="space-y-4">
          <InterviewList 
            interviews={interviews}
            onInterviewUpdated={handleInterviewUpdated}
            onRefresh={loadInterviews}
          />
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <InterviewScheduler 
            agentProfiles={agentProfiles}
            onInterviewCreated={handleInterviewCreated}
          />
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          <AgentProfileManager 
            profiles={agentProfiles}
            onProfileCreated={handleAgentProfileCreated}
            onProfileUpdated={handleAgentProfileUpdated}
          />
        </TabsContent>

        <TabsContent value="monitor" className="space-y-4">
          <BotSessionMonitor 
            sessions={botSessions}
            interviews={interviews}
            onRefresh={loadBotSessions}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default InterviewAutomation;
