<html>
 <head>
  <title><PERSON><PERSON>ra Voice AI Agent for Talent Acquisition</title>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&amp;display=swap" rel="stylesheet"/>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <style>
   body {
        font-family: 'Montserrat', Arial, sans-serif;
        background-color: #f8fafc;
      }
      @keyframes float {
        0% { transform: translateY(0px);}
        50% { transform: translateY(-18px);}
        100% { transform: translateY(0px);}
      }
      .float-animate {
        animation: float 2.5s ease-in-out infinite;
      }
      @keyframes fadeInUp {
        0% { opacity: 0; transform: translateY(40px);}
        100% { opacity: 1; transform: translateY(0);}
      }
      .fade-in-up {
        animation: fadeInUp 1.2s cubic-bezier(.39,.575,.565,1) both;
      }
      .fade-in-up-delayed {
        animation: fadeInUp 1.2s cubic-bezier(.39,.575,.565,1) both;
        animation-delay: 0.5s;
      }
      .fade-in-up-delayed2 {
        animation: fadeInUp 1.2s cubic-bezier(.39,.575,.565,1) both;
        animation-delay: 1s;
      }
      @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
      }
      .pulse-animate {
        animation: pulse 2s infinite;
      }
      @keyframes typewriter {
        from { width: 0; }
        to { width: 100%; }
      }
      .typewriter {
        overflow: hidden;
        border-right: .15em solid black;
        white-space: nowrap;
        margin: 0 auto;
        letter-spacing: .15em;
        animation: 
          typewriter 4s steps(40, end) infinite,
          blink-caret .75s step-end infinite;
      }
      @keyframes blink-caret {
        from, to { border-color: transparent }
        50% { border-color: black; }
      }
      @keyframes wave {
        0% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
        100% { transform: translateY(0); }
      }
      .wave {
        animation: wave 1.5s ease-in-out infinite;
      }
  </style>
 </head>
 <body class="min-h-screen flex flex-col items-center">
  <!-- Navbar -->
  <div class="w-full bg-white/80 backdrop-blur-md rounded-b-3xl shadow-lg border border-[#e5e7eb] px-8 py-4 flex items-center justify-between mt-0 z-10">
   <div class="flex items-center space-x-2">
    <span class="text-3xl font-extrabold text-black tracking-tight select-none">steorra</span>
   </div>
   <div class="flex items-center space-x-8 ml-8">
    <a class="text-black text-base font-semibold hover:text-gray-600 transition" href="#">Product</a>
    <a class="text-black text-base font-semibold hover:text-gray-600 transition" href="#">Pricing</a>
    <a class="text-black text-base font-semibold hover:text-gray-600 transition" href="#">Resources</a>
   </div>
   <div class="flex items-center space-x-4">
    <a class="text-black text-base font-semibold hover:text-gray-600 transition" href="#">Login</a>
    <button class="bg-black text-white text-base font-bold px-6 py-2 rounded-full shadow-lg hover:bg-gray-800 transition transform duration-200 pulse-animate">Book a Demo</button>
   </div>
  </div>
  <!-- Banner with Animation -->
  <div class="w-[95vw] max-w-xl rounded-[40px] px-8 py-16 flex flex-col items-center justify-center relative mt-16 bg-black shadow-2xl fade-in-up">
   <!-- Animated Voice AI Agent Model -->
   <div class="float-animate mb-8">
    <img alt="Animated illustration of a friendly Voice AI agent, futuristic, with headset and glowing sound waves" class="rounded-full border-4 border-white bg-white shadow-xl" height="120" src="https://replicate.delivery/xezq/QREdojPSfb2PMq4fY640HreuXJMaVavv49fBfiwnIURw6rfRF/out-0.png" width="120"/>
   </div>
   <h1 class="text-white text-center text-4xl md:text-5xl font-extrabold leading-tight mb-2 drop-shadow-lg fade-in-up-delayed">Voice AI Agent for Talent Acquisition</h1>
   <p class="text-white/90 text-center text-lg font-medium mt-2 fade-in-up-delayed2">Steorra</p>
   <!-- Animated sound waves below the avatar -->
   <div class="flex justify-center mt-8 space-x-2 fade-in-up-delayed2">
    <div class="h-6 w-1.5 rounded-full bg-white/80 animate-pulse" style="animation-delay:0.1s"></div>
    <div class="h-8 w-1.5 rounded-full bg-white/60 animate-pulse" style="animation-delay:0.2s"></div>
    <div class="h-10 w-1.5 rounded-full bg-white/40 animate-pulse" style="animation-delay:0.3s"></div>
    <div class="h-8 w-1.5 rounded-full bg-white/60 animate-pulse" style="animation-delay:0.4s"></div>
    <div class="h-6 w-1.5 rounded-full bg-white/80 animate-pulse" style="animation-delay:0.5s"></div>
   </div>
  </div>
  <!-- Typewriter effect section -->
  <div class="w-full max-w-3xl mt-20 mb-16 px-4 fade-in-up-delayed2">
   <h2 class="text-black text-3xl md:text-4xl font-extrabold text-center mb-6 typewriter">Simplify Your Recruitment Automation</h2>
   <p class="text-black text-xl text-center font-medium">with <span class="font-bold">Steorra Voice AI Agent</span></p>
  </div>
  <!-- Animated Initial Screening Video -->
  <div class="w-full max-w-4xl mt-8 mb-16 px-4 fade-in-up-delayed2">
   <h3 class="text-black text-2xl font-bold text-center mb-6">AI Agent Conducting Initial Screening</h3>
   <div class="relative bg-gray-100 rounded-lg shadow-lg p-8 overflow-hidden">
    <!-- AI Agent -->
    <div class="absolute left-4 top-4 w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center wave">
     <i class="fas fa-robot text-white text-2xl"></i>
    </div>
    <!-- Candidate -->
    <div class="absolute right-4 top-4 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center wave" style="animation-delay: 0.5s;">
     <i class="fas fa-user text-white text-2xl"></i>
    </div>
    <!-- Conversation Bubbles -->
    <div class="mt-24 space-y-4">
     <div class="bg-blue-100 p-3 rounded-lg max-w-xs ml-8 fade-in-up">
      Hello! I'm the Steorra AI assistant. Is this John Doe?
     </div>
     <div class="bg-green-100 p-3 rounded-lg max-w-xs ml-auto mr-8 fade-in-up" style="animation-delay: 1s;">
      Yes, this is John. How can I help you?
     </div>
     <div class="bg-blue-100 p-3 rounded-lg max-w-xs ml-8 fade-in-up" style="animation-delay: 2s;">
      Great! I'm calling about your application for the Software Engineer position. Do you have a few minutes to answer some questions?
     </div>
     <div class="bg-green-100 p-3 rounded-lg max-w-xs ml-auto mr-8 fade-in-up" style="animation-delay: 3s;">
      Sure, I'd be happy to answer some questions.
     </div>
     <div class="bg-blue-100 p-3 rounded-lg max-w-xs ml-8 fade-in-up" style="animation-delay: 4s;">
      Excellent! Let's begin with your experience in Python programming...
     </div>
    </div>
   </div>
  </div>
  <!-- Features section with animations -->
  <div class="w-full max-w-5xl mt-16 mb-16 px-4">
   <h3 class="text-black text-3xl font-bold text-center mb-12">Key Features</h3>
   <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
    <div class="bg-white p-6 rounded-lg shadow-lg transform hover:scale-105 transition duration-300">
     <i class="fas fa-robot text-4xl text-black mb-4"></i>
     <h4 class="text-xl font-semibold mb-2">AI-Powered Conversations</h4>
     <p class="text-gray-600">Engage candidates with natural, intelligent dialogues.</p>
    </div>
    <div class="bg-white p-6 rounded-lg shadow-lg transform hover:scale-105 transition duration-300">
     <i class="fas fa-chart-line text-4xl text-black mb-4"></i>
     <h4 class="text-xl font-semibold mb-2">Advanced Analytics</h4>
     <p class="text-gray-600">Gain insights from detailed recruitment metrics and reports.</p>
    </div>
    <div class="bg-white p-6 rounded-lg shadow-lg transform hover:scale-105 transition duration-300">
     <i class="fas fa-clock text-4xl text-black mb-4"></i>
     <h4 class="text-xl font-semibold mb-2">24/7 Availability</h4>
     <p class="text-gray-600">Screen and engage candidates around the clock.</p>
    </div>
   </div>
  </div>
 </body>
</html>