 // Build comprehensive full context
            fullContext = `COMPANY CONTEXT:
- Company: ${organization?.name || 'Our company'}
- Industry: ${organization?.domain || 'Not specified'}
- About: ${organization?.name ? `${organization.name} - Leading organization in ${organization.domain || 'our field'}` : 'Leading organization in our field'}

JOB CONTEXT:
- Position: ${jobPosting?.title || 'Open position'}
- Experience Level: ${jobPosting?.experienceLevel || 'Not specified'}
- Salary Range: ${jobPosting?.salaryRange || 'Competitive compensation'}
- Work Environment: ${jobPosting?.workEnvironment || 'Collaborative team environment'}

KEY RESPONSIBILITIES:
${jobPosting?.responsibilities || 'Will be discussed during interview'}

REQUIREMENTS:
${jobPosting?.requirements || 'Will be discussed during interview'}

BENEFITS HIGHLIGHTS:
${jobPosting?.benefits || 'Comprehensive benefits package'}

CANDIDATE CONTEXT:
- Current Company: ${candidate?.currentCompany || 'Not specified'}
- Education: ${candidate?.education || 'Not specified'}  
- Match Score: ${candidate?.matchScore || 0}/100
- Key Skills: ${candidate?.skills ? (Array.isArray(candidate.skills) ? candidate.skills.join(', ') : candidate.skills) : 'Not specified'}`;

            console.log('✅ FRESH DATA FETCHED:', {
              candidate_name: candidateName,
              job_title: jobTitle,
              company_name: companyName,
              full_context_length: fullContext.length
            });
          }
        } catch (error) {
          console.error('❌ Error fetching fresh data:', error);
          candidateName = 'Candidate';
          jobTitle = 'Position';  
          companyName = 'Company';
          callPurpose = 'screening';
          fullContext = 'Error fetching context';
        }
        
        // 📤 INPUT CONTEXT TO ELEVENLABS:
        console.log('📤 ElevenLabs Enhanced Context:', {
          candidate_name: candidateName,
          job_title: jobTitle,
          company_name: companyName,
          call_purpose: callPurpose,
          full_context: fullContext.substring(0, 200) + '...'
        });