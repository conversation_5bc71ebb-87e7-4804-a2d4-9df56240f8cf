import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { db } from '../db';
import { interviewsV2, interviewRuns, agentProfiles } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { zoomVideoSDKService } from './zoomVideoSDKService';
import { elevenLabsService } from './elevenlabsService';

export interface BotSession {
  interviewId: string;
  sessionId: string;
  browser: Browser;
  page: Page;
  status: 'starting' | 'connected' | 'in_progress' | 'ending' | 'ended' | 'failed';
  startedAt: Date;
  zoomSessionName?: string;
  elevenlabsConversationId?: string;
}

export class BotRunnerService {
  private activeSessions: Map<string, BotSession> = new Map();
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = process.env.BASE_URL || 'http://localhost:5000';
  }

  /**
   * Start bot auto-join process for an interview
   */
  async startBotSession(interviewId: string): Promise<string> {
    try {
      console.log(`🤖 Starting bot session for interview: ${interviewId}`);

      // Get interview details
      const [interview] = await db
        .select()
        .from(interviewsV2)
        .where(eq(interviewsV2.id, interviewId))
        .limit(1);

      if (!interview) {
        throw new Error(`Interview ${interviewId} not found`);
      }

      // Get agent profile
      let agentProfile = null;
      if (interview.agentProfileId) {
        [agentProfile] = await db
          .select()
          .from(agentProfiles)
          .where(eq(agentProfiles.id, interview.agentProfileId))
          .limit(1);
      }

      // Generate session ID
      const sessionId = `bot-${interviewId}-${Date.now()}`;

      // Launch headless browser
      const browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--single-process',
          '--disable-gpu',
          '--use-fake-ui-for-media-stream',
          '--use-fake-device-for-media-stream',
          '--allow-running-insecure-content',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      });

      const page = await browser.newPage();

      // Set viewport and user agent
      await page.setViewport({ width: 1280, height: 720 });
      await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

      // Create bot session
      const botSession: BotSession = {
        interviewId,
        sessionId,
        browser,
        page,
        status: 'starting',
        startedAt: new Date(),
        zoomSessionName: interview.roomOrMeetingId || undefined
      };

      this.activeSessions.set(sessionId, botSession);

      // Update interview run
      await db
        .insert(interviewRuns)
        .values({
          interviewId,
          organizationId: interview.organizationId,
          status: 'starting',
          botSessionId: sessionId,
          zoomMeetingId: interview.roomOrMeetingId,
          metricsJson: {
            botStarted: new Date().toISOString(),
            agentProfile: agentProfile?.name || 'default'
          }
        })
        .onConflictDoUpdate({
          target: [interviewRuns.interviewId],
          set: {
            status: 'starting',
            botSessionId: sessionId,
            metricsJson: {
              botStarted: new Date().toISOString(),
              agentProfile: agentProfile?.name || 'default'
            }
          }
        });

      // Start the bot join process
      this.joinZoomSession(sessionId, interview, agentProfile);

      return sessionId;

    } catch (error) {
      console.error(`Error starting bot session for interview ${interviewId}:`, error);
      throw error;
    }
  }

  /**
   * Join Zoom session as host
   */
  private async joinZoomSession(sessionId: string, interview: any, agentProfile: any): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      throw new Error(`Bot session ${sessionId} not found`);
    }

    try {
      console.log(`🎥 Bot joining Zoom session: ${interview.roomOrMeetingId}`);

      // Generate host token
      const tokens = zoomVideoSDKService.generateSessionTokens(
        interview.roomOrMeetingId,
        interview.candidateId,
        'interviewer-bot'
      );

      // Navigate to bot client page
      const authToken = this.generateBotAuthToken();
      const botClientUrl = `${this.baseUrl}/bot-client?token=${tokens.hostToken}&sessionName=${tokens.sessionName}&authToken=${encodeURIComponent(authToken)}`;
      await session.page.goto(botClientUrl, { waitUntil: 'networkidle0' });

      // Wait for Zoom SDK to initialize
      await session.page.waitForSelector('#zoom-container', { timeout: 30000 });

      // Initialize audio bridge
      await this.initializeAudioBridge(sessionId, agentProfile);

      // Update session status
      session.status = 'connected';
      
      // Update interview run
      await db
        .update(interviewRuns)
        .set({
          status: 'in_progress',
          startedAt: new Date(),
          metricsJson: {
            botConnected: new Date().toISOString(),
            zoomSessionJoined: true
          }
        })
        .where(eq(interviewRuns.interviewId, interview.id));

      console.log(`✅ Bot successfully joined Zoom session: ${interview.roomOrMeetingId}`);

    } catch (error) {
      console.error(`Error joining Zoom session for bot ${sessionId}:`, error);
      session.status = 'failed';
      
      await db
        .update(interviewRuns)
        .set({
          status: 'failed',
          metricsJson: {
            error: error.message,
            failedAt: new Date().toISOString()
          }
        })
        .where(eq(interviewRuns.interviewId, interview.id));

      // Clean up failed session
      await this.endBotSession(sessionId);
    }
  }

  /**
   * Initialize audio bridge between Zoom and ElevenLabs
   */
  private async initializeAudioBridge(sessionId: string, agentProfile: any): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) return;

    try {
      console.log(`🔊 Initializing audio bridge for bot session: ${sessionId}`);

      // Prepare ElevenLabs configuration
      const elevenlabsConfig = {
        agentId: process.env.ELEVENLABS_AGENT_ID || 'default-agent-id',
        promptTemplate: agentProfile?.promptTemplate || 'You are conducting a professional interview. Be friendly, professional, and ask relevant questions.',
        voiceSettings: agentProfile?.voiceSettings || {
          voiceId: 'sarah',
          stability: 0.6,
          similarityBoost: 0.8,
          style: 0.2,
          useSpeakerBoost: true
        }
      };

      // Inject audio bridge script
      await session.page.evaluate((config) => {
        console.log('Setting up audio bridge with ElevenLabs config:', config);

        // Initialize ElevenLabs connection
        if (window.initializeElevenLabsBridge) {
          window.initializeElevenLabsBridge(config);
        }
      }, elevenlabsConfig);

      console.log(`✅ Audio bridge initialized for bot session: ${sessionId}`);

    } catch (error) {
      console.error(`Error initializing audio bridge for bot ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * End bot session
   */
  async endBotSession(sessionId: string): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      console.warn(`Bot session ${sessionId} not found`);
      return;
    }

    try {
      console.log(`🤖 Ending bot session: ${sessionId}`);

      session.status = 'ending';

      // Gracefully leave Zoom session
      try {
        await session.page.evaluate(() => {
          if (window.leaveZoomSession) {
            window.leaveZoomSession();
          }
        });
      } catch (error) {
        console.warn('Error leaving Zoom session gracefully:', error);
      }

      // Close browser
      await session.browser.close();

      // Update session status
      session.status = 'ended';

      // Update interview run
      await db
        .update(interviewRuns)
        .set({
          status: 'completed',
          endedAt: new Date(),
          metricsJson: {
            botEnded: new Date().toISOString(),
            duration: Date.now() - session.startedAt.getTime()
          }
        })
        .where(eq(interviewRuns.interviewId, session.interviewId));

      // Remove from active sessions
      this.activeSessions.delete(sessionId);

      console.log(`✅ Bot session ended: ${sessionId}`);

    } catch (error) {
      console.error(`Error ending bot session ${sessionId}:`, error);
      // Force cleanup
      try {
        await session.browser.close();
      } catch (closeError) {
        console.error('Error force closing browser:', closeError);
      }
      this.activeSessions.delete(sessionId);
    }
  }

  /**
   * Get active bot sessions
   */
  getActiveSessions(): BotSession[] {
    return Array.from(this.activeSessions.values());
  }

  /**
   * Generate auth token for bot API calls
   */
  private generateBotAuthToken(): string {
    // For now, use a simple bot token. In production, this should be a proper JWT
    // that identifies the bot and has appropriate permissions
    return process.env.BOT_AUTH_TOKEN || 'bot-token-' + Date.now();
  }

  /**
   * Get bot session by ID
   */
  getBotSession(sessionId: string): BotSession | null {
    return this.activeSessions.get(sessionId) || null;
  }

  /**
   * Schedule bot auto-join (T-2 minutes before interview)
   */
  async scheduleAutoJoin(interviewId: string, scheduledAt: Date): Promise<void> {
    const joinTime = new Date(scheduledAt.getTime() - 2 * 60 * 1000); // 2 minutes before
    const delay = joinTime.getTime() - Date.now();

    if (delay > 0) {
      console.log(`⏰ Scheduling bot auto-join for interview ${interviewId} in ${Math.round(delay / 1000)} seconds`);
      
      setTimeout(async () => {
        try {
          await this.startBotSession(interviewId);
        } catch (error) {
          console.error(`Error in scheduled bot auto-join for interview ${interviewId}:`, error);
        }
      }, delay);
    } else {
      console.log(`⚡ Starting bot immediately for interview ${interviewId} (scheduled time has passed)`);
      await this.startBotSession(interviewId);
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<void> {
    const now = Date.now();
    const maxSessionDuration = 4 * 60 * 60 * 1000; // 4 hours

    for (const [sessionId, session] of this.activeSessions.entries()) {
      const sessionAge = now - session.startedAt.getTime();
      
      if (sessionAge > maxSessionDuration) {
        console.log(`🧹 Cleaning up expired bot session: ${sessionId}`);
        await this.endBotSession(sessionId);
      }
    }
  }
}

export const botRunnerService = new BotRunnerService();

// Clean up expired sessions every hour
setInterval(() => {
  botRunnerService.cleanupExpiredSessions();
}, 60 * 60 * 1000);
