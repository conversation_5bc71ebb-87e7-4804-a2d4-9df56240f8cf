import { api } from '@/lib/api';

export interface ResumeAnalysisResult {
  name: string;
  email: string;
  phone: string;
  location: string;
  linkedin: string;
  experience_years: number;
  key_skills: string[];
  recent_roles: string[];
  education: string;
  overall_score: number;
  match_score: number;
  strengths: string[];
  concerns: string[];
  skill_analysis: {
    matched_skills: string[];
    missing_skills: string[];
    transferable_skills: string[];
  };
  experience_analysis: {
    relevant_experience: string;
    experience_gap: string;
    career_progression: string;
  };
  recommendation: 'HIRE' | 'INTERVIEW' | 'REJECT';
  detailed_feedback: string;
  interview_questions: string[];
}

export const analyzeResumeWithJob = async (
  resumeText: string,
  jobDescription: string,
  contactInfo?: {
    name?: string;
    email?: string;
    phone?: string;
    location?: string;
    linkedin?: string;
  }
): Promise<ResumeAnalysisResult> => {
  try {
    const result = await api.analyzeResume({
      resumeText,
      jobDescription,
      extractedName: contactInfo?.name,
      extractedEmail: contactInfo?.email,
      extractedPhone: contactInfo?.phone,
      extractedLocation: contactInfo?.location,
      extractedLinkedin: contactInfo?.linkedin,
    });

    if (result.error) {
      throw new Error(result.error);
    }

    return result;
  } catch (error) {
    console.error('Error analyzing resume:', error);
    throw error;
  }
};