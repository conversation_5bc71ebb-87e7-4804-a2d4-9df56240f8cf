import express from 'express';
import { z } from 'zod';
import { db } from '../db';
import { callSummaries, voiceCalls } from '../../shared/schema';
import { eq, and, desc } from 'drizzle-orm';
import { authenticateToken } from '../auth';
import { requireOrganizationAccess, OrganizationScopedRequest } from '../middleware/organizationAccess';

/**
 * @swagger
 * components:
 *   schemas:
 *     CallSummary:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         callId:
 *           type: string
 *           format: uuid
 *         summary:
 *           type: string
 *         nextActions:
 *           type: object
 *         scheduled:
 *           type: boolean
 *         scheduledTime:
 *           type: string
 *           format: date-time
 *         scheduledEvidence:
 *           type: string
 *         openQuestions:
 *           type: array
 *           items:
 *             type: string
 *         riskFlags:
 *           type: array
 *           items:
 *             type: string
 *         generatedAt:
 *           type: string
 *           format: date-time
 *         modelUsed:
 *           type: string
 *         processingTimeMs:
 *           type: number
 *         candidateId:
 *           type: string
 *           format: uuid
 *         phoneNumber:
 *           type: string
 *         callPurpose:
 *           type: string
 *         startedAt:
 *           type: string
 *           format: date-time
 *         endedAt:
 *           type: string
 *           format: date-time
 *         durationSeconds:
 *           type: number
 */

const router = express.Router();

/**
 * @swagger
 * /api/call-summaries/organization/{organizationId}:
 *   get:
 *     summary: Get all call summaries for an organization
 *     tags: [Call Summaries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: Call summaries retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 summaries:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/CallSummary'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       500:
 *         description: Server error
 */
// Get all call summaries for an organization
router.get('/organization/:organizationId', authenticateToken, requireOrganizationAccess, async (req: OrganizationScopedRequest, res) => {
  try {
    const organizationId = req.params.organizationId;
    
    const summaries = await db
      .select({
        id: callSummaries.id,
        callId: callSummaries.callId,
        summary: callSummaries.summary,
        nextActions: callSummaries.nextActions,
        scheduled: callSummaries.scheduled,
        scheduledTime: callSummaries.scheduledTime,
        scheduledEvidence: callSummaries.scheduledEvidence,
        openQuestions: callSummaries.openQuestions,
        riskFlags: callSummaries.riskFlags,
        generatedAt: callSummaries.generatedAt,
        modelUsed: callSummaries.modelUsed,
        processingTimeMs: callSummaries.processingTimeMs,
        // Join with voice call data for context
        candidateId: voiceCalls.candidateId,
        phoneNumber: voiceCalls.phoneNumber,
        callPurpose: voiceCalls.callPurpose,
        startedAt: voiceCalls.startedAt,
        endedAt: voiceCalls.endedAt,
        durationSeconds: voiceCalls.durationSeconds
      })
      .from(callSummaries)
      .innerJoin(voiceCalls, eq(callSummaries.callId, voiceCalls.id))
      .where(eq(callSummaries.organizationId, organizationId))
      .orderBy(desc(callSummaries.generatedAt));

    res.json({
      success: true,
      summaries
    });
    
  } catch (error) {
    console.error('❌ Error fetching call summaries:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch call summaries'
    });
  }
});

/**
 * @swagger
 * /api/call-summaries/call/{callId}:
 *   get:
 *     summary: Get call summary by call ID
 *     tags: [Call Summaries]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: callId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Call ID
 *     responses:
 *       200:
 *         description: Call summary retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 summary:
 *                   $ref: '#/components/schemas/CallSummary'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Call or summary not found
 *       500:
 *         description: Server error
 */
// Get call summary by call ID
router.get('/call/:callId', authenticateToken, async (req, res) => {
  try {
    const callId = req.params.callId;
    
    // First verify the call exists and user has access
    const [call] = await db
      .select({ organizationId: voiceCalls.organizationId })
      .from(voiceCalls)
      .where(eq(voiceCalls.id, callId));

    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    // Check organization access
    const user = (req as any).user;
    if (user.role !== 'super_admin' && user.organizationId !== call.organizationId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Get the summary
    const [summary] = await db
      .select()
      .from(callSummaries)
      .where(eq(callSummaries.callId, callId));

    if (!summary) {
      return res.status(404).json({
        success: false,
        message: 'Call summary not found'
      });
    }

    res.json({
      success: true,
      summary
    });
    
  } catch (error) {
    console.error('❌ Error fetching call summary:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch call summary'
    });
  }
});

// Get summary statistics for organization
router.get('/stats/:organizationId', authenticateToken, requireOrganizationAccess, async (req: OrganizationScopedRequest, res) => {
  try {
    const organizationId = req.params.organizationId;
    
    const summaries = await db
      .select()
      .from(callSummaries)
      .where(eq(callSummaries.organizationId, organizationId));

    const stats = {
      totalSummaries: summaries.length,
      scheduledMeetings: summaries.filter(s => s.scheduled).length,
      avgOpenQuestions: summaries.reduce((acc, s) => acc + (s.openQuestions?.length || 0), 0) / summaries.length || 0,
      avgRiskFlags: summaries.reduce((acc, s) => acc + (s.riskFlags?.length || 0), 0) / summaries.length || 0,
      recentSummaries: summaries.filter(s => {
        const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        return s.generatedAt && s.generatedAt > dayAgo;
      }).length,
      modelDistribution: summaries.reduce((acc, s) => {
        acc[s.modelUsed || 'unknown'] = (acc[s.modelUsed || 'unknown'] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    res.json({
      success: true,
      stats
    });
    
  } catch (error) {
    console.error('❌ Error fetching summary stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch summary statistics'
    });
  }
});

// Manually trigger summarization for a completed call (admin only)
router.post('/generate/:callId', authenticateToken, async (req, res) => {
  try {
    const callId = req.params.callId;
    const user = (req as any).user;

    // Verify admin access
    if (user.role !== 'admin' && user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    // Get the call
    const [call] = await db
      .select()
      .from(voiceCalls)
      .where(eq(voiceCalls.id, callId));

    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    // Check organization access for non-super-admin
    if (user.role !== 'super_admin' && user.organizationId !== call.organizationId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Check if call has transcript
    if (!call.transcription) {
      return res.status(400).json({
        success: false,
        message: 'Call must have transcription to generate summary'
      });
    }

    // Check if summary already exists
    const [existingSummary] = await db
      .select()
      .from(callSummaries)
      .where(eq(callSummaries.callId, callId));

    if (existingSummary) {
      return res.status(400).json({
        success: false,
        message: 'Summary already exists for this call'
      });
    }

    // Trigger summarization
    const { processCallSummaryAsync } = await import('../services/conversationSummarizationService');
    processCallSummaryAsync(callId, call.organizationId, call.transcription);

    res.json({
      success: true,
      message: 'Summary generation triggered. Check back in a moment.'
    });
    
  } catch (error) {
    console.error('❌ Error triggering summary generation:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to trigger summary generation'
    });
  }
});

export default router;