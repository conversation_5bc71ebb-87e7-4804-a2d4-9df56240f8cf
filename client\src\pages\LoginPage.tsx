import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, Link } from 'react-router-dom';
import AuthLayout from '../components/AuthLayout';

export default function LoginPage() {
  const { login, isLoading, error, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);

  const [loginForm, setLoginForm] = useState({
    email: '',
    password: '',
  });

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated && user && !isLoading) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, user, isLoading, navigate]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login(loginForm.email, loginForm.password);
    } catch (error) {
      console.error('Lo<PERSON> failed:', error);
    }
  };

  return (
    <AuthLayout>
      {/* Logo */}
      <div className="mb-6">
        <img src="/Login/Vector.svg" alt="Steorra Logo" className="w-10 h-10 mb-4" />
        <h1 className="text-2xl font-bold text-[#151518] mb-1">
          Welcome back
        </h1>
        <p className="text-[#4B4B52] text-sm">
          Sign in to your account
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-xl text-red-600 text-sm">
          {error}
        </div>
      )}

      {/* Login Form */}
      <form onSubmit={handleLogin} className="space-y-4">
        {/* Email Field */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-[#151518] mb-1.5">
            Email address <span className="text-red-500">*</span>
          </label>
          <input
            id="email"
            type="email"
            required
            className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
            placeholder="Enter the email address"
            value={loginForm.email}
            onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
          />
        </div>

        {/* Password Field */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-[#151518] mb-1.5">
            Password <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              required
              className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all pr-12"
              placeholder="Enter the password"
              value={loginForm.password}
              onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#9CA3AF] hover:text-[#4B4B52] transition-colors"
            >
              <img src="/Login/eye-off.svg" alt="Toggle password" className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Sign In Button */}
        <button
          type="submit"
          disabled={isLoading}
          className="w-full py-2.5 px-6 bg-[#0152FF] text-white font-semibold rounded-full hover:bg-[#0142CC] transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>{isLoading ? 'Signing in...' : 'Sign in'}</span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </form>

      {/* Footer Links */}
      <div className="mt-5 text-center text-sm">
        <p className="text-[#4B4B52]">
          Don't have an account?{' '}
          <Link to="/signup" className="text-[#0152FF] font-semibold hover:underline">
            Sign up
          </Link>
        </p>
        <p className="text-[#4B4B52] mt-2">
          Need to create an organization?{' '}
          <Link to="/create-organization" className="text-[#0152FF] font-semibold hover:underline">
            Create Organization
          </Link>
        </p>
      </div>
    </AuthLayout>
  );
}

