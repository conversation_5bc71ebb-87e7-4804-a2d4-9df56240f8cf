import { Router, Request, Response } from 'express';
import multer from 'multer';
import { validateCandidateApplication, CandidateValidationError } from '../utils/candidateValidation';
import { sendCandidateValidationFailureEmail } from '../services/candidateEmailService';
import { parseDocument } from '../utils/documentParser.js';

const router = Router();
const upload = multer({ storage: multer.memoryStorage() });

interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
}

interface ParsedResumeData {
  contactInfo: ContactInfo;
  workExperience?: any[];
  education?: any[];
  skills?: string[];
  summary?: string;
  extractedText?: string;
  analysis?: any;
  hasJobAnalysis?: boolean;
  method?: string;
}

export async function parseResumeWithPython(fileContent: string, fileName: string, fileType: string, jobDescription?: string): Promise<ParsedResumeData> {
  const openaiApiKey = process.env.OPENAI_API_KEY || 'none';
  const fs = await import('fs');
  const path = await import('path');
  const { spawn } = await import('child_process');
  
  let tempFilePath = '';

  try {
    // Create a temporary file for large content
    // Use OS-specific temp directory
    const os = await import('os');
    const tempDir = os.tmpdir();

    // Ensure temp directory exists
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    tempFilePath = path.join(tempDir, `resume_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.b64`);

    // Write file content to temp file
    fs.writeFileSync(tempFilePath, fileContent);
    
    return new Promise((resolve, reject) => {
      const args = [
        'server/document_parser.py',
        tempFilePath,
        fileName,
        fileType,
        openaiApiKey
      ];
      
      if (jobDescription) {
        args.push(jobDescription);
      }
      
      const pythonProcess = spawn('python3', args);
      
      let stdout = '';
      let stderr = '';
      
      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });
      
      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });
      
      pythonProcess.on('close', (code) => {
        // Clean up temp file
        try {
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
          }
        } catch (cleanupError) {
          console.error('Failed to cleanup temp file:', cleanupError);
        }
        
        if (code === 0) {
          try {
            // Check if output is JSON
            const trimmedOutput = stdout.trim();
            if (trimmedOutput.startsWith('{')) {
              const result = JSON.parse(trimmedOutput);
              console.log(`Python extraction successful: ${result.method}`);
              resolve(result);
            } else {
              // Output is plain text, create structure
              console.log('Python returned plain text:', trimmedOutput.substring(0, 100));
              const extractedText = trimmedOutput.length > 20 ? trimmedOutput : fileContent;
              resolve({
                contactInfo: { name: 'Unknown', email: '<EMAIL>' },
                extractedText: extractedText,
                method: 'python_text_extraction',
                hasJobAnalysis: false
              });
            }
          } catch (parseError) {
            console.log('Python output not JSON, treating as plain text:', stdout.substring(0, 100));
            const extractedText = stdout.trim().length > 20 ? stdout.trim() : fileContent;
            resolve({
              contactInfo: { name: 'Unknown', email: '<EMAIL>' },
              extractedText: extractedText,
              method: 'python_fallback',
              hasJobAnalysis: false
            });
          }
        } else {
          console.error('Python process failed:', stderr);
          reject(new Error(`Python process failed with code ${code}: ${stderr}`));
        }
      });
      
      pythonProcess.on('error', (error) => {
        // Clean up temp file on error
        try {
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
          }
        } catch (cleanupError) {
          console.error('Failed to cleanup temp file:', cleanupError);
        }
        console.error('Python process error:', error);
        reject(error);
      });
    });
    
  } catch (error) {
    // Clean up temp file on error
    try {
      if (tempFilePath && fs.existsSync(tempFilePath)) {
        fs.unlinkSync(tempFilePath);
      }
    } catch (cleanupError) {
      console.error('Failed to cleanup temp file:', cleanupError);
    }
    
    console.error('Error in Python resume parsing:', error);

    // Fallback to basic extraction using regex
    const contactInfo = extractContactInfoFromText(fileContent);
    return {
      contactInfo,
      extractedText: fileContent,
      skills: [],
      workExperience: [],
      education: []
    };
  }
}

// New TypeScript-based document parser function
export async function parseResumeWithTypeScript(fileContent: string, fileName: string, fileType: string, jobDescription?: string): Promise<ParsedResumeData> {
  const openaiApiKey = process.env.OPENAI_API_KEY || 'none';

  try {
    console.log(`Processing ${fileName} with TypeScript parser`);

    // Use the new TypeScript document parser
    const result = await parseDocument(fileContent, fileName, fileType, openaiApiKey, jobDescription);

    console.log(`TypeScript extraction successful: ${result.method}`);
    return result;

  } catch (error) {
    console.error('Error in TypeScript resume parsing:', error);

    // Fallback to basic extraction using regex
    const contactInfo = extractContactInfoFromText(fileContent);
    return {
      contactInfo,
      extractedText: fileContent,
      method: 'typescript_fallback_regex',
      hasJobAnalysis: false
    };
  }
}

// Helper function to extract contact info from text using regex
function extractContactInfoFromText(text: string): any {
  const emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9_-]+)/gi;
  const phoneRegex = /(\+?\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g;
  const linkedinRegex = /(linkedin\.com\/in\/[a-zA-Z0-9-]+)/gi;

  // Extract email
  const emailMatch = text.match(emailRegex);
  const email = emailMatch ? emailMatch[0] : undefined;

  // Extract phone
  const phoneMatch = text.match(phoneRegex);
  const phone = phoneMatch ? phoneMatch[0] : undefined;

  // Extract LinkedIn
  const linkedinMatch = text.match(linkedinRegex);
  const linkedin = linkedinMatch ? `https://${linkedinMatch[0]}` : undefined;

  // Try to extract name (usually first line or near top)
  const lines = text.split('\n').filter(line => line.trim().length > 0);
  let name = 'Unknown';

  // Look for name in first few lines (skip if it's just email or phone)
  for (let i = 0; i < Math.min(5, lines.length); i++) {
    const line = lines[i].trim();
    // Name is usually 2-4 words, not too long, and doesn't contain @ or numbers
    if (line.length > 3 && line.length < 50 &&
        !line.includes('@') &&
        !/^\d+$/.test(line) &&
        /^[a-zA-Z\s.'-]+$/.test(line)) {
      const words = line.split(/\s+/);
      if (words.length >= 2 && words.length <= 4) {
        name = line;
        break;
      }
    }
  }

  return {
    name,
    email,
    phone,
    location: undefined,
    linkedin
  };
}

/**
 * @swagger
 * /resume/parse:
 *   post:
 *     summary: Parse resume file
 *     description: Parse uploaded resume file and extract candidate information with AI analysis
 *     tags: [Resume]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fileContent
 *               - fileName
 *               - fileType
 *             properties:
 *               fileContent:
 *                 type: string
 *                 description: Base64 encoded file content
 *               fileName:
 *                 type: string
 *                 description: Original file name
 *               fileType:
 *                 type: string
 *                 enum: [pdf, docx, doc, txt]
 *               extractContactOnly:
 *                 type: boolean
 *                 default: true
 *               jobDescription:
 *                 type: string
 *                 description: Job description for analysis matching
 *     responses:
 *       200:
 *         description: Resume parsed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 extractedText:
 *                   type: string
 *                 contactInfo:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     email:
 *                       type: string
 *                     phone:
 *                       type: string
 *                     location:
 *                       type: string
 *                     linkedin:
 *                       type: string
 *                 analysis:
 *                   type: object
 *                   properties:
 *                     overall_score:
 *                       type: number
 *                     match_score:
 *                       type: number
 *                     experience_years:
 *                       type: number
 *                     key_skills:
 *                       type: array
 *                       items:
 *                         type: string
 *                     recommendation:
 *                       type: string
 *                 hasJobAnalysis:
 *                   type: boolean
 *       400:
 *         description: Missing required fields
 *       500:
 *         description: Internal server error
 */
router.post('/parse', async (req, res) => {
  try {
    const { fileContent, fileName, fileType, extractContactOnly = true, jobDescription } = req.body;

    if (!fileContent || !fileName || !fileType) {
      return res.status(400).json({ 
        error: 'Missing required fields',
        contactInfo: { name: null, email: null, phone: null, location: null, linkedin: null }
      });
    }

    console.log(`Processing ${fileName} (${fileType})`);
    
    const result = await parseResumeWithTypeScript(fileContent, fileName, fileType, jobDescription);
    
    // Enhance result with analysis if we have sufficient text
    if (result.extractedText && result.extractedText.length > 100) {
      console.log('Text extracted successfully, performing analysis...');
      try {
        const openaiKey = process.env.OPENAI_API_KEY;
        let analysis = null;
        
        if (jobDescription && openaiKey) {
          try {
            analysis = await analyzeWithOpenAI(result.extractedText, jobDescription, openaiKey);
            console.log('OpenAI analysis completed');
          } catch (openaiError: unknown) {
            console.log('OpenAI failed, using fallback analysis:', openaiError instanceof Error ? openaiError.message : 'Unknown error');
            analysis = generateIntelligentFallback(result.extractedText, jobDescription);
          }
        } else {
          console.log('Using basic analysis (no job description or OpenAI key)');
          analysis = generateBasicAnalysis(result.extractedText);
        }
        
        result.analysis = analysis;
        result.hasJobAnalysis = !!analysis;
      } catch (analysisError) {
        console.error('Analysis failed:', analysisError);
      }
    }
    
    console.log('Successfully parsed resume:', fileName);
    res.json(result);

  } catch (error) {
    console.error('Error parsing resume with AI:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      contactInfo: { name: null, email: null, phone: null, location: null, linkedin: null }
    });
  }
});

// Analyze resume from file path (for public job portal)
router.post('/analyze-from-path', async (req: Request, res: Response) => {
  try {
    const { filePath, jobId, candidateName, email, phone, coverLetter } = req.body;
    
    if (!filePath) {
      return res.status(400).json({ error: 'File path is required' });
    }

    const fs = await import('fs');
    const path = await import('path');
    
    // Read the file from the path
    if (!fs.existsSync(filePath)) {
      return res.status(400).json({ error: 'File not found at specified path' });
    }
    
    const fileBuffer = fs.readFileSync(filePath);
    const fileName = path.basename(filePath);
    const fileType = fileName.toLowerCase().endsWith('.pdf') ? 'application/pdf' : 
                     fileName.toLowerCase().endsWith('.docx') ? 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' :
                     'application/msword';
    
    // Convert file to base64
    const fileContent = fileBuffer.toString('base64');
    
    console.log(`Processing file from path: ${fileName} (${fileType})`);

    // Get job description if jobId is provided
    let jobDescription = '';
    if (jobId) {
      try {
        const { db } = await import('../db');
        const { jobPostings } = await import('../../shared/schema');
        const { eq } = await import('drizzle-orm');
        
        const [job] = await db.select().from(jobPostings).where(eq(jobPostings.id, jobId));
        if (job) {
          jobDescription = `${job.title}\n\n${job.description}\n\nRequirements: ${job.requirements || 'Not specified'}`;
        }
      } catch (dbError) {
        console.error('Error fetching job details:', dbError);
      }
    }

    // Parse resume with TypeScript
    const resumeData = await parseResumeWithTypeScript(fileContent, fileName, fileType, jobDescription);

    // Prepare response
    const result: any = {
      contactInfo: resumeData.contactInfo,
      extractedText: resumeData.extractedText || '',
      method: 'typescript_extraction',
      hasJobAnalysis: false,
      candidateName,
      email,
      phone,
      coverLetter
    };

    // Try AI analysis if OpenAI key is available
    if (process.env.OPENAI_API_KEY && jobDescription && jobDescription.trim()) {
      try {
        console.log('Attempting OpenAI analysis...');
        result.analysis = await analyzeWithOpenAI(result.extractedText, jobDescription, process.env.OPENAI_API_KEY, jobId);
        result.hasJobAnalysis = true;
        console.log('OpenAI analysis completed successfully');
      } catch (openaiError: any) {
        console.error('AI analysis failed:', openaiError);
        result.analysis = generateIntelligentFallback(result.extractedText, jobDescription);
        result.hasJobAnalysis = false;
      }
    } else {
      console.log('Using fallback analysis');
      result.analysis = generateIntelligentFallback(result.extractedText, jobDescription || 'General position analysis');
      result.hasJobAnalysis = false;
    }

    res.json(result);

  } catch (error) {
    console.error('Error analyzing resume from path:', error);
    res.status(500).json({ 
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      contactInfo: { name: null, email: null, phone: null, location: null, linkedin: null }
    });
  }
});

// File upload and analysis endpoint for complete resume analysis
router.post('/analyze', upload.single('resume'), async (req: Request, res: Response) => {
  try {
    const file = req.file;
    const { jobDescription, selectedJobId } = req.body;

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Convert file to base64
    const fileContent = file.buffer.toString('base64');
    const fileName = file.originalname;
    const fileType = file.mimetype;

    console.log(`Processing file: ${fileName} (${fileType})`);

    // STEP 1: Extract contact info only (cheap operation - no job description = no AI analysis)
    const contactOnlyData = await parseResumeWithTypeScript(fileContent, fileName, fileType);

    // STEP 2: CRITICAL BUSINESS VALIDATION BEFORE any expensive analysis
    if (selectedJobId && contactOnlyData.contactInfo?.email) {
      try {
        // Get organization ID from authenticated user (if available) or from job posting
        let organizationId = null;
        
        // Check if this is an authenticated request
        if ((req as any).user && (req as any).user.organizationId) {
          const userOrganizationId = (req as any).user.organizationId;
          
          // CRITICAL: Verify selectedJobId belongs to authenticated user's organization
          const { db } = await import('../db');
          const { jobPostings } = await import('../../shared/schema');
          const { eq, and } = await import('drizzle-orm');
          
          const [job] = await db.select().from(jobPostings).where(
            and(
              eq(jobPostings.id, selectedJobId),
              eq(jobPostings.organizationId, userOrganizationId)
            )
          );
          
          if (!job) {
            console.error(`🚨 SECURITY: Authenticated user from org ${userOrganizationId} tried to access job ${selectedJobId} from different org`);
            return res.status(403).json({ 
              error: 'Access denied. Job posting not found in your organization.',
              errorCode: 'CROSS_ORG_ACCESS_DENIED',
              isBusinessRule: true,
              contactInfo: contactOnlyData.contactInfo
            });
          }
          
          organizationId = userOrganizationId;
        } else {
          // For non-authenticated requests, get org ID from job posting
          const { db } = await import('../db');
          const { jobPostings } = await import('../../shared/schema');
          const { eq } = await import('drizzle-orm');
          
          const [job] = await db.select().from(jobPostings).where(eq(jobPostings.id, selectedJobId));
          if (!job) {
            console.error(`🚨 CRITICAL: Job posting not found for selectedJobId: ${selectedJobId}`);
            return res.status(404).json({ 
              error: 'Selected job posting not found. Please refresh the page and try again.',
              errorCode: 'JOB_NOT_FOUND',
              isBusinessRule: true,
              contactInfo: contactOnlyData.contactInfo
            });
          }
          organizationId = job.organizationId;
        }
        
        if (organizationId) {
          const validationResult = await validateCandidateApplication(
            contactOnlyData.contactInfo.email, 
            selectedJobId, 
            organizationId
          );

          if (!validationResult.isValid) {
            console.log(`🚫 Resume analysis blocked due to business validation for ${contactOnlyData.contactInfo.email}:`, validationResult);
            
            // Send email notification for validation failure
            try {
              // Get job information for email context
              const { db } = await import('../db');
              const { jobPostings } = await import('../../shared/schema');
              const { eq } = await import('drizzle-orm');
              
              const [job] = await db.select().from(jobPostings).where(eq(jobPostings.id, selectedJobId));
              
              if (job && contactOnlyData.contactInfo?.email) {
                await sendCandidateValidationFailureEmail({
                  candidateName: contactOnlyData.contactInfo.name || 'Candidate',
                  candidateEmail: contactOnlyData.contactInfo.email,
                  errorCode: validationResult.errorCode,
                  errorMessage: validationResult.message,
                  data: validationResult.data,
                  jobTitle: job.title,
                  organizationName: job.organizationId // You might want to fetch actual organization name
                });
                console.log(`📧 Validation failure email sent to ${contactOnlyData.contactInfo.email} for ${validationResult.errorCode}`);
              }
            } catch (emailError) {
              console.error(`📧 Failed to send validation failure email:`, emailError);
            }
            
            return res.status(409).json({ 
              error: validationResult.message,
              errorCode: validationResult.errorCode,
              data: validationResult.data,
              isBusinessRule: true,
              contactInfo: contactOnlyData.contactInfo  // Still return contact info for UX
            });
          } else {
            console.log(`✅ Business validation passed for ${contactOnlyData.contactInfo.email} applying to job ${selectedJobId}`);
          }
        }
      } catch (validationError) {
        console.error('Business validation error during resume analysis:', validationError);
        // Continue with analysis but log the error
      }
    }

    // STEP 3: OPTIMIZED AI ANALYSIS - No duplicate parsing!
    // Use contact-only data for all cases to avoid duplication
    const fullResumeData = contactOnlyData;

    // If we need full text analysis and don't have it yet, get it efficiently
    if (jobDescription && jobDescription.trim() && jobDescription !== 'AUTO_MATCH_ALL_JOBS' && !fullResumeData.extractedText) {
      console.log('Getting full text for AI analysis...');
      // Only extract text if we don't have it from contact extraction
      const textOnlyResult = await parseResumeWithTypeScript(fileContent, fileName, fileType);
      fullResumeData.extractedText = textOnlyResult.extractedText;
    }

    // Prepare response
    const result: any = {
      contactInfo: fullResumeData.contactInfo,
      extractedText: fullResumeData.extractedText || '',
      method: 'typescript_extraction_optimized',
      hasJobAnalysis: false
    };

    // Try AI analysis if OpenAI key is available
    if (process.env.OPENAI_API_KEY && jobDescription && jobDescription.trim() && jobDescription !== 'AUTO_MATCH_ALL_JOBS' && result.extractedText) {
      try {
        console.log('Attempting OpenAI analysis...');
        result.analysis = await analyzeWithOpenAI(result.extractedText, jobDescription, process.env.OPENAI_API_KEY, selectedJobId);
        result.hasJobAnalysis = true;
        console.log('OpenAI analysis completed successfully');
      } catch (openaiError: any) {
        console.error('AI analysis failed:', openaiError);
        result.analysis = generateIntelligentFallback(result.extractedText, jobDescription);
        result.hasJobAnalysis = false;
      }
    } else {
      console.log('Using fallback analysis');
      result.analysis = generateIntelligentFallback(result.extractedText, jobDescription || 'General position analysis');
      result.hasJobAnalysis = false;
    }

    res.json(result);

  } catch (error) {
    console.error('Error in resume analysis:', error);
    res.status(500).json({ 
      error: 'Resume analysis failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Text-based analysis endpoint for backward compatibility
router.post('/analyze-text', async (req, res) => {
  try {
    const { resumeText, jobDescription, extractContactOnly = false } = req.body;
    
    if (!resumeText) {
      return res.status(400).json({ error: 'Resume text is required' });
    }

    console.log('Starting text-based resume analysis...');

    // If only extracting contact info, return basic analysis
    if (extractContactOnly) {
      const basicAnalysis = generateBasicAnalysis(resumeText);
      return res.json({ analysis: basicAnalysis });
    }

    // Use intelligent fallback analysis
    const analysis = generateIntelligentFallback(resumeText, jobDescription || 'General position analysis');
    return res.json({ analysis });

  } catch (error) {
    console.error('Error in text-based resume analysis:', error);
    const emergencyAnalysis = generateBasicAnalysis('');
    res.json({ analysis: emergencyAnalysis });
  }
});

// Helper function for OpenAI analysis with timeout
export async function analyzeWithOpenAI(resumeText: string, jobDescription: string, apiKey: string, selectedJobId?: string) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

  try {
    const analysisPrompt = `Analyze this resume against the job requirements and return a JSON object:

Job Description: ${jobDescription || 'General position'}

Resume: ${resumeText.substring(0, 4000)}

Return only valid JSON with this exact structure:
{
  "overall_score": 85,
  "match_score": 75,
  "experience_years": 5,
  "key_skills": ["skill1", "skill2", "skill3"],
  "matched_skills": ["skill1", "skill2"],
  "missing_skills": ["skill3", "skill4"],
  "strengths": [
    "Strong technical background with X years of experience",
    "Proven track record in relevant technologies",
    "Leadership experience managing teams"
  ],
  "concerns": [
    "Limited experience with specific requirement X",
    "No direct experience in industry Y"
  ],
  "recommendation": "HIRE",
  "detailed_feedback": "This candidate demonstrates strong qualifications for the role. Their experience with [specific technologies/skills] aligns well with the job requirements. Key strengths include [list 2-3 specific strengths from resume]. Areas for development include [list 2-3 areas]. The candidate's background in [relevant experience] makes them a strong fit. Overall assessment: [detailed reasoning for recommendation].",
  "interview_questions": [
    "Can you walk me through your experience with [specific technology from job description]?",
    "How did you handle [specific challenge relevant to the role]?",
    "Tell me about a time you [behavioral question relevant to job requirements]?"
  ]
}

Important: 
- For strengths, provide 3-5 specific, detailed strengths based on the resume content
- For detailed_feedback, write a comprehensive 3-4 sentence assessment explaining the match
- For interview_questions, provide 3 specific questions tailored to both the resume and job requirements
- Base all analysis on actual resume content, not generic responses`;

    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert HR analyst. Analyze resumes thoroughly and provide detailed, specific feedback. Return only valid JSON without any markdown formatting.'
          },
          {
            role: 'user',
            content: analysisPrompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.3
      }),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`OpenAI API failed: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content.trim();

    // Clean and parse JSON
    let jsonContent = content.replace(/```json\s*/, '').replace(/```\s*$/, '').trim();
    const analysis = JSON.parse(jsonContent);
    
    return analysis;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// Generate intelligent fallback analysis based on resume content
export function generateIntelligentFallback(resumeText: string, jobDescription: string) {
  const text = resumeText.toLowerCase();
  const jobDesc = (jobDescription || '').toLowerCase();
  
  // Extract skills from resume using comprehensive keyword matching
  const skillKeywords = [
    // Programming Languages
    'java', 'python', 'javascript', 'typescript', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'scala', 'kotlin',
    // Frontend Technologies
    'react', 'angular', 'vue', 'html', 'css', 'sass', 'less', 'webpack', 'vite',
    // Backend Technologies
    'node', 'express', 'django', 'flask', 'spring', 'hibernate', '.net',
    // Databases
    'sql', 'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch', 'dynamodb',
    // Cloud & DevOps
    'aws', 'azure', 'gcp', 'docker', 'kubernetes', 'jenkins', 'gitlab', 'github actions', 'terraform',
    // Tools & Methodologies
    'git', 'jira', 'confluence', 'agile', 'scrum', 'kanban', 'ci/cd', 'microservices', 'api', 'rest', 'graphql',
    // Leadership & Management
    'leadership', 'management', 'project management', 'team lead', 'architect', 'senior', 'principal', 'director',
    // Business & Domain
    'erp', 'sap', 'oracle', 'salesforce', 'crm', 'financial', 'healthcare', 'e-commerce',
    // General Tech
    'engineering', 'software', 'development', 'backend', 'frontend', 'full stack', 'devops', 'qa', 'testing'
  ];
  
  const foundSkills = skillKeywords.filter(skill => {
    // More flexible matching including variations
    const variations = [
      skill,
      skill.replace(/\s+/g, ''),
      skill.toUpperCase(),
      skill.replace(/js$/, 'javascript'),
      skill.replace(/javascript/, 'js')
    ];
    return variations.some(variant => text.includes(variant.toLowerCase()));
  });
  
  // Calculate experience years using multiple patterns
  const experiencePatterns = [
    /(\d+)\+?\s*years?\s*(of\s*)?experience/gi,
    /(\d+)\+?\s*years?\s*(in|with)/gi,
    /experience.*?(\d+)\+?\s*years?/gi,
    /(\d{4})\s*[-–]\s*(\d{4})/g, // Date ranges
    /(\d{4})\s*[-–]\s*present/gi
  ];
  
  let maxExperience = 3; // Default minimum
  
  experiencePatterns.forEach(pattern => {
    const matches = resumeText.match(pattern) || [];
    matches.forEach(match => {
      const numbers = match.match(/\d+/g) || [];
      numbers.forEach(num => {
        const value = parseInt(num);
        if (value > 1990 && value < 2030) {
          // This looks like a year, calculate experience from current year
          const currentYear = new Date().getFullYear();
          const yearExperience = Math.max(0, currentYear - value);
          if (yearExperience <= 40) { // Reasonable max
            maxExperience = Math.max(maxExperience, yearExperience);
          }
        } else if (value <= 50) {
          // This looks like years of experience
          maxExperience = Math.max(maxExperience, value);
        }
      });
    });
  });
  
  // Also check for senior/lead/principal titles which imply experience
  if (text.includes('senior') || text.includes('sr.')) maxExperience = Math.max(maxExperience, 5);
  if (text.includes('lead') || text.includes('principal')) maxExperience = Math.max(maxExperience, 7);
  if (text.includes('director') || text.includes('manager')) maxExperience = Math.max(maxExperience, 8);

  // Calculate match score based on job description
  let matchScore = 70;
  if (jobDesc) {
    const jobSkills = skillKeywords.filter(skill => jobDesc.includes(skill));
    const commonSkills = foundSkills.filter(skill => jobSkills.includes(skill));
    matchScore = jobSkills.length > 0 ? Math.round((commonSkills.length / jobSkills.length) * 100) : 70;
  }

  const overallScore = Math.min(95, Math.max(60, (foundSkills.length * 8) + (maxExperience * 3) + 40));

  // Generate contextual strengths and concerns
  const strengths = [
    `${maxExperience} years of professional experience`,
    `Technical proficiency in ${foundSkills.length} relevant areas`
  ];
  
  if (foundSkills.includes('leadership') || foundSkills.includes('management')) {
    strengths.push('Demonstrated leadership and management capabilities');
  }
  if (foundSkills.includes('senior') || foundSkills.includes('principal')) {
    strengths.push('Senior-level technical expertise');
  }
  if (foundSkills.length >= 10) {
    strengths.push('Diverse technical skill set across multiple domains');
  }
  
  const concerns = [];
  if (matchScore < 70) {
    concerns.push('Some key job requirements may not be directly addressed');
  }
  if (foundSkills.length < 5) {
    concerns.push('Limited technical details provided in resume');
  }
  concerns.push('Technical depth and team collaboration skills need interview assessment');
  
  // Generate contextual interview questions
  const baseQuestions = [
    'Walk me through your most challenging technical project',
    'How do you approach learning new technologies or frameworks?',
    'Describe your experience with code review and quality assurance'
  ];
  
  const contextQuestions = [];
  if (foundSkills.includes('leadership') || foundSkills.includes('management')) {
    contextQuestions.push('How do you handle conflicting priorities when managing a technical team?');
  }
  if (jobDesc.includes('agile') || jobDesc.includes('scrum')) {
    contextQuestions.push('Describe your experience working in agile development environments');
  }
  if (foundSkills.includes('aws') || foundSkills.includes('cloud')) {
    contextQuestions.push('What considerations do you make when designing cloud-native applications?');
  }
  
  const allQuestions = [...baseQuestions, ...contextQuestions].slice(0, 5);
  
  return {
    overall_score: overallScore,
    match_score: Math.min(matchScore, overallScore),
    experience_years: maxExperience,
    key_skills: foundSkills.slice(0, 12),
    matched_skills: foundSkills.filter(skill => jobDesc.includes(skill.toLowerCase())).slice(0, 8),
    missing_skills: jobDesc ? skillKeywords.filter(skill => jobDesc.includes(skill.toLowerCase()) && !foundSkills.includes(skill)).slice(0, 4) : ['Specific domain expertise'],
    strengths: strengths.slice(0, 4),
    concerns: concerns.slice(0, 3),
    recommendation: overallScore >= 85 ? 'HIRE' : overallScore >= 70 ? 'INTERVIEW' : 'REJECT',
    detailed_feedback: `Analysis reveals ${overallScore >= 85 ? 'excellent' : overallScore >= 70 ? 'solid' : 'basic'} candidate qualifications. With ${maxExperience} years of experience and expertise in ${foundSkills.slice(0, 5).join(', ')}, this candidate demonstrates ${matchScore >= 80 ? 'strong alignment' : matchScore >= 60 ? 'good potential fit' : 'partial alignment'} with the role requirements. ${overallScore >= 75 ? 'Highly recommend advancing to technical interview to validate depth of expertise and assess team dynamics.' : 'Recommend structured interview focused on technical competencies and role-specific requirements.'}`,
    interview_questions: allQuestions
  };
}

// Generate basic analysis for emergency fallback
function generateBasicAnalysis(resumeText: string) {
  return {
    overall_score: 75,
    match_score: 70,
    experience_years: 5,
    key_skills: ['Professional Experience', 'Communication', 'Problem Solving'],
    matched_skills: ['Professional Experience'],
    missing_skills: ['Specific Technical Requirements'],
    strengths: ['Resume successfully processed', 'Contains relevant experience'],
    concerns: ['Detailed analysis temporarily unavailable'],
    recommendation: 'INTERVIEW',
    detailed_feedback: 'Resume has been processed successfully. Recommend proceeding with interview for detailed evaluation.',
    interview_questions: [
      'Tell me about your professional background.',
      'What interests you about this role?',
      'What are your key strengths and achievements?'
    ]
  };
}

export default router;