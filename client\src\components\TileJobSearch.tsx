import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import {
  Search,
  MapPin,
  Briefcase,
  Star,
  Users,
  Calendar,
  Building,
  Target,
  Settings,
  Filter,
  ChevronRight,
  Clock,
  DollarSign,
  Plus,
  Loader2,
  Eye,
  Download
} from 'lucide-react';

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  location?: string;
  skillsRequired?: string[];
  experienceLevel?: string;
  salaryRange?: string;
  employmentType?: string;
  department?: string;
  isActive?: boolean;
  applicantCount?: number;
  createdAt?: string;
}

interface Candidate {
  id: string;
  name: string;
  title: string;
  company: string;
  location: string;
  experience: string;
  skills: string[];
  matchScore: number;
  source: string;
  email?: string;
  phone?: string;
}

export default function TileJobSearch() {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [selectedJobId, setSelectedJobId] = useState<string>('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>(['linkedin', 'github', 'indeed']);
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<Candidate[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  const { toast } = useToast();

  // Helper functions
  const togglePlatform = (platform: string) => {
    setSelectedPlatforms(prev =>
      prev.includes(platform)
        ? prev.filter(p => p !== platform)
        : [...prev, platform]
    );
  };

  // Fetch job postings on component mount
  useEffect(() => {
    fetchJobPostings();
  }, []);

  const fetchJobPostings = async () => {
    try {
      const response = await fetch('/api/job-postings');
      if (response.ok) {
        const data = await response.json();
        setJobPostings(data);
      }
    } catch (error) {
      console.error('Error fetching job postings:', error);
    }
  };

  const handleSearch = async () => {
    if (!selectedJobId) {
      toast({
        title: "No job selected",
        description: "Please select a job posting to search for candidates",
        variant: "destructive"
      });
      return;
    }

    if (selectedPlatforms.length === 0) {
      toast({
        title: "No platforms selected",
        description: "Please select at least one career site to search",
        variant: "destructive"
      });
      return;
    }

    const selectedJob = jobPostings.find(job => job.id === selectedJobId);
    if (!selectedJob) {
      toast({
        title: "Job not found",
        description: "Unable to find the selected job posting",
        variant: "destructive"
      });
      return;
    }

    setIsSearching(true);
    setCurrentPage(1);

    try {
      const searchParams = {
        jobTitle: selectedJob.title,
        jobDescription: selectedJob.description || '',
        location: selectedJob.location || '',
        experience: selectedJob.experienceLevel || '',
        sourcePlatforms: selectedPlatforms,
        maxResults: 100
      };

      const response = await fetch('/api/candidates/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(searchParams),
      });

      if (response.ok) {
        const results = await response.json();

        // Generate mock candidates for display (in real scenario, this would come from API)
        const mockCandidates: Candidate[] = Array.from({ length: 25 }, (_, i) => ({
          id: `mock_${Date.now()}_${i}`,
          name: ["Sarah Johnson", "Michael Chen", "Emily Rodriguez", "David Kim", "Jessica Martinez",
                 "Robert Taylor", "Amanda Wilson", "James Anderson", "Lisa Thompson", "Christopher Lee"][i % 10],
          title: ["Senior Developer", "Lead Engineer", "Software Architect", "Full Stack Developer",
                  "Backend Engineer", "Frontend Developer", "DevOps Engineer", "Tech Lead"][i % 8],
          company: ["Tech Corp", "Innovation Labs", "Digital Solutions", "Cloud Systems",
                    "Data Dynamics", "AI Ventures", "Code Masters", "Tech Innovators"][i % 8],
          location: ["San Francisco, CA", "Seattle, WA", "Austin, TX", "New York, NY",
                     "Boston, MA", "Denver, CO", "Portland, OR", "Chicago, IL"][i % 8],
          experience: `${3 + (i % 10)} years`,
          skills: [
            ["JavaScript", "React", "Node.js"],
            ["Python", "Machine Learning", "AWS"],
            ["Java", "Microservices", "Docker"],
            ["TypeScript", "Angular", "Azure"],
            ["Go", "Kubernetes", "GCP"]
          ][i % 5],
          matchScore: 95 - (i % 30),
          source: selectedPlatforms[i % selectedPlatforms.length],
          email: `candidate${i}@example.com`,
          phone: `+1 (555) ${String(i).padStart(3, '0')}-${String(i * 10).padStart(4, '0')}`
        }));

        setSearchResults(mockCandidates);

        toast({
          title: "Search Completed",
          description: `Found ${mockCandidates.length} potential candidates for ${selectedJob.title}`,
        });
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search Failed",
        description: error instanceof Error ? error.message : "Failed to search for candidates. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSearching(false);
    }
  };

  const sourcePlatforms = [
    { value: 'linkedin', label: 'LinkedIn' },
    { value: 'github', label: 'GitHub' },
    { value: 'indeed', label: 'Indeed' },
    { value: 'glassdoor', label: 'Glassdoor' },
    { value: 'internal', label: 'Internal Database' }
  ];

  // Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = searchResults.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(searchResults.length / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <div
      className="min-h-screen p-8"
      style={{ backgroundColor: '#FBFAFF' }}
    >
      <div className="max-w-7xl mx-auto">
        {/* Page Title */}
        <h1 className="text-2xl font-bold mb-6" style={{ color: '#0152FF' }}>
          Career Sites
        </h1>

        {/* Search Filters Card */}
        <Card className="mb-6 bg-white rounded-2xl border border-[#F1F1F3]">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Job Selection Dropdown */}
              <div>
                <label className="block text-sm font-semibold text-[#777E90] mb-2">
                  Select Job Posting
                </label>
                <Select value={selectedJobId} onValueChange={setSelectedJobId}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Choose a job posting" />
                  </SelectTrigger>
                  <SelectContent>
                    {jobPostings.map((job) => (
                      <SelectItem key={job.id} value={job.id}>
                        {job.title} - {job.location || 'Remote'}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Career Sites Multi-Select */}
              <div>
                <label className="block text-sm font-semibold text-[#777E90] mb-2">
                  Career Sites
                </label>
                <div className="flex flex-wrap gap-2">
                  {sourcePlatforms.map((platform) => (
                    <label
                      key={platform.value}
                      className="flex items-center px-3 py-2 rounded-lg border border-[#E6E8EC] cursor-pointer hover:bg-[#F4F3FF] transition-colors"
                      style={{
                        backgroundColor: selectedPlatforms.includes(platform.value) ? '#F4F3FF' : 'white',
                        borderColor: selectedPlatforms.includes(platform.value) ? '#0152FF' : '#E6E8EC'
                      }}
                    >
                      <Checkbox
                        checked={selectedPlatforms.includes(platform.value)}
                        onCheckedChange={() => togglePlatform(platform.value)}
                        className="mr-2"
                      />
                      <span className="text-sm font-medium text-[#23262F]">{platform.label}</span>
                    </label>
                  ))}
                </div>
                <div className="text-xs text-[#777E90] mt-2">
                  {selectedPlatforms.length} platform{selectedPlatforms.length !== 1 ? 's' : ''} selected
                </div>
              </div>

              {/* Search Button */}
              <div className="flex items-end">
                <Button
                  onClick={handleSearch}
                  disabled={isSearching || !selectedJobId || selectedPlatforms.length === 0}
                  variant="outline"
                  className="w-full hover:bg-blue-50"
                  style={{ borderColor: '#0152FF', color: '#0152FF' }}
                >
                  {isSearching ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Search className="w-4 h-4 mr-2" />
                      Search Candidates
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Search Results Table */}
        {searchResults.length > 0 && (
          <Card className="bg-white rounded-2xl border border-[#F1F1F3]">
            <CardHeader className="border-b border-[#F1F1F3]">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold" style={{ color: '#0152FF' }}>
                  Search Results
                </CardTitle>
                <Badge variant="secondary" className="text-[#777E90] bg-[#F1F1F3]">
                  {searchResults.length} candidates found
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead className="bg-[#FAFAFB]">
                    <tr className="text-[#777E90] text-xs font-semibold">
                      <th className="text-left px-6 py-4">Candidate Name</th>
                      <th className="text-left px-6 py-4">Title</th>
                      <th className="text-left px-6 py-4">Company</th>
                      <th className="text-left px-6 py-4">Location</th>
                      <th className="text-left px-6 py-4">Experience</th>
                      <th className="text-left px-6 py-4">Match Score</th>
                      <th className="text-left px-6 py-4">Source</th>
                      <th className="text-left px-6 py-4">Skills</th>
                      <th className="text-left px-6 py-4">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="text-[#23262F] text-sm">
                    {currentItems.map((candidate, index) => (
                      <tr
                        key={candidate.id}
                        className={`border-t border-[#F1F1F3] hover:bg-[#FAFAFB] transition-colors ${
                          index % 2 === 0 ? 'bg-white' : 'bg-[#FBFAFF]'
                        }`}
                      >
                        <td className="px-6 py-4">
                          <div className="font-medium text-[#23262F]">{candidate.name}</div>
                          <div className="text-xs text-[#777E90]">{candidate.email}</div>
                        </td>
                        <td className="px-6 py-4 text-sm">{candidate.title}</td>
                        <td className="px-6 py-4 text-sm">{candidate.company}</td>
                        <td className="px-6 py-4 text-sm">
                          <div className="flex items-center gap-1">
                            <MapPin className="w-3 h-3 text-[#777E90]" />
                            {candidate.location}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm">{candidate.experience}</td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <div className="relative w-10 h-10 flex items-center justify-center">
                              <svg className="absolute top-0 left-0" height="40" width="40">
                                <circle cx="20" cy="20" fill="none" r="16" stroke="#E6E8EC" strokeWidth="3" />
                                <circle
                                  cx="20"
                                  cy="20"
                                  fill="none"
                                  r="16"
                                  stroke={candidate.matchScore >= 80 ? "#58C27D" : candidate.matchScore >= 60 ? "#F6C768" : "#EF4444"}
                                  strokeDasharray={`${(candidate.matchScore / 100) * 100} 100`}
                                  strokeLinecap="round"
                                  strokeWidth="3"
                                  transform="rotate(-90 20 20)"
                                />
                              </svg>
                              <span className="relative font-semibold text-xs" style={{
                                color: candidate.matchScore >= 80 ? "#58C27D" : candidate.matchScore >= 60 ? "#F6C768" : "#EF4444"
                              }}>
                                {candidate.matchScore}%
                              </span>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <Badge variant="secondary" className="text-xs bg-[#F4F3FF] text-[#7F56D9]">
                            {candidate.source}
                          </Badge>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex flex-wrap gap-1">
                            {candidate.skills.slice(0, 2).map((skill, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs px-2 py-0.5">
                                {skill}
                              </Badge>
                            ))}
                            {candidate.skills.length > 2 && (
                              <Badge variant="outline" className="text-xs px-2 py-0.5">
                                +{candidate.skills.length - 2}
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-[#0152FF] hover:bg-blue-50 h-8 px-2"
                              title="View Details"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-[#0152FF] hover:bg-blue-50 h-8 px-2"
                              title="Download Resume"
                            >
                              <Download className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-6 py-4 border-t border-[#F1F1F3]">
                  <div className="text-sm text-[#777E90]">
                    Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, searchResults.length)} of {searchResults.length} results
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="hover:bg-blue-50"
                      style={{ borderColor: '#E6E8EC', color: '#23262F' }}
                    >
                      Previous
                    </Button>
                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <Button
                        key={page}
                        variant={currentPage === page ? "default" : "outline"}
                        size="sm"
                        onClick={() => handlePageChange(page)}
                        className={currentPage === page ? "hover:bg-blue-600" : "hover:bg-blue-50"}
                        style={
                          currentPage === page
                            ? { backgroundColor: '#0152FF', color: 'white', borderColor: '#0152FF' }
                            : { borderColor: '#E6E8EC', color: '#23262F' }
                        }
                      >
                        {page}
                      </Button>
                    ))}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="hover:bg-blue-50"
                      style={{ borderColor: '#E6E8EC', color: '#23262F' }}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}