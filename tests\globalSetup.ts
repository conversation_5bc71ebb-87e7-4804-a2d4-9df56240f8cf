import { config } from 'dotenv';

export default async function globalSetup() {
  // Load test environment variables
  config({ path: '.env.test' });
  
  console.log('🧪 Setting up test environment...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/hrms_test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.ELEVENLABS_API_KEY = 'test-elevenlabs-key';
  process.env.ZOOM_SDK_KEY = 'test-zoom-key';
  process.env.ZOOM_SDK_SECRET = 'test-zoom-secret';
  process.env.OPENAI_API_KEY = 'test-openai-key';
  
  // Disable external service calls in tests
  process.env.DISABLE_EXTERNAL_SERVICES = 'true';
  
  console.log('✅ Test environment setup complete');
}
