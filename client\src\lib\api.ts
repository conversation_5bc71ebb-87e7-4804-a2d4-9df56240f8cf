// Enhanced API utilities with proper authentication
import { authAPI } from './authApi';

export function getAuthHeaders(): Record<string, string> {
  const token = authAPI.getAccessToken();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

export function getAuthHeadersForFormData(): Record<string, string> {
  const token = authAPI.getAccessToken();
  const headers: Record<string, string> = {};
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  return headers;
}

export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  // Use the new authAPI with automatic token refresh
  return authAPI.makeAuthenticatedRequest(url, options);
}

// Legacy function name support for backward compatibility
export { authenticatedFetch as apiRequest };