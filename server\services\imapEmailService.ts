// Optional IMAP email service for self-contained email receiving
// Install: npm install imap mailparser

/*
import Imap from 'imap';
import { simpleParser } from 'mailparser';
import { db } from '../db';
import { candidateAvailability, candidates } from '@shared/schema';
import { eq } from 'drizzle-orm';

interface EmailConfig {
  user: string;
  password: string;
  host: string;
  port: number;
  tls: boolean;
}

class EmailService {
  private imap: Imap;
  private config: EmailConfig;

  constructor() {
    this.config = {
      user: process.env.EMAIL_USER || '',
      password: process.env.EMAIL_PASS || '',
      host: process.env.EMAIL_HOST || 'imap.gmail.com',
      port: parseInt(process.env.EMAIL_PORT || '993'),
      tls: true
    };

    this.imap = new Imap(this.config);
  }

  async startMonitoring(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.imap.once('ready', () => {
        console.log('IMAP connection ready');
        this.openInbox();
        resolve();
      });

      this.imap.once('error', (err: Error) => {
        console.error('IMAP connection error:', err);
        reject(err);
      });

      this.imap.once('end', () => {
        console.log('IMAP connection ended');
      });

      this.imap.connect();
    });
  }

  private openInbox(): void {
    this.imap.openBox('INBOX', false, (err, box) => {
      if (err) {
        console.error('Error opening inbox:', err);
        return;
      }

      console.log('Inbox opened, monitoring for new emails...');
      
      // Listen for new emails
      this.imap.on('mail', () => {
        this.checkForNewEmails();
      });

      // Initial check
      this.checkForNewEmails();
    });
  }

  private checkForNewEmails(): void {
    // Search for unread emails with specific subject
    this.imap.search(['UNSEEN', ['SUBJECT', 'Interview Availability Request']], (err, results) => {
      if (err) {
        console.error('Error searching emails:', err);
        return;
      }

      if (results.length === 0) {
        return;
      }

      console.log(`Found ${results.length} new emails`);

      const fetch = this.imap.fetch(results, { bodies: '' });

      fetch.on('message', (msg, seqno) => {
        let buffer = '';

        msg.on('body', (stream) => {
          stream.on('data', (chunk) => {
            buffer += chunk.toString('utf8');
          });

          stream.once('end', () => {
            this.processEmail(buffer, seqno);
          });
        });
      });

      fetch.once('error', (err) => {
        console.error('Fetch error:', err);
      });
    });
  }

  private async processEmail(rawEmail: string, seqno: number): Promise<void> {
    try {
      const parsed = await simpleParser(rawEmail);
      
      const from = parsed.from?.text || '';
      const subject = parsed.subject || '';
      const text = parsed.text || '';
      const html = parsed.html || '';

      console.log(`Processing email from: ${from}, subject: ${subject}`);

      // Extract candidate ID
      const candidateIdMatch = text.match(/Candidate ID:\s*([a-f0-9-]+)/i) || 
                              html.match(/Candidate ID:\s*([a-f0-9-]+)/i);

      if (!candidateIdMatch) {
        console.log('No candidate ID found in email');
        this.markAsRead(seqno);
        return;
      }

      const candidateId = candidateIdMatch[1];

      // Verify candidate exists
      const candidate = await db
        .select()
        .from(candidates)
        .where(eq(candidates.id, candidateId))
        .limit(1);

      if (candidate.length === 0) {
        console.log('Candidate not found:', candidateId);
        this.markAsRead(seqno);
        return;
      }

      // Parse availability information
      const dayMatch = text.match(/Preferred Day:\s*([A-Za-z]+)/i);
      const timeMatch = text.match(/Preferred Time:\s*([^\\n\\r]+)/i);
      const notesMatch = text.match(/Additional Notes:\s*([^\\n\\r]+)/i);

      const preferredDay = dayMatch ? dayMatch[1].trim() : 'Not specified';
      const preferredTime = timeMatch ? timeMatch[1].trim() : 'Not specified';
      const notes = notesMatch ? notesMatch[1].trim() : '';

      // Update availability
      const availabilityData = {
        candidateId,
        respondedAt: new Date(),
        status: 'received' as const,
        availableSlots: JSON.stringify([
          `${preferredDay} 9:00 AM - 10:00 AM`,
          `${preferredDay} 10:00 AM - 11:00 AM`,
          `${preferredDay} 2:00 PM - 3:00 PM`,
          `${preferredDay} 3:00 PM - 4:00 PM`
        ]),
        selectedSlot: JSON.stringify({
          day: preferredDay,
          time: preferredTime,
          notes: notes || 'Available for interview',
          duration: '1 hour'
        })
      };

      // Check if availability already exists
      const existing = await db
        .select()
        .from(candidateAvailability)
        .where(eq(candidateAvailability.candidateId, candidateId))
        .limit(1);

      if (existing.length > 0) {
        await db
          .update(candidateAvailability)
          .set({
            ...availabilityData,
            updatedAt: new Date()
          })
          .where(eq(candidateAvailability.id, existing[0].id));
      } else {
        await db
          .insert(candidateAvailability)
          .values(availabilityData);
      }

      console.log(`Updated availability for candidate ${candidateId}: ${preferredDay} ${preferredTime}`);
      
      // Mark email as read
      this.markAsRead(seqno);

    } catch (error) {
      console.error('Error processing email:', error);
      this.markAsRead(seqno);
    }
  }

  private markAsRead(seqno: number): void {
    this.imap.addFlags(seqno, ['\\Seen'], (err) => {
      if (err) {
        console.error('Error marking email as read:', err);
      }
    });
  }

  stop(): void {
    this.imap.end();
  }
}

export const emailService = new EmailService();

// To start monitoring (add to your server startup):
// emailService.startMonitoring().catch(console.error);
*/

// This file shows how to implement IMAP email monitoring
// Uncomment and install dependencies if you want to use this approach
export const placeholder = true;