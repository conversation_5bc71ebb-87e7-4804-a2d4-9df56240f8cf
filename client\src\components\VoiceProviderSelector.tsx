import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { useQuery } from '@tanstack/react-query';
import { 
  Phone, 
  PhoneCall, 
  Globe, 
  User, 
  Cloud, 
  Smartphone,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface VoiceProvider {
  name: string;
  available: boolean;
}

interface VoiceProviderSelectorProps {
  onProviderSelect: (provider: string) => void;
  selectedProvider?: string;
}

export default function VoiceProviderSelector({ onProviderSelect, selectedProvider }: VoiceProviderSelectorProps) {
  const [showInfo, setShowInfo] = useState(false);

  // Fetch available providers
  const { data: providers = [], isLoading } = useQuery({
    queryKey: ['/api/voice-providers/providers'],
    queryFn: async () => {
      const response = await fetch('/api/voice-providers/providers', {
        credentials: 'include'
      });
      if (!response.ok) throw new Error('Failed to fetch providers');
      return response.json();
    }
  });

  const getProviderIcon = (name: string) => {
    switch (name.toLowerCase()) {
      case 'twilio': return <Cloud className="w-5 h-5 text-red-500" />;
      case 'vonage': return <Cloud className="w-5 h-5 text-blue-500" />;
      case 'amazon connect': return <Cloud className="w-5 h-5 text-orange-500" />;
      case 'google ccai': return <Cloud className="w-5 h-5 text-green-500" />;
      case 'google speech enhanced': return <Cloud className="w-5 h-5 text-blue-600" />;
      case 'webrtc browser call': return <Globe className="w-5 h-5 text-purple-500" />;
      case 'manual call': return <User className="w-5 h-5 text-gray-500" />;
      default: return <Phone className="w-5 h-5 text-gray-400" />;
    }
  };

  const getProviderDescription = (name: string) => {
    switch (name.toLowerCase()) {
      case 'twilio':
        return {
          description: 'Industry-leading cloud communications platform with AI voice capabilities',
          setup: 'Requires Twilio Account SID, Auth Token, and phone number',
          features: ['AI-powered conversations', 'Call recording', 'Transcription', 'Global reach'],
          cost: 'Pay-per-minute pricing'
        };
      case 'vonage':
        return {
          description: 'Vonage API platform for voice communications',
          setup: 'Requires Vonage API key, secret, and application ID',
          features: ['Voice API', 'Call recording', 'Global coverage', 'WebRTC support'],
          cost: 'Pay-per-minute pricing'
        };
      case 'amazon connect':
        return {
          description: 'AWS cloud-based contact center service',
          setup: 'Requires AWS credentials and Connect instance',
          features: ['AI-powered contact flows', 'Lex integration', 'Analytics', 'Scalable'],
          cost: 'Pay-per-use AWS pricing'
        };
      case 'google ccai':
        return {
          description: 'Google Cloud Contact Center AI',
          setup: 'Requires Google Cloud credentials and CCAI setup',
          features: ['Dialogflow integration', 'Natural language processing', 'Analytics'],
          cost: 'Google Cloud pricing'
        };
      case 'webrtc browser call':
        return {
          description: 'Browser-based calling without external service dependencies',
          setup: 'No external setup required - uses browser WebRTC',
          features: ['No external costs', 'Browser-based', 'Real-time communication'],
          cost: 'Free (data usage only)'
        };
      case 'manual call':
        return {
          description: 'Human-initiated calls with guided conversation notes',
          setup: 'No technical setup required',
          features: ['Human touch', 'Flexible conversations', 'Manual note-taking', 'No automation'],
          cost: 'Your phone service costs only'
        };
      default:
        return {
          description: 'Voice communication provider',
          setup: 'Setup requirements vary',
          features: ['Voice calling'],
          cost: 'Pricing varies'
        };
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4" />
            <p className="text-gray-600">Loading voice providers...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Provider Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <PhoneCall className="w-5 h-5 text-blue-600" />
              Select Voice Provider
            </CardTitle>
            <Dialog open={showInfo} onOpenChange={setShowInfo}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Info className="w-4 h-4 mr-2" />
                  Compare Options
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Voice Provider Comparison</DialogTitle>
                </DialogHeader>
                <div className="grid gap-4">
                  {providers.map((provider: VoiceProvider) => {
                    const info = getProviderDescription(provider.name);
                    return (
                      <Card key={provider.name} className="border">
                        <CardHeader className="pb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              {getProviderIcon(provider.name)}
                              <h3 className="font-semibold">{provider.name}</h3>
                            </div>
                            {provider.available ? (
                              <Badge className="bg-green-100 text-green-800">
                                <CheckCircle className="w-3 h-3 mr-1" />
                                Available
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-gray-500">
                                <XCircle className="w-3 h-3 mr-1" />
                                Setup Required
                              </Badge>
                            )}
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <p className="text-sm text-gray-600">{info.description}</p>
                          
                          <div>
                            <h4 className="font-medium text-sm mb-2">Setup Requirements:</h4>
                            <p className="text-xs text-gray-500">{info.setup}</p>
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-sm mb-2">Key Features:</h4>
                            <div className="flex flex-wrap gap-1">
                              {info.features.map((feature, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          <div>
                            <h4 className="font-medium text-sm mb-1">Cost:</h4>
                            <p className="text-xs text-gray-500">{info.cost}</p>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Select value={selectedProvider} onValueChange={onProviderSelect}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a voice provider..." />
              </SelectTrigger>
              <SelectContent>
                {providers.map((provider: VoiceProvider) => (
                  <SelectItem 
                    key={provider.name} 
                    value={provider.name.toLowerCase().replace(/\s+/g, '-')}
                  >
                    <div className="flex items-center gap-2">
                      {getProviderIcon(provider.name)}
                      <span>{provider.name}</span>
                      {provider.available && (
                        <Badge className="ml-2 bg-green-100 text-green-800 text-xs">
                          Ready
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {selectedProvider && (
              <Alert>
                <Info className="w-4 h-4" />
                <AlertDescription>
                  {(() => {
                    const providerName = providers.find((p: VoiceProvider) => 
                      p.name.toLowerCase().replace(/\s+/g, '-') === selectedProvider
                    )?.name || '';
                    const info = getProviderDescription(providerName);
                    return info.description;
                  })()}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Quick Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Quick Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3">
            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
              <User className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">Start Simple: Manual Call</h4>
                <p className="text-sm text-blue-700">No setup required. Make calls yourself with guided notes.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
              <Globe className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-green-900">No Cost: WebRTC Browser Call</h4>
                <p className="text-sm text-green-700">Free browser-based calling with basic features.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
              <Cloud className="w-5 h-5 text-purple-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-purple-900">Professional: Twilio</h4>
                <p className="text-sm text-purple-700">Full AI automation with recording and transcription.</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}