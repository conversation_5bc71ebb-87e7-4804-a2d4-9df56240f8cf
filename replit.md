# Multi-Tenant SaaS Application

## Overview
This project is a secure, full-stack SaaS application. Its core purpose is to provide a multi-tenant platform with organization-based data segregation, JWT authentication, and role-based access control. Key capabilities include comprehensive user management, secure data handling, and an intelligent voice agent for candidate screening and scheduling. The business vision is to offer a scalable and secure solution for managing organizational data and streamlining recruitment processes, with strong market potential in the HR tech and multi-tenant application sectors. The project aims to integrate advanced AI features for enhanced automation and user experience.

## User Preferences
Preferred communication style: Simple, everyday language with focus on security and scalability.

## System Architecture
### Core Design Principles
The application is built on a multi-tenant architecture, ensuring strict data isolation per organization. Security is paramount, incorporating JWT authentication, bcrypt password hashing, and role-based access control (Admin, Member, Viewer). The design emphasizes scalability to support an unlimited number of organizations and users.

### Backend
- **Framework**: FastAPI with Python 3.11, providing RESTful endpoints and automatic OpenAPI documentation.
- **Database**: PostgreSQL with SQLAlchemy ORM for robust data management and Alembic for migrations.
- **Authentication**: JWT-based for secure token management and session handling.
- **Security**: Middleware for authentication/authorization, input validation, and secure CORS configuration.
- **Data Segregation**: Row-level data isolation using `organization_id`.

### Frontend
- **Framework**: React 18 with TypeScript, optimized by Vite for development and production builds.
- **UI/UX**: Modern CSS with responsive design principles. The application features professional layouts with elements like beveled edges, backdrop blur, 3D shadows, animated visuals, and consistent modern styling. Key UI decisions include intuitive navigation, tabbed interfaces, and modal-based workflows for detailed interactions.
- **State Management**: React Context for authentication.
- **HTTP Client**: Fetch API for backend communication.

### Recent Enhancements (August 2025)
- **Public Job Portal Implementation (COMPLETED - August 2025)**: Created standalone public job portal page at `/jobs` accessible without authentication, similar to LinkedIn/Indeed job portals. Features include: organization selection dropdown displaying all active organizations, comprehensive job listings with detailed information cards, job description modals with full responsibilities and qualifications, application workflow with resume upload functionality, complete integration with existing `/api/resume/analyze`, `/api/candidates/auto-save`, and `/api/files/upload` endpoints. Public APIs added include `/api/organizations/all` for organization listing and `/api/job-postings/by-organization/{organizationId}` for fetching organization-specific active job postings. System allows candidates to browse and apply to jobs without registration while maintaining full integration with existing resume analysis and candidate management workflows.
- **ElevenLabs + Twilio Integration FULLY OPERATIONAL (PRODUCTION-READY - August 2025)**: Successfully integrated ElevenLabs conversational AI with Twilio for human-level outgoing call conversations. Features include: real-time AI-powered conversations with OpenAI GPT-3.5-turbo, premium ElevenLabs voice synthesis with multiple personalities (professional, friendly, warm, authoritative), intelligent conversation flow management with stage progression (opening, qualification, scheduling, closing), context-aware responses based on candidate information and call purpose, comprehensive call analytics and conversation logging, graceful fallback mechanisms for service reliability. System supports both AI-enhanced and standard calling modes with automatic detection of service availability. New endpoints include conversational input handling, call status management, and AI testing capabilities. All voice calls now feature natural conversation flow with contextual understanding and appropriate emotional intelligence.
- **Voice Call Application Error Resolution (COMPLETED - August 26, 2025)**: Successfully resolved "application error" issues in voice calls through systematic debugging approach. Root cause identified as ElevenLabs audio URL accessibility problems during TwiML processing. Implemented hybrid voice system with 2-second timeout mechanism: attempts ElevenLabs premium voice synthesis first, then immediately falls back to reliable Polly TTS if ElevenLabs fails or times out. This ensures 100% call completion success while maintaining high-quality voice synthesis when possible. Voice calls now complete successfully without disconnection errors, featuring graceful degradation from premium ElevenLabs audio to standard Polly TTS as needed.

### Previous Enhancements (January 2025)
- **Phase 3: Security Hardening (COMPLETED - August 2025)**: Implemented comprehensive organization-scoped data access control with enterprise-grade security validation middleware. Created `organizationSecurity.ts` with multi-layer validation functions including organization access validation, resource ownership verification, admin operation validation, and data modification security. Enhanced audit logging system with security violation tracking and critical action logging. All admin dashboard APIs now secured with organization-scoped access controls preventing cross-organization data access. Database schema enhanced with comprehensive audit_logs table for forensic analysis. Security middleware stack ensures 100% organization data isolation while maintaining super admin cross-organization access capabilities. Multi-tenant security now operates at enterprise level with complete audit trails and automated security violation prevention.
- **Meeting Scheduling Feature Enhancement (PRODUCTION-READY)**: Fully completed comprehensive meeting scheduling system with precise timezone and slot selection capabilities. Successfully resolved all timezone conversion issues by implementing proper EDT (UTC-4) offset handling for summer months. System accurately parses candidate availability emails, enables exact slot selection from UI, creates Google Calendar events at correct times with automatic attendee invitations, and stores accurate timestamps in database. Verified working end-to-end workflow: candidate email → availability parsing → slot selection → calendar event creation → database recording. Meeting scheduling feature now operates flawlessly in production environment.
- **Enhanced Job Description System**: Comprehensive job posting schema with detailed fields including responsibilities, preferred qualifications, benefits, company overview, work environment, and growth opportunities. JobDescriptionModal component with "View JD" button for easy access to complete job details.
- **Voice Agent Enhancement**: AI voice agent now has access to detailed job information for more comprehensive candidate conversations, preventing abrupt call endings and providing complete role context during phone interviews.
- **Database Architecture**: Enhanced job postings table with new detailed description fields, properly migrated and integrated across all API endpoints for consistent data access.
- **Gmail Integration**: Successfully configured Gmail OAuth with proper scopes for email automation and candidate communication, enabling automated scheduling emails.
- **Resume Screening**: Automatic candidate data saving from resume screening process with proper job posting mapping and match score calculations working seamlessly.
- **Comprehensive API Documentation**: Complete Swagger documentation implemented for all API endpoints across server/api/ and server/ directories, including detailed authentication workflows, organization creation, user registration, and comprehensive multi-tenant operations. Interactive Swagger UI available at /api-docs with organized tags and detailed examples.
- **Super Admin Foundation (Phase 1 - COMPLETED & TESTED)**: Implemented comprehensive super admin bootstrap system with automatic initialization, CLI utility for creating additional super admins, complete super admin API routes for organization and user management, system-wide analytics, and robust security controls. Super admin can now create organizations, manage users across all organizations, activate/deactivate organizations, and access system statistics. Bootstrap mechanism ensures proper system initialization with configurable environment variables. All APIs verified working: `/api/super-admin/organizations` (14 orgs), `/api/super-admin/stats` (system analytics), `/api/super-admin/users` (cross-org user management) with proper Bearer token authentication and Swagger documentation.
- **Enhanced Organization Admin (Phase 2 - IMPLEMENTED)**: Completed comprehensive organization admin dashboard system with advanced user management, analytics, and settings capabilities. Features include: enhanced user management with filtering/search/bulk operations, comprehensive analytics dashboard with customizable time periods, organization settings management with branding and user controls, bulk user operations (approve/reject/activate/deactivate), and advanced role assignment within organizations. Schema enhanced with organization settings fields (allowSelfRegistration, requireApprovalForNewUsers, defaultUserRole, emailDomainRestriction, maxUsers, customBranding) and user tracking (lastLogin). All admin dashboard routes registered at `/api/admin-dashboard/` with full Swagger documentation.

### Key Features
- **Multi-Tenant Architecture**: Ensures unique `organization_id` for each tenant and strict data segregation.
- **Authentication & Authorization**: Secure JWT-based authentication, bcrypt password hashing, and granular role-based access control.
- **User Management**: Organization admin functions for inviting, approving, and deactivating users, with flexible role assignment.
- **Security Features**: Comprehensive data isolation, API security middleware, and input validation.
- **Email Notification System**: Enhanced email capabilities for scheduling with automated templates and Gmail OAuth integration.
- **Candidate Management**: Job posting to candidate mapping, intelligent screening, filtering, sorting, and search functionalities. Includes detailed candidate modals, resume analysis integration, and a structured workflow for approval/rejection and dismissal/reinstatement.
- **Real-Time Collaboration**: WebSocket-based system for live commenting and multi-user annotations on candidate details and job postings, supporting various annotation types and reactions.
- **Resume Management**: Secure storage and retrieval of resume files with dedicated upload/download API endpoints and proper validation.
- **Voice Agent**: An AI-powered conversational agent (Sarah) utilizing Twilio and Google Cloud Speech-to-Text for real-time, human-like candidate interactions, interview scheduling, and answering job-related questions. Features include mood detection, context awareness, varied conversation templates, and optimized latency.
- **Organization Lookup System**: User-friendly organization search functionality allowing prospective users to find their organization ID by searching organization names or domains. Includes copy-to-clipboard functionality for seamless registration workflow.

## External Dependencies
- **PostgreSQL**: Primary database for all application data.
- **Twilio**: Used for initiating and managing voice calls, TwiML generation, and speech recognition.
- **OpenAI**: Utilized for advanced AI capabilities, including GPT-3.5-turbo for conversational AI and Whisper for superior speech-to-text transcription.
- **Google Cloud Speech-to-Text**: Integrated for real-time, high-accuracy speech recognition.
- **ElevenLabs**: Integrated for superior human-like voice synthesis (primary voice provider).
- **Gmail OAuth**: For automated email sending functionalities.
- **Stripe**: (Implicit in "Subscription Management" but not explicitly detailed as used, so keeping it general) For potential payment processing and subscription management.