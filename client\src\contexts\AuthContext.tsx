import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authAPI } from '../lib/authApi';

interface User {
  id: string;
  email: string;
  fullName: string;
  role: string;
  organizationId: string;
  organizationName: string;
  isActive: boolean;
  isApproved: boolean;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  createOrganization: (data: OrganizationData) => Promise<void>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  clearError: () => void;
}

interface RegisterData {
  email: string;
  fullName: string;
  password: string;
  organizationId: string;
}

interface OrganizationData {
  name: string;
  domain: string;
  adminEmail: string;
  adminName: string;
  adminPassword: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Session validation and refresh
  const validateAndRefreshSession = async () => {
    try {
      setError(null);
      
      // 🎯 DIRECT VALIDATE CALL - Get fresh user data directly from validate endpoint
      const response = await authAPI.makeAuthenticatedRequest('/api/auth/validate');
      
      if (response.ok) {
        const data = await response.json();
        console.log('🔄 Fresh user data from validate:', data);
        
        if (data.valid && data.user) {
          // ✅ SUCCESS - Set fresh user data and authenticate
          setUser(data.user);
          setIsAuthenticated(true);
          setError(null);
          console.log('✅ User state updated:', data.user);
        } else {
          throw new Error('Invalid session response');
        }
      } else {
        // API call failed - clear everything
        throw new Error('Session validation failed');
      }
    } catch (error) {
      console.error('Session validation error:', error);
      // 🎯 CLEAR SESSION ON ANY ERROR
      authAPI.clearTokensFromStorage();
      setUser(null);
      setIsAuthenticated(false);
      setError('Session expired. Please log in again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Initialize auth state on mount
  useEffect(() => {
    // 🎯 ADD SMALL DELAY TO ENSURE TOKENS ARE LOADED
    const initializeAuth = async () => {
      // Give a small delay to ensure localStorage is ready
      await new Promise(resolve => setTimeout(resolve, 100));
      console.log('🔄 Starting session validation...');
      validateAndRefreshSession();
    };
    
    initializeAuth();
  }, []);

  // Set up periodic session refresh (every 10 minutes)
  useEffect(() => {
    if (!isAuthenticated) return;

    const interval = setInterval(async () => {
      console.log('Periodic validation check...');
      const isValid = await authAPI.validateSession();
      if (!isValid) {
        console.log('Periodic validation failed, refreshing session...');
        await validateAndRefreshSession();
      }
    }, 10 * 60 * 1000); // 10 minutes

    return () => clearInterval(interval);
  }, [isAuthenticated]);

  // Activity-based session extension
  useEffect(() => {
    if (!isAuthenticated) return;

    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    let lastActivity = Date.now();

    const handleActivity = () => {
      const now = Date.now();
      // Only extend session if more than 5 minutes have passed since last activity
      if (now - lastActivity > 5 * 60 * 1000) {
        lastActivity = now;
        // Make a lightweight request to extend session - but handle token expiry gracefully  
        authAPI.makeAuthenticatedRequest('/api/auth/validate')
          .then(async (response) => {
            // 🎯 UPDATE USER STATE FROM ACTIVITY-BASED VALIDATION
            if (response.ok) {
              const data = await response.json();
              if (data.valid && data.user) {
                setUser(data.user); // Keep user data fresh
              }
            }
          })
          .catch(async (error) => {
            // 🎯 IMPROVED ERROR HANDLING - check error.status properly
            if (error?.status === 401 || error?.name === 'AuthError') {
              try {
                await authAPI.refreshAccessToken();
                // After successful refresh, try validation again silently
                await authAPI.makeAuthenticatedRequest('/api/auth/validate');
              } catch (refreshError) {
                // If refresh fails during activity, trigger full re-validation
                console.log('Activity-based refresh failed, triggering re-validation');
                await validateAndRefreshSession();
              }
            }
            // Ignore all other errors for activity-based requests
          });
      }
    };

    activityEvents.forEach(event => {
      document.addEventListener(event, handleActivity, true);
    });

    return () => {
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleActivity, true);
      });
    };
  }, [isAuthenticated]);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await authAPI.login({ email, password });
      // Map API response to expected User interface
      const user = {
        id: response.user.id,
        email: response.user.email,
        fullName: response.user.full_name,
        role: response.user.role,
        organizationId: response.user.organization_id,
        organizationName: response.user.organization_name,
        isActive: response.user.is_active,
        isApproved: response.user.is_approved
      };
      setUser(user);
      setIsAuthenticated(true);
      setError(null);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setError(errorMessage);
      setUser(null);
      setIsAuthenticated(false);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email: data.email,
          fullName: data.fullName,
          password: data.password,
          organizationId: data.organizationId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || 'Registration failed');
      }

      const responseData = await response.json();
      
      // If registration returns tokens, store them
      if (responseData.access_token && responseData.refresh_token) {
        localStorage.setItem('access_token', responseData.access_token);
        localStorage.setItem('refresh_token', responseData.refresh_token);
        setUser(responseData.user);
        setIsAuthenticated(true);
      }
      
      setError(null);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration failed';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const createOrganization = async (data: OrganizationData) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/auth/organizations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          name: data.name,
          domain: data.domain,
          adminEmail: data.adminEmail,
          adminName: data.adminName,
          adminPassword: data.adminPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || 'Organization creation failed');
      }

      const responseData = await response.json();
      
      // If organization creation returns tokens, store them
      if (responseData.access_token && responseData.refresh_token) {
        localStorage.setItem('access_token', responseData.access_token);
        localStorage.setItem('refresh_token', responseData.refresh_token);
        setUser(responseData.user);
        setIsAuthenticated(true);
      }
      
      setError(null);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Organization creation failed';
      setError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      setError(null);
      setIsLoading(false);
    }
  };

  const refreshSession = async () => {
    await validateAndRefreshSession();
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    createOrganization,
    logout,
    refreshSession,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}