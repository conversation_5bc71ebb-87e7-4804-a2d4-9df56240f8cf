
import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js worker with a reliable source
const initializePdfJs = () => {
  // Use a more reliable worker source
  try {
    pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
    console.log(`PDF.js worker set to: https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`);
  } catch (error) {
    console.warn('Failed to set PDF.js worker:', error);
  }
};

// Initialize PDF.js on module load
initializePdfJs();

// Remove null bytes and clean text
const cleanText = (text: string): string => {
  return text
    .replace(/\0/g, '') // Remove null bytes
    .replace(/[\x00-\x1F\x7F-\x9F]/g, ' ') // Remove control characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
};

// Enhanced contact information extraction with better patterns
const extractContactInfo = (text: string): { 
  email?: string; 
  name?: string; 
  phone?: string; 
  location?: string; 
  linkedin?: string 
} => {
  const info: { email?: string; name?: string; phone?: string; location?: string; linkedin?: string } = {};
  
  // Clean the text first
  const cleanedText = cleanText(text);
  
  // Extract email with improved regex
  const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/gi;
  const emails = cleanedText.match(emailRegex);
  if (emails && emails.length > 0) {
    info.email = emails[0].toLowerCase();
  }
  
  // Extract phone number with multiple patterns
  const phonePatterns = [
    /(\+?1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}/g,
    /\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b/g,
    /\b\(\d{3}\)\s*\d{3}-\d{4}\b/g
  ];
  
  for (const pattern of phonePatterns) {
    const phones = cleanedText.match(pattern);
    if (phones && phones.length > 0) {
      const validPhone = phones.find(phone => {
        const digits = phone.replace(/\D/g, '');
        return digits.length >= 10 && digits.length <= 11;
      });
      if (validPhone) {
        info.phone = validPhone.trim();
        break;
      }
    }
  }
  
  // Extract LinkedIn profile
  const linkedinRegex = /(?:https?:\/\/)?(?:www\.)?linkedin\.com\/in\/[a-zA-Z0-9-]+/gi;
  const linkedinMatch = cleanedText.match(linkedinRegex);
  if (linkedinMatch) {
    info.linkedin = linkedinMatch[0];
  }
  
  // Extract location with improved patterns
  const locationPatterns = [
    /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*,\s*[A-Z]{2}(?:\s+\d{5})?)/g,
    /([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*),\s*([A-Z][a-z]+)/g
  ];
  
  for (const pattern of locationPatterns) {
    const locationMatch = cleanedText.match(pattern);
    if (locationMatch && locationMatch[0]) {
      const cleaned = locationMatch[0].trim();
      if (cleaned.length > 3 && cleaned.length < 100) {
        info.location = cleaned;
        break;
      }
    }
  }
  
  // Extract name from the beginning of the document
  const lines = cleanedText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
  
  // Look for name in first 5 lines
  for (let i = 0; i < Math.min(lines.length, 5); i++) {
    const line = lines[i];
    
    // Skip lines that are too long or too short
    if (line.length > 50 || line.length < 4) continue;
    
    // Skip lines with common resume keywords
    const skipKeywords = [
      'resume', 'cv', 'curriculum', 'vitae', 'experience', 'education', 
      'skills', 'objective', 'summary', 'phone', 'email', 'address',
      'linkedin', 'github', 'portfolio', 'website', '@', 'http', 'www'
    ];
    
    const lowerLine = line.toLowerCase();
    if (skipKeywords.some(keyword => lowerLine.includes(keyword))) continue;
    
    // Skip lines with numbers or special characters
    if (/[0-9@#$%^&*()_+=\[\]{};':"\\|,<>\/?]/.test(line)) continue;
    
    // Check if line looks like a name (2-4 words, each starting with capital)
    const words = line.split(/\s+/);
    if (words.length >= 2 && words.length <= 4) {
      const isName = words.every(word => 
        /^[A-Z][a-z]*$/.test(word) || 
        /^[A-Z]\.$/.test(word) || // Middle initial
        /^(de|van|von|della|di|da|del|la|le|jr|sr|ii|iii|iv)$/i.test(word)
      );
      
      if (isName) {
        info.name = line;
        break;
      }
    }
  }
  
  return info;
};

// Improved PDF parsing with better error handling and fallbacks
export const extractTextFromPDF = async (file: File): Promise<{ 
  text: string; 
  email?: string; 
  name?: string; 
  phone?: string;
  location?: string;
  linkedin?: string;
  parseMethod: string;
}> => {
  console.log("Starting PDF text extraction for:", file.name);
  
  try {
    const arrayBuffer = await file.arrayBuffer();
    
    // Try with simpler configuration first
    const loadingTask = pdfjsLib.getDocument({
      data: arrayBuffer,
      verbosity: 0
    });
    
    const pdf = await loadingTask.promise;
    console.log(`PDF loaded successfully. Processing ${pdf.numPages} pages`);
    
    let fullText = '';
    
    // Extract text from all pages (limit to first 10 for performance)
    const maxPages = Math.min(pdf.numPages, 10);
    
    for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent();
        
        // Extract text items and sort by position
        const items = textContent.items as any[];
        
        // Sort by Y position (top to bottom), then X position (left to right)
        items.sort((a, b) => {
          const yDiff = b.transform[5] - a.transform[5];
          if (Math.abs(yDiff) > 2) return yDiff;
          return a.transform[4] - b.transform[4];
        });
        
        let pageText = '';
        let lastY = 0;
        
        items.forEach((item) => {
          if (item.str) {
            const currentY = item.transform[5];
            
            // Add line break if Y position changed significantly
            if (lastY !== 0 && Math.abs(currentY - lastY) > 5) {
              pageText += '\n';
            }
            
            pageText += item.str + ' ';
            lastY = currentY;
          }
        });
        
        fullText += pageText + '\n\n';
        
      } catch (pageError) {
        console.warn(`Failed to extract text from page ${pageNum}:`, pageError);
      }
    }
    
    // Clean the extracted text
    const cleanedText = cleanText(fullText);
    console.log(`Extracted ${cleanedText.length} characters from PDF`);
    
    if (cleanedText.length > 100) {
      const contactInfo = extractContactInfo(cleanedText);
      console.log("Extracted contact info:", contactInfo);
      
      return {
        text: cleanedText,
        ...contactInfo,
        parseMethod: 'PDF.js Enhanced'
      };
    } else {
      // If we got very little text, try to extract whatever we can
      const contactInfo = extractContactInfo(cleanedText);
      
      return {
        text: cleanedText || `Minimal text extracted from PDF: ${file.name}. This may be a scanned document or have formatting issues.`,
        ...contactInfo,
        parseMethod: 'PDF.js Minimal'
      };
    }
    
  } catch (error) {
    console.error('PDF parsing failed:', error);
    
    // Return a structured error with the filename for AI processing
    const errorText = `PDF parsing failed for: ${file.name}

This appears to be a PDF that cannot be processed by the current parser. This could be due to:
- Scanned image PDF without text layer
- Encrypted or password-protected PDF
- Corrupted PDF file
- Unsupported PDF format

Please try a different PDF file or convert this document to a text-readable format.

Error: ${error.message}`;

    return {
      text: errorText,
      parseMethod: 'PDF Parse Error'
    };
  }
};
