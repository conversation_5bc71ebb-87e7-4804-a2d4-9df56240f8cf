import { db } from './db';
import { authUsers } from '@shared/schema';
import { eq } from 'drizzle-orm';
import { hashPassword } from './auth';

/**
 * Bootstrap script to create the initial super admin user
 * This should be run once during initial system setup
 */
export async function bootstrapSuperAdmin() {
  const superAdminEmail = process.env.SUPER_ADMIN_EMAIL || '<EMAIL>';
  const superAdminPassword = process.env.SUPER_ADMIN_PASSWORD || 'SuperAdmin123!';
  const superAdminName = process.env.SUPER_ADMIN_NAME || 'System Administrator';

  console.log('🔧 Checking for existing super admin...');

  // Check if super admin already exists
  const existingSuperAdmin = await db
    .select()
    .from(authUsers)
    .where(eq(authUsers.role, 'super_admin'))
    .limit(1);

  if (existingSuperAdmin.length > 0) {
    console.log('✅ Super admin already exists:', existingSuperAdmin[0].email);
    return existingSuperAdmin[0];
  }

  console.log('🚀 Creating initial super admin user...');

  // Create super admin user
  const hashedPassword = hashPassword(superAdminPassword);
  const superAdmin = await db
    .insert(authUsers)
    .values({
      email: superAdminEmail,
      fullName: superAdminName,
      hashedPassword,
      role: 'super_admin',
      organizationId: null, // Super admin is not tied to any organization
      isActive: true,
      isApproved: true, // Super admin is auto-approved
    })
    .returning();

  console.log('✅ Super admin created successfully:', superAdmin[0].email);
  console.log('📧 Email:', superAdminEmail);
  console.log('🔑 Password:', superAdminPassword);
  console.log('⚠️  IMPORTANT: Change the default password after first login!');

  return superAdmin[0];
}

/**
 * Check if system has been bootstrapped with super admin
 */
export async function isSuperAdminBootstrapped(): Promise<boolean> {
  const superAdminCount = await db
    .select()
    .from(authUsers)
    .where(eq(authUsers.role, 'super_admin'));
  
  return superAdminCount.length > 0;
}

/**
 * One-time setup endpoint for creating super admin
 * Should be protected and only callable once
 */
export async function setupInitialSuperAdmin(email: string, password: string, fullName: string) {
  // Check if already bootstrapped
  if (await isSuperAdminBootstrapped()) {
    throw new Error('System already has a super admin. Cannot create another through setup.');
  }

  // Validate input
  if (!email || !password || password.length < 8) {
    throw new Error('Invalid email or password (minimum 8 characters required)');
  }

  // Create super admin
  const hashedPassword = hashPassword(password);
  const superAdmin = await db
    .insert(authUsers)
    .values({
      email,
      fullName,
      hashedPassword,
      role: 'super_admin',
      organizationId: null,
      isActive: true,
      isApproved: true,
    })
    .returning();

  return superAdmin[0];
}