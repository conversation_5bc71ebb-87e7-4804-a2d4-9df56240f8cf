<html>
  <head>
    <title>Job Listings Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"></link>
    <style>
      body {
        font-family: 'Inter', sans-serif;
      }
      .custom-scrollbar::-webkit-scrollbar {
        width: 8px;
        background: transparent;
      }
      .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #e5e7eb;
        border-radius: 8px;
      }
    </style>
  </head>
  <body class="bg-[#f7f7fa] min-h-screen">
    <div class="flex min-h-screen">
      <!-- Sidebar -->
      <aside class="w-[320px] bg-white border-r border-[#e5e7eb] py-8 px-8">
        <div class="flex items-center mb-8">
          <button class="flex items-center px-4 py-2 rounded-full border border-[#e5e7eb] text-[#6b7280] text-base font-medium focus:outline-none">
            <i class="fas fa-filter mr-2"></i>
            Filter
          </button>
        </div>
        <div>
          <div class="mb-6">
            <h3 class="text-[#22223b] text-base font-semibold mb-3">Category</h3>
            <div class="flex flex-col gap-3">
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Marketing
              </label>
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Engineering
              </label>
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Product
              </label>
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Operations
              </label>
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Sales
              </label>
            </div>
          </div>
          <div class="mb-6">
            <h3 class="text-[#22223b] text-base font-semibold mb-3">Location</h3>
            <div class="flex flex-col gap-3">
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Toronto, Canada
              </label>
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Newyork, USA
              </label>
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Lead, UK
              </label>
            </div>
          </div>
          <div>
            <h3 class="text-[#22223b] text-base font-semibold mb-3">Job Type</h3>
            <div class="flex flex-col gap-3">
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Full-Time
              </label>
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Part-Time
              </label>
              <label class="flex items-center text-[#6b7280] text-base font-normal cursor-pointer">
                <input type="checkbox" class="form-checkbox h-5 w-5 rounded border-[#e5e7eb] mr-3" />
                Contractor
              </label>
            </div>
          </div>
        </div>
      </aside>
      <!-- Main Content -->
      <main class="flex-1 px-8 py-8 overflow-x-auto custom-scrollbar">
        <!-- Tabs -->
        <div class="flex items-center border-b border-[#e5e7eb] mb-6">
          <button class="px-4 pb-3 text-[#5f3dc4] text-base font-semibold border-b-2 border-[#5f3dc4] focus:outline-none">All</button>
          <button class="px-4 pb-3 text-[#6b7280] text-base font-medium focus:outline-none">Open</button>
          <button class="px-4 pb-3 text-[#6b7280] text-base font-medium focus:outline-none">Hold</button>
          <button class="px-4 pb-3 text-[#6b7280] text-base font-medium focus:outline-none">Closed</button>
          <button class="px-4 pb-3 text-[#6b7280] text-base font-medium focus:outline-none">Drafts <span class="ml-1 text-xs text-[#6b7280]">(2)</span></button>
        </div>
        <!-- Table Header -->
        <div class="grid grid-cols-5 gap-4 px-2 py-2 text-[#6b7280] text-sm font-semibold">
          <div>Job Title</div>
          <div>Category</div>
          <div>Status</div>
          <div>Salary</div>
          <div>Location</div>
        </div>
        <!-- Job List -->
        <div class="flex flex-col gap-4 mt-2">
          <!-- Row 1 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Personal Assistant</div>
            <div class="text-[#6b7280] font-normal">Administrative</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#a5b4fc] bg-[#f5f3ff] text-[#5f3dc4] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#5f3dc4] mr-2"></span>
                Open
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> Canada
            </div>
          </div>
          <!-- Row 2 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Junior HR Manager</div>
            <div class="text-[#6b7280] font-normal">Administrative</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#a5b4fc] bg-[#f5f3ff] text-[#5f3dc4] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#5f3dc4] mr-2"></span>
                Open
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> India
            </div>
          </div>
          <!-- Row 3 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Senior Product Designer</div>
            <div class="text-[#6b7280] font-normal">Product</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#fde68a] bg-[#fef9c3] text-[#eab308] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#eab308] mr-2"></span>
                Hold
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> USA
            </div>
          </div>
          <!-- Row 4 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Associate Product Designer</div>
            <div class="text-[#6b7280] font-normal">Marketing</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#fca5a5] bg-[#fef2f2] text-[#ef4444] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#ef4444] mr-2"></span>
                Closed
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> UK
            </div>
          </div>
          <!-- Row 5 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Software Developer</div>
            <div class="text-[#6b7280] font-normal">Marketing</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#fca5a5] bg-[#fef2f2] text-[#ef4444] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#ef4444] mr-2"></span>
                Closed
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> UK
            </div>
          </div>
          <!-- Row 6 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Customer Success Manager</div>
            <div class="text-[#6b7280] font-normal">Administrative</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#a5b4fc] bg-[#f5f3ff] text-[#5f3dc4] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#5f3dc4] mr-2"></span>
                Open
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> India
            </div>
          </div>
          <!-- Row 7 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Head of Design</div>
            <div class="text-[#6b7280] font-normal">Sales</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#fde68a] bg-[#fef9c3] text-[#eab308] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#eab308] mr-2"></span>
                Hold
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> USA
            </div>
          </div>
          <!-- Row 8 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Marketing Analyser</div>
            <div class="text-[#6b7280] font-normal">Administrative</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#a5b4fc] bg-[#f5f3ff] text-[#5f3dc4] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#5f3dc4] mr-2"></span>
                Open
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> Canada
            </div>
          </div>
          <!-- Row 9 -->
          <div class="grid grid-cols-5 gap-4 bg-white rounded-xl px-6 py-5 items-center text-[#22223b] text-base font-medium shadow-sm">
            <div>Personal Assistant</div>
            <div class="text-[#6b7280] font-normal">Administrative</div>
            <div>
              <span class="inline-flex items-center px-3 py-1 rounded-full border border-[#a5b4fc] bg-[#f5f3ff] text-[#5f3dc4] text-sm font-semibold">
                <span class="w-2 h-2 rounded-full bg-[#5f3dc4] mr-2"></span>
                Open
              </span>
            </div>
            <div class="text-[#6b7280] font-normal">$80K - $100K</div>
            <div class="flex items-center text-[#6b7280] font-normal">
              <i class="fas fa-map-marker-alt mr-2"></i> Canada
            </div>
          </div>
        </div>
      </main>
    </div>
  </body>
</html>