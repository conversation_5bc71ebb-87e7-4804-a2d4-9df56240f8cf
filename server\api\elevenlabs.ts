import { Router } from 'express';
import { authenticateToken } from '../auth';
import { elevenLabsService } from '../services/elevenlabsService';
import { db } from '../db';
import { interviewsV2, agentProfiles, authUsers } from '@shared/schema';
import { eq, and } from 'drizzle-orm';

const router = Router();

/**
 * @swagger
 * /elevenlabs/signed-url:
 *   post:
 *     summary: Get ElevenLabs signed URL for conversation
 *     description: Create a signed URL for ElevenLabs Conversational AI
 *     tags: [ElevenLabs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - agentId
 *             properties:
 *               agentId:
 *                 type: string
 *                 description: ElevenLabs agent ID
 *     responses:
 *       200:
 *         description: Signed URL created successfully
 *       400:
 *         description: Invalid request
 *       500:
 *         description: Failed to create signed URL
 */
router.post('/signed-url', authenticateToken, async (req, res) => {
  try {
    const { agentId } = req.body;

    if (!agentId) {
      return res.status(400).json({
        success: false,
        error: 'Agent ID is required'
      });
    }

    const signedUrl = await elevenLabsService.createConversationSignedUrl(agentId);

    res.json({
      success: true,
      signed_url: signedUrl
    });

  } catch (error) {
    console.error('Error creating ElevenLabs signed URL:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create signed URL'
    });
  }
});

/**
 * @swagger
 * /elevenlabs/conversation/start:
 *   post:
 *     summary: Start ElevenLabs conversation
 *     description: Start a new conversation session with ElevenLabs
 *     tags: [ElevenLabs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *               - interviewId
 *             properties:
 *               sessionId:
 *                 type: string
 *               interviewId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Conversation started successfully
 *       400:
 *         description: Invalid request
 *       404:
 *         description: Interview not found
 *       500:
 *         description: Failed to start conversation
 */
router.post('/conversation/start', authenticateToken, async (req, res) => {
  try {
    const { sessionId, interviewId } = req.body;
    const userId = req.user?.id;

    if (!sessionId || !interviewId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID and Interview ID are required'
      });
    }

    // Get user's organization
    const [user] = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.id, userId))
      .limit(1);

    if (!user || !user.organizationId) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }

    // Get interview with agent profile
    const [interview] = await db
      .select({
        interview: interviewsV2,
        agentProfile: agentProfiles
      })
      .from(interviewsV2)
      .leftJoin(agentProfiles, eq(interviewsV2.agentProfileId, agentProfiles.id))
      .where(and(
        eq(interviewsV2.id, interviewId),
        eq(interviewsV2.organizationId, user.organizationId)
      ))
      .limit(1);

    if (!interview) {
      return res.status(404).json({
        success: false,
        error: 'Interview not found'
      });
    }

    // Get agent configuration
    const agentId = process.env.ELEVENLABS_AGENT_ID || 'default-agent-id';
    const conversationConfig = {
      agentId,
      conversationConfig: {
        agent: {
          prompt: {
            prompt: interview.agentProfile?.promptTemplate || 
              `You are conducting a professional interview for the ${interview.interview.role} position. 
               Be friendly, professional, and ask relevant technical and behavioral questions. 
               Adapt your questions based on the candidate's responses and experience level.`
          }
        }
      }
    };

    // Start conversation
    const conversationSessionId = await elevenLabsService.startConversation(sessionId, conversationConfig);

    res.json({
      success: true,
      conversationSessionId,
      message: 'Conversation started successfully'
    });

  } catch (error) {
    console.error('Error starting ElevenLabs conversation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start conversation'
    });
  }
});

/**
 * @swagger
 * /elevenlabs/conversation/end:
 *   post:
 *     summary: End ElevenLabs conversation
 *     description: End an active conversation session
 *     tags: [ElevenLabs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *             properties:
 *               sessionId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Conversation ended successfully
 *       404:
 *         description: Session not found
 *       500:
 *         description: Failed to end conversation
 */
router.post('/conversation/end', authenticateToken, async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      });
    }

    const status = elevenLabsService.getConversationStatus(sessionId);
    if (!status) {
      return res.status(404).json({
        success: false,
        error: 'Conversation session not found'
      });
    }

    await elevenLabsService.endConversation(sessionId);

    res.json({
      success: true,
      message: 'Conversation ended successfully'
    });

  } catch (error) {
    console.error('Error ending ElevenLabs conversation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to end conversation'
    });
  }
});

/**
 * @swagger
 * /elevenlabs/conversation/status:
 *   get:
 *     summary: Get conversation status
 *     description: Get the status of active conversations
 *     tags: [ElevenLabs]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Status retrieved successfully
 */
router.get('/conversation/status', authenticateToken, async (req, res) => {
  try {
    const activeSessions = elevenLabsService.getActiveConversations();

    const sessionStatuses = activeSessions.map(sessionId => {
      const status = elevenLabsService.getConversationStatus(sessionId);
      return {
        sessionId,
        ...status
      };
    });

    res.json({
      success: true,
      activeSessions: sessionStatuses
    });

  } catch (error) {
    console.error('Error getting conversation status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conversation status'
    });
  }
});

/**
 * @swagger
 * /elevenlabs/conversation/{sessionId}/status:
 *   get:
 *     summary: Get specific conversation status
 *     description: Get the status of a specific conversation session
 *     tags: [ElevenLabs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Status retrieved successfully
 *       404:
 *         description: Session not found
 */
router.get('/conversation/:sessionId/status', authenticateToken, async (req, res) => {
  try {
    const { sessionId } = req.params;

    const status = elevenLabsService.getConversationStatus(sessionId);
    
    if (!status) {
      return res.status(404).json({
        success: false,
        error: 'Conversation session not found'
      });
    }

    res.json({
      success: true,
      sessionId,
      ...status
    });

  } catch (error) {
    console.error('Error getting conversation status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conversation status'
    });
  }
});

export default router;
