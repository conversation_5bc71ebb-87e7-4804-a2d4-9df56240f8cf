<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Bot Client</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            font-family: Arial, sans-serif;
        }
        
        #zoom-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        #status {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            z-index: 1000;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div id="status">Initializing bot...</div>
    <div id="zoom-container"></div>

    <!-- Zoom Video SDK -->
    <script src="https://source.zoom.us/videosdk/1.11.0/lib/av/index.js"></script>
    
    <script>
        class InterviewBot {
            constructor() {
                this.client = null;
                this.stream = null;
                this.isConnected = false;
                this.audioContext = null;
                this.elevenlabsWs = null;
                this.agentConfig = null;
                this.recordingStream = null;
                
                this.init();
            }

            async init() {
                try {
                    this.updateStatus('Parsing URL parameters...');
                    const urlParams = new URLSearchParams(window.location.search);
                    const token = urlParams.get('token');
                    const sessionName = urlParams.get('sessionName');

                    if (!token || !sessionName) {
                        throw new Error('Missing token or sessionName parameters');
                    }

                    this.updateStatus('Initializing Zoom Video SDK...');
                    await this.initializeZoomSDK(token, sessionName);
                    
                } catch (error) {
                    console.error('Bot initialization error:', error);
                    this.updateStatus(`Error: ${error.message}`);
                }
            }

            async initializeZoomSDK(token, sessionName) {
                try {
                    // Initialize Zoom Video SDK
                    this.client = ZoomVideo.createClient();
                    
                    // Set up event listeners
                    this.client.on('connection-change', (payload) => {
                        console.log('Connection change:', payload);
                        if (payload.state === 'Connected') {
                            this.isConnected = true;
                            this.updateStatus('Connected to Zoom session');
                            this.setupAudioCapture();
                        }
                    });

                    this.client.on('user-added', (payload) => {
                        console.log('User added:', payload);
                        this.handleParticipantJoined(payload);
                    });

                    this.client.on('user-removed', (payload) => {
                        console.log('User removed:', payload);
                        this.handleParticipantLeft(payload);
                    });

                    // Join session
                    this.updateStatus('Joining Zoom session...');
                    await this.client.join(sessionName, token, 'InterviewBot');
                    
                    // Start video and audio
                    this.stream = this.client.getMediaStream();
                    await this.stream.startAudio();
                    
                    this.updateStatus('Bot connected successfully');
                    
                } catch (error) {
                    console.error('Zoom SDK initialization error:', error);
                    throw error;
                }
            }

            async setupAudioCapture() {
                try {
                    this.updateStatus('Setting up audio capture...');
                    
                    // Initialize Web Audio API
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    
                    // Get audio stream from Zoom
                    const audioTrack = this.stream.getAudioTrack();
                    if (audioTrack) {
                        const mediaStream = new MediaStream([audioTrack]);
                        const source = this.audioContext.createMediaStreamSource(mediaStream);
                        
                        // Create audio processor for ElevenLabs
                        const processor = this.audioContext.createScriptProcessor(4096, 1, 1);
                        processor.onaudioprocess = (event) => {
                            this.processAudioForElevenLabs(event.inputBuffer);
                        };
                        
                        source.connect(processor);
                        processor.connect(this.audioContext.destination);
                    }
                    
                    this.updateStatus('Audio capture ready');
                    
                } catch (error) {
                    console.error('Audio setup error:', error);
                    this.updateStatus(`Audio setup error: ${error.message}`);
                }
            }

            processAudioForElevenLabs(audioBuffer) {
                if (!this.elevenlabsWs || this.elevenlabsWs.readyState !== WebSocket.OPEN) {
                    return;
                }

                try {
                    // Convert audio buffer to format expected by ElevenLabs
                    const channelData = audioBuffer.getChannelData(0);

                    // Apply noise reduction and normalization
                    const normalizedData = this.normalizeAudio(channelData);

                    // Convert to PCM 16-bit
                    const pcmData = new Int16Array(normalizedData.length);
                    for (let i = 0; i < normalizedData.length; i++) {
                        pcmData[i] = Math.max(-32768, Math.min(32767, normalizedData[i] * 32768));
                    }

                    // Send to ElevenLabs
                    const audioMessage = {
                        user_audio_chunk: this.arrayBufferToBase64(pcmData.buffer)
                    };

                    this.elevenlabsWs.send(JSON.stringify(audioMessage));

                } catch (error) {
                    console.error('Audio processing error:', error);
                }
            }

            normalizeAudio(channelData) {
                // Simple audio normalization
                let max = 0;
                for (let i = 0; i < channelData.length; i++) {
                    max = Math.max(max, Math.abs(channelData[i]));
                }

                if (max === 0) return channelData;

                const normalized = new Float32Array(channelData.length);
                const scale = 0.8 / max; // Normalize to 80% to avoid clipping

                for (let i = 0; i < channelData.length; i++) {
                    normalized[i] = channelData[i] * scale;
                }

                return normalized;
            }

            async initializeElevenLabsBridge(agentConfig) {
                try {
                    this.agentConfig = agentConfig;
                    this.updateStatus('Connecting to ElevenLabs...');

                    // Get ElevenLabs signed URL
                    const response = await fetch('/api/elevenlabs/signed-url', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${this.getAuthToken()}`
                        },
                        body: JSON.stringify({
                            agentId: agentConfig?.agentId || process.env.ELEVENLABS_AGENT_ID || 'default-agent-id'
                        })
                    });

                    if (!response.ok) {
                        throw new Error('Failed to get ElevenLabs signed URL');
                    }

                    const { signed_url } = await response.json();

                    // Connect to ElevenLabs
                    this.elevenlabsWs = new WebSocket(signed_url);
                    
                    this.elevenlabsWs.onopen = () => {
                        console.log('Connected to ElevenLabs');
                        this.updateStatus('ElevenLabs connected');
                        
                        // Send initial configuration
                        const initMessage = {
                            type: 'conversation_initiation_client_data',
                            conversation_initiation_client_data: {
                                conversation_config_override: {
                                    agent: {
                                        prompt: {
                                            prompt: agentConfig?.promptTemplate || 'You are an AI interviewer.'
                                        }
                                    }
                                }
                            }
                        };
                        
                        this.elevenlabsWs.send(JSON.stringify(initMessage));
                    };
                    
                    this.elevenlabsWs.onmessage = (event) => {
                        this.handleElevenLabsMessage(JSON.parse(event.data));
                    };
                    
                    this.elevenlabsWs.onerror = (error) => {
                        console.error('ElevenLabs WebSocket error:', error);
                        this.updateStatus('ElevenLabs connection error');
                    };
                    
                } catch (error) {
                    console.error('ElevenLabs bridge initialization error:', error);
                    this.updateStatus(`ElevenLabs error: ${error.message}`);
                }
            }

            handleElevenLabsMessage(message) {
                try {
                    if (message.type === 'agent_response_audio_chunk') {
                        // Play agent audio through Zoom
                        this.playAgentAudio(message.audio_chunk);
                    } else if (message.type === 'conversation_end') {
                        console.log('Interview conversation ended');
                        this.updateStatus('Interview completed');
                        this.endSession();
                    }
                } catch (error) {
                    console.error('Error handling ElevenLabs message:', error);
                }
            }

            async playAgentAudio(audioChunk) {
                try {
                    if (!this.audioContext) return;

                    // Decode base64 audio
                    const audioData = this.base64ToArrayBuffer(audioChunk);
                    const audioBuffer = await this.audioContext.decodeAudioData(audioData);

                    // Create gain node for volume control
                    const gainNode = this.audioContext.createGain();
                    gainNode.gain.value = 0.8; // Slightly lower volume to avoid feedback

                    // Play through Zoom audio stream
                    const source = this.audioContext.createBufferSource();
                    source.buffer = audioBuffer;
                    source.connect(gainNode);
                    gainNode.connect(this.audioContext.destination);

                    // Also inject into Zoom's audio stream if available
                    if (this.stream && this.stream.startShareAudio) {
                        try {
                            // Create a media stream from the audio buffer
                            const mediaStreamDestination = this.audioContext.createMediaStreamDestination();
                            gainNode.connect(mediaStreamDestination);

                            // Share the audio with Zoom participants
                            await this.stream.startShareAudio(mediaStreamDestination.stream);
                        } catch (shareError) {
                            console.warn('Could not share audio with Zoom participants:', shareError);
                        }
                    }

                    source.start();

                } catch (error) {
                    console.error('Error playing agent audio:', error);
                }
            }

            handleParticipantJoined(participant) {
                console.log('Participant joined:', participant);
                this.updateStatus(`Participant joined: ${participant.displayName}`);
                
                // Start interview when candidate joins
                if (this.elevenlabsWs && this.elevenlabsWs.readyState === WebSocket.OPEN) {
                    const startMessage = {
                        type: 'conversation_start',
                        participant_name: participant.displayName
                    };
                    this.elevenlabsWs.send(JSON.stringify(startMessage));
                }
            }

            handleParticipantLeft(participant) {
                console.log('Participant left:', participant);
                this.updateStatus(`Participant left: ${participant.displayName}`);
            }

            async endSession() {
                try {
                    this.updateStatus('Ending session...');
                    
                    // Close ElevenLabs connection
                    if (this.elevenlabsWs) {
                        this.elevenlabsWs.close();
                    }
                    
                    // Leave Zoom session
                    if (this.client && this.isConnected) {
                        await this.client.leave();
                    }
                    
                    this.updateStatus('Session ended');
                    
                } catch (error) {
                    console.error('Error ending session:', error);
                }
            }

            updateStatus(message) {
                const statusEl = document.getElementById('status');
                if (statusEl) {
                    statusEl.textContent = `Bot Status: ${message}`;
                }
                console.log(`Bot Status: ${message}`);
            }

            // Utility functions
            getAuthToken() {
                // For bot client, we might need to pass token via URL params or environment
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('authToken') || localStorage.getItem('auth_token') || '';
            }

            arrayBufferToBase64(buffer) {
                const bytes = new Uint8Array(buffer);
                let binary = '';
                for (let i = 0; i < bytes.byteLength; i++) {
                    binary += String.fromCharCode(bytes[i]);
                }
                return btoa(binary);
            }

            base64ToArrayBuffer(base64) {
                const binaryString = atob(base64);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                return bytes.buffer;
            }

            // Audio quality monitoring
            monitorAudioQuality() {
                if (!this.audioContext) return;

                const analyser = this.audioContext.createAnalyser();
                analyser.fftSize = 256;

                const dataArray = new Uint8Array(analyser.frequencyBinCount);

                const checkQuality = () => {
                    analyser.getByteFrequencyData(dataArray);

                    // Calculate average volume
                    let sum = 0;
                    for (let i = 0; i < dataArray.length; i++) {
                        sum += dataArray[i];
                    }
                    const average = sum / dataArray.length;

                    // Log quality metrics periodically
                    if (Math.random() < 0.01) { // 1% chance to log
                        console.log('Audio quality - Average level:', average);
                    }

                    requestAnimationFrame(checkQuality);
                };

                checkQuality();
            }
        }

        // Global functions for Puppeteer control
        window.initializeElevenLabsBridge = function(agentConfig) {
            if (window.interviewBot) {
                window.interviewBot.initializeElevenLabsBridge(agentConfig);
            }
        };

        window.leaveZoomSession = function() {
            if (window.interviewBot) {
                window.interviewBot.endSession();
            }
        };

        // Initialize bot when page loads
        window.addEventListener('load', () => {
            window.interviewBot = new InterviewBot();
        });
    </script>
</body>
</html>
