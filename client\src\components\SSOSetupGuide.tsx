// Removed: SSO Setup Guide component (no longer needed)
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  BookOpen, 
  CheckCircle, 
  ExternalLink, 
  Copy, 
  ArrowRight,
  Shield,
  Key,
  Settings,
  Users,
  AlertCircle,
  Info
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const steps = [
  {
    id: 1,
    title: "Enable SSO",
    description: "Toggle SSO to enabled in the SSO Status section",
    icon: Shield,
    status: "pending"
  },
  {
    id: 2,
    title: "Configure Settings",
    description: "Set up auto-provisioning and default roles",
    icon: Settings,
    status: "pending"
  },
  {
    id: 3,
    title: "Add Provider",
    description: "Configure your SSO provider (Google, Azure, etc.)",
    icon: Key,
    status: "pending"
  },
  {
    id: 4,
    title: "Test Connection",
    description: "Verify your SSO provider is working correctly",
    icon: CheckCircle,
    status: "pending"
  },
  {
    id: 5,
    title: "Enable Provider",
    description: "Activate the SSO provider for your users",
    icon: Users,
    status: "pending"
  }
];

const providers = [
  {
    name: "Google Workspace",
    type: "OpenID Connect",
    setup: {
      clientId: "Your Google OAuth Client ID",
      clientSecret: "Your Google OAuth Client Secret",
      issuerUrl: "https://accounts.google.com",
      discoveryUrl: "https://accounts.google.com/.well-known/openid_configuration",
      scopes: "openid email profile"
    },
    instructions: [
      "Go to Google Cloud Console",
      "Create OAuth 2.0 Client ID",
      "Set application type to Web application",
      "Add redirect URI from your ATS system",
      "Copy Client ID and Client Secret"
    ]
  },
  {
    name: "Azure Active Directory",
    type: "SAML 2.0",
    setup: {
      clientId: "Your Azure App Registration ID",
      clientSecret: "Your Azure App Client Secret",
      issuerUrl: "https://login.microsoftonline.com/[tenant-id]",
      discoveryUrl: "N/A for SAML",
      scopes: "N/A for SAML"
    },
    instructions: [
      "Go to Azure Portal",
      "Navigate to Azure Active Directory",
      "Create new app registration",
      "Set redirect URI from your ATS system",
      "Generate client secret"
    ]
  },
  {
    name: "Okta",
    type: "OpenID Connect",
    setup: {
      clientId: "Your Okta Client ID",
      clientSecret: "Your Okta Client Secret",
      issuerUrl: "https://your-domain.okta.com",
      discoveryUrl: "https://your-domain.okta.com/.well-known/openid_configuration",
      scopes: "openid email profile"
    },
    instructions: [
      "Go to Okta Developer Console",
      "Create new application",
      "Choose Web application type",
      "Configure redirect URI",
      "Copy Client ID and Client Secret"
    ]
  }
];

export default function SSOSetupGuide() {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedProvider, setSelectedProvider] = useState(providers[0]);
  const { toast } = useToast();

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied",
      description: "Text copied to clipboard"
    });
  };

  const getStepStatus = (stepId: number) => {
    if (stepId < currentStep) return "completed";
    if (stepId === currentStep) return "current";
    return "pending";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "current":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">SSO Setup Guide</h1>
        <p className="text-gray-600">Configure Single Sign-On for your organization</p>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="troubleshooting">Troubleshooting</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="w-5 h-5" />
                Setup Process
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {steps.map((step) => (
                  <div
                    key={step.id}
                    className={`flex items-center p-4 rounded-lg border-2 transition-all ${
                      getStepStatus(step.id) === 'current' 
                        ? 'border-blue-500 bg-blue-50' 
                        : getStepStatus(step.id) === 'completed'
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className={`flex items-center justify-center w-10 h-10 rounded-full mr-4 ${
                      getStepStatus(step.id) === 'completed' 
                        ? 'bg-green-500' 
                        : getStepStatus(step.id) === 'current'
                        ? 'bg-blue-500'
                        : 'bg-gray-300'
                    }`}>
                      {getStepStatus(step.id) === 'completed' ? (
                        <CheckCircle className="w-5 h-5 text-white" />
                      ) : (
                        <step.icon className="w-5 h-5 text-white" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{step.title}</h3>
                      <p className="text-sm text-gray-600">{step.description}</p>
                    </div>
                    <Badge className={getStatusColor(getStepStatus(step.id))}>
                      {getStepStatus(step.id)}
                    </Badge>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 flex justify-center">
                <Button 
                  onClick={() => setCurrentStep(Math.min(currentStep + 1, steps.length))}
                  className="flex items-center gap-2"
                >
                  Next Step <ArrowRight className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="providers">
          <div className="grid gap-6 md:grid-cols-2">
            {providers.map((provider) => (
              <Card 
                key={provider.name}
                className={`cursor-pointer transition-all ${
                  selectedProvider.name === provider.name 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'hover:border-gray-300'
                }`}
                onClick={() => setSelectedProvider(provider)}
              >
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold">{provider.name}</div>
                      <div className="text-sm text-gray-600">{provider.type}</div>
                    </div>
                    {selectedProvider.name === provider.name && (
                      <CheckCircle className="w-5 h-5 text-blue-500" />
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="text-sm">
                      <strong>Setup Requirements:</strong>
                    </div>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {provider.instructions.map((instruction, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <span className="text-blue-500">•</span>
                          {instruction}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {selectedProvider && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Configuration for {selectedProvider.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Client ID
                      </label>
                      <div className="flex items-center gap-2">
                        <code className="flex-1 p-2 bg-gray-100 rounded text-sm">
                          {selectedProvider.setup.clientId}
                        </code>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(selectedProvider.setup.clientId)}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Client Secret
                      </label>
                      <div className="flex items-center gap-2">
                        <code className="flex-1 p-2 bg-gray-100 rounded text-sm">
                          {selectedProvider.setup.clientSecret}
                        </code>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(selectedProvider.setup.clientSecret)}
                        >
                          <Copy className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Issuer URL
                    </label>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-2 bg-gray-100 rounded text-sm">
                        {selectedProvider.setup.issuerUrl}
                      </code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(selectedProvider.setup.issuerUrl)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Redirect URI (Add this to your SSO provider)
                    </label>
                    <div className="flex items-center gap-2">
                      <code className="flex-1 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm">
                        {window.location.origin}/api/sso/callback/{selectedProvider.name.toLowerCase().replace(/\s+/g, '-')}
                      </code>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(`${window.location.origin}/api/sso/callback/${selectedProvider.name.toLowerCase().replace(/\s+/g, '-')}`)}
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="troubleshooting">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5" />
                Common Issues & Solutions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="border-l-4 border-red-500 pl-4">
                  <h4 className="font-semibold text-red-700">Invalid Redirect URI</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Ensure the redirect URI in your SSO provider matches exactly. 
                    Copy the redirect URI from the provider configuration above.
                  </p>
                </div>

                <div className="border-l-4 border-yellow-500 pl-4">
                  <h4 className="font-semibold text-yellow-700">Client Secret Invalid</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Regenerate the client secret in your SSO provider and update 
                    the configuration in the ATS system.
                  </p>
                </div>

                <div className="border-l-4 border-blue-500 pl-4">
                  <h4 className="font-semibold text-blue-700">Domain Not Allowed</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Add your organization's domain to the Allowed Domains list 
                    in the SSO Settings tab.
                  </p>
                </div>

                <div className="border-l-4 border-green-500 pl-4">
                  <h4 className="font-semibold text-green-700">User Not Auto-Provisioned</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Ensure Auto-provision users is enabled and the user's email 
                    domain is in the allowed domains list.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="w-5 h-5" />
                Security Best Practices
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-semibold">Regular Review</h4>
                    <p className="text-sm text-gray-600">
                      Periodically review SSO providers and user access permissions.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-semibold">Least Privilege</h4>
                    <p className="text-sm text-gray-600">
                      Set appropriate default roles for new users (typically "Viewer" or "Member").
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-semibold">Domain Restrictions</h4>
                    <p className="text-sm text-gray-600">
                      Only allow domains that you control and verify domain ownership.
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <h4 className="font-semibold">Backup Authentication</h4>
                    <p className="text-sm text-gray-600">
                      Maintain local admin accounts as backup access method.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="text-center">
        <Button 
          onClick={() => window.open('/SSO_SETUP_GUIDE.md', '_blank')}
          variant="outline"
          className="flex items-center gap-2"
        >
          <ExternalLink className="w-4 h-4" />
          View Full Documentation
        </Button>
      </div>
    </div>
  );
}