import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, Link } from 'react-router-dom';
import AuthLayout from '../components/AuthLayout';

export default function SignupPage() {
  const { register, isLoading, error, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const [registerForm, setRegisterForm] = useState({
    email: '',
    fullName: '',
    password: '',
    organizationId: '',
  });

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated && user && !isLoading) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, user, isLoading, navigate]);

  // Handle organization search
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    try {
      const response = await fetch(`/api/organizations/search?query=${encodeURIComponent(searchQuery)}`);
      const data = await response.json();
      setSearchResults(data.organizations || []);
    } catch (error) {
      console.error('Search failed:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await register(registerForm);
    } catch (error) {
      console.error('Registration failed:', error);
    }
  };

  return (
    <AuthLayout>
      {/* Logo */}
      <div className="mb-5">
        <img src="/Login/Vector.svg" alt="Steorra Logo" className="w-10 h-10 mb-3" />
        <h1 className="text-2xl font-bold text-[#151518] mb-1">
          Join Steorra
        </h1>
        <p className="text-[#4B4B52] text-sm">
          Create your account
        </p>
      </div>

      {/* Organization Search Box */}
      <div className="mb-4 p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-[#E5E7EB]">
        <h3 className="text-xs font-semibold text-[#151518] mb-1">
          Find Your Organization ID
        </h3>
        <p className="text-xs text-[#4B4B52] mb-3">
          Search for your organization by name or domain.
        </p>
        <div className="flex gap-2">
          <input
            type="text"
            className="flex-1 px-3 py-2 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent"
            placeholder="Search by organization name"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
          />
          <button
            type="button"
            onClick={handleSearch}
            disabled={isSearching}
            className="px-3 py-2 bg-[#0152FF] text-white rounded-xl hover:bg-[#0142CC] transition-colors disabled:opacity-50"
          >
            <svg width="18" height="18" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M9 17A8 8 0 1 0 9 1a8 8 0 0 0 0 16zM18 18l-4-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
        {searchResults.length > 0 && (
          <div className="mt-2 space-y-1 max-h-32 overflow-y-auto">
            {searchResults.map((org) => (
              <button
                key={org.id}
                type="button"
                onClick={() => {
                  setRegisterForm({ ...registerForm, organizationId: org.id });
                  setSearchResults([]);
                  setSearchQuery('');
                }}
                className="w-full text-left px-3 py-2 bg-white rounded-lg hover:bg-[#F0F4FF] transition-colors text-sm"
              >
                <div className="font-medium text-[#151518]">{org.name}</div>
                {org.domain && <div className="text-xs text-[#9CA3AF]">{org.domain}</div>}
              </button>
            ))}
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-xl text-red-600 text-sm">
          {error}
        </div>
      )}

      {/* Registration Form */}
      <form onSubmit={handleRegister} className="space-y-3.5">
        {/* Email Field */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-[#151518] mb-1.5">
            Email address <span className="text-red-500">*</span>
          </label>
          <input
            id="email"
            type="email"
            required
            className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
            placeholder="Enter the email address"
            value={registerForm.email}
            onChange={(e) => setRegisterForm({ ...registerForm, email: e.target.value })}
          />
        </div>

        {/* Password Field */}
        <div>
          <label htmlFor="password" className="block text-sm font-medium text-[#151518] mb-1.5">
            Password <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              required
              minLength={8}
              className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all pr-12"
              placeholder="Enter the password"
              value={registerForm.password}
              onChange={(e) => setRegisterForm({ ...registerForm, password: e.target.value })}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#9CA3AF] hover:text-[#4B4B52] transition-colors"
            >
              <img src="/Login/eye-off.svg" alt="Toggle password" className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Organization ID Field */}
        <div>
          <label htmlFor="organizationId" className="block text-sm font-medium text-[#151518] mb-1.5">
            Organization ID <span className="text-red-500">*</span>
          </label>
          <input
            id="organizationId"
            type="text"
            required
            className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
            placeholder="Contact your admin for org ID"
            value={registerForm.organizationId}
            onChange={(e) => setRegisterForm({ ...registerForm, organizationId: e.target.value })}
          />
        </div>

        {/* Create Account Button */}
        <button
          type="submit"
          disabled={isLoading}
          className="w-full py-2.5 px-6 bg-[#0152FF] text-white font-semibold rounded-full hover:bg-[#0142CC] transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>{isLoading ? 'Creating account...' : 'Create account'}</span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </form>

      {/* Footer Links */}
      <div className="mt-4 text-center text-sm">
        <p className="text-[#4B4B52]">
          Already have an account?{' '}
          <Link to="/login" className="text-[#0152FF] font-semibold hover:underline">
            Sign in
          </Link>
        </p>
        <p className="text-[#4B4B52] mt-2">
          Need to create an organization?{' '}
          <Link to="/create-organization" className="text-[#0152FF] font-semibold hover:underline">
            Create Organization
          </Link>
        </p>
      </div>
    </AuthLayout>
  );
}

