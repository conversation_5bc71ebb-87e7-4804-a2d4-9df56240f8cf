import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { EventEmitter } from 'events';
import OpenAI from 'openai';

interface TranscriptionResult {
  text: string;
  segments?: Array<{
    start: number;
    end: number;
    text: string;
    speaker?: string;
  }>;
  language?: string;
  duration?: number;
}

interface SummaryResult {
  summary: string;
  keyPoints: string[];
  competencyScores: {
    [competency: string]: {
      score: number;
      evidence: string[];
      feedback: string;
    };
  };
  overallRating: number;
  recommendations: string[];
}

class TranscriptionService extends EventEmitter {
  private openai: OpenAI;
  private recordingsDir: string;

  constructor() {
    super();
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    this.recordingsDir = path.join(process.cwd(), 'recordings');
    this.ensureRecordingsDirectory();
  }

  private ensureRecordingsDirectory(): void {
    if (!fs.existsSync(this.recordingsDir)) {
      fs.mkdirSync(this.recordingsDir, { recursive: true });
    }
  }

  /**
   * Start recording audio from a stream
   */
  async startRecording(sessionId: string, audioStream: NodeJS.ReadableStream): Promise<string> {
    const recordingPath = path.join(this.recordingsDir, `${sessionId}-${Date.now()}.wav`);
    
    try {
      console.log(`🎙️ Starting recording for session ${sessionId}`);
      
      // Create write stream for recording
      const writeStream = fs.createWriteStream(recordingPath);
      
      // Pipe audio stream to file
      audioStream.pipe(writeStream);
      
      // Handle stream events
      writeStream.on('finish', () => {
        console.log(`✅ Recording completed for session ${sessionId}`);
        this.emit('recordingComplete', sessionId, recordingPath);
      });

      writeStream.on('error', (error) => {
        console.error(`❌ Recording error for session ${sessionId}:`, error);
        this.emit('recordingError', sessionId, error);
      });

      return recordingPath;
    } catch (error) {
      console.error(`❌ Error starting recording for session ${sessionId}:`, error);
      throw error;
    }
  }

  /**
   * Transcribe audio file using OpenAI Whisper
   */
  async transcribeAudio(audioFilePath: string): Promise<TranscriptionResult> {
    try {
      console.log(`🎯 Transcribing audio file: ${audioFilePath}`);

      if (!fs.existsSync(audioFilePath)) {
        throw new Error(`Audio file not found: ${audioFilePath}`);
      }

      // Check file size (OpenAI has 25MB limit)
      const stats = fs.statSync(audioFilePath);
      if (stats.size > 25 * 1024 * 1024) {
        console.log('📦 File too large, splitting into chunks...');
        return await this.transcribeLargeFile(audioFilePath);
      }

      // Transcribe using OpenAI Whisper
      const transcription = await this.openai.audio.transcriptions.create({
        file: fs.createReadStream(audioFilePath),
        model: 'whisper-1',
        response_format: 'verbose_json',
        timestamp_granularities: ['segment']
      });

      const result: TranscriptionResult = {
        text: transcription.text,
        language: transcription.language,
        duration: transcription.duration,
        segments: transcription.segments?.map(segment => ({
          start: segment.start,
          end: segment.end,
          text: segment.text
        }))
      };

      console.log(`✅ Transcription completed. Length: ${result.text.length} characters`);
      return result;

    } catch (error) {
      console.error('❌ Error transcribing audio:', error);
      throw error;
    }
  }

  /**
   * Transcribe large audio files by splitting them
   */
  private async transcribeLargeFile(audioFilePath: string): Promise<TranscriptionResult> {
    const chunks = await this.splitAudioFile(audioFilePath);
    const transcriptions: TranscriptionResult[] = [];

    for (const chunkPath of chunks) {
      try {
        const transcription = await this.transcribeAudio(chunkPath);
        transcriptions.push(transcription);
        
        // Clean up chunk file
        fs.unlinkSync(chunkPath);
      } catch (error) {
        console.error(`Error transcribing chunk ${chunkPath}:`, error);
      }
    }

    // Combine transcriptions
    const combinedText = transcriptions.map(t => t.text).join(' ');
    const combinedSegments = transcriptions.reduce((acc, t, index) => {
      const offset = index * 600; // 10 minutes per chunk
      const segments = t.segments?.map(s => ({
        ...s,
        start: s.start + offset,
        end: s.end + offset
      })) || [];
      return acc.concat(segments);
    }, [] as any[]);

    return {
      text: combinedText,
      segments: combinedSegments,
      duration: combinedSegments[combinedSegments.length - 1]?.end || 0
    };
  }

  /**
   * Split audio file into smaller chunks
   */
  private async splitAudioFile(audioFilePath: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const chunks: string[] = [];
      const baseDir = path.dirname(audioFilePath);
      const baseName = path.basename(audioFilePath, path.extname(audioFilePath));
      
      // Use ffmpeg to split audio into 10-minute chunks
      const ffmpeg = spawn('ffmpeg', [
        '-i', audioFilePath,
        '-f', 'segment',
        '-segment_time', '600', // 10 minutes
        '-c', 'copy',
        path.join(baseDir, `${baseName}_chunk_%03d.wav`)
      ]);

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          // Find all chunk files
          const files = fs.readdirSync(baseDir);
          const chunkFiles = files
            .filter(f => f.startsWith(`${baseName}_chunk_`))
            .map(f => path.join(baseDir, f));
          
          resolve(chunkFiles);
        } else {
          reject(new Error(`FFmpeg exited with code ${code}`));
        }
      });

      ffmpeg.on('error', reject);
    });
  }

  /**
   * Generate interview summary and competency scores
   */
  async generateInterviewSummary(
    transcription: TranscriptionResult,
    jobRole: string,
    agentProfile?: any
  ): Promise<SummaryResult> {
    try {
      console.log(`📊 Generating interview summary for role: ${jobRole}`);

      const competencies = this.getCompetenciesForRole(jobRole);
      
      const prompt = `
You are an expert HR interviewer analyzing an interview transcript. Please provide a comprehensive analysis.

**Job Role:** ${jobRole}
**Interview Transcript:**
${transcription.text}

**Key Competencies to Evaluate:**
${competencies.map(c => `- ${c.name}: ${c.description}`).join('\n')}

Please provide your analysis in the following JSON format:
{
  "summary": "Brief 2-3 sentence summary of the interview",
  "keyPoints": ["Key point 1", "Key point 2", "Key point 3"],
  "competencyScores": {
    "${competencies[0]?.name}": {
      "score": 0-10,
      "evidence": ["Evidence from transcript"],
      "feedback": "Specific feedback"
    }
  },
  "overallRating": 0-10,
  "recommendations": ["Recommendation 1", "Recommendation 2"]
}

Rate each competency from 0-10 where:
- 0-3: Below expectations
- 4-6: Meets expectations  
- 7-8: Exceeds expectations
- 9-10: Outstanding

Provide specific evidence from the transcript for each score.
`;

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert HR interviewer and assessment specialist. Provide detailed, objective analysis based on the interview transcript.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const responseText = completion.choices[0]?.message?.content;
      if (!responseText) {
        throw new Error('No response from OpenAI');
      }

      // Parse JSON response
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('Could not parse JSON from OpenAI response');
      }

      const summary: SummaryResult = JSON.parse(jsonMatch[0]);
      
      console.log(`✅ Interview summary generated. Overall rating: ${summary.overallRating}/10`);
      return summary;

    } catch (error) {
      console.error('❌ Error generating interview summary:', error);
      throw error;
    }
  }

  /**
   * Get competencies for a specific job role
   */
  private getCompetenciesForRole(jobRole: string): Array<{name: string, description: string}> {
    const roleKeywords = jobRole.toLowerCase();
    
    // Base competencies for all roles
    const baseCompetencies = [
      { name: 'Communication', description: 'Clear verbal and written communication skills' },
      { name: 'Problem Solving', description: 'Ability to analyze and solve complex problems' },
      { name: 'Teamwork', description: 'Collaboration and interpersonal skills' },
      { name: 'Adaptability', description: 'Flexibility and ability to handle change' }
    ];

    // Technical competencies based on role
    let technicalCompetencies: Array<{name: string, description: string}> = [];

    if (roleKeywords.includes('software') || roleKeywords.includes('developer') || roleKeywords.includes('engineer')) {
      technicalCompetencies = [
        { name: 'Technical Knowledge', description: 'Understanding of relevant programming languages and technologies' },
        { name: 'System Design', description: 'Ability to design scalable and maintainable systems' },
        { name: 'Code Quality', description: 'Writing clean, efficient, and well-documented code' }
      ];
    } else if (roleKeywords.includes('manager') || roleKeywords.includes('lead')) {
      technicalCompetencies = [
        { name: 'Leadership', description: 'Ability to guide and motivate team members' },
        { name: 'Strategic Thinking', description: 'Long-term planning and decision-making skills' },
        { name: 'Project Management', description: 'Planning, executing, and delivering projects' }
      ];
    } else if (roleKeywords.includes('sales') || roleKeywords.includes('business')) {
      technicalCompetencies = [
        { name: 'Sales Skills', description: 'Ability to identify opportunities and close deals' },
        { name: 'Customer Focus', description: 'Understanding and meeting customer needs' },
        { name: 'Market Knowledge', description: 'Understanding of industry and market trends' }
      ];
    }

    return [...baseCompetencies, ...technicalCompetencies];
  }

  /**
   * Clean up old recording files
   */
  async cleanupOldRecordings(maxAgeHours: number = 24): Promise<void> {
    try {
      const files = fs.readdirSync(this.recordingsDir);
      const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);

      for (const file of files) {
        const filePath = path.join(this.recordingsDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          fs.unlinkSync(filePath);
          console.log(`🗑️ Cleaned up old recording: ${file}`);
        }
      }
    } catch (error) {
      console.error('❌ Error cleaning up recordings:', error);
    }
  }
}

export const transcriptionService = new TranscriptionService();
export default transcriptionService;
