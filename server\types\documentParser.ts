/**
 * TypeScript interfaces and types for the document parser module
 * These types match the Python document_parser.py functionality
 */

export interface ContactInfo {
  name: string | null;
  email: string | null;
  phone: string | null;
  location: string | null;
  linkedin: string | null;
}

export interface JobAnalysis {
  overall_score: number;
  match_score: number;
  experience_years: number;
  key_skills: string[];
  matched_skills: string[];
  missing_skills: string[];
  strengths: string[];
  concerns: string[];
  recommendation: 'HIRE' | 'INTERVIEW' | 'REJECT';
  detailed_feedback: string;
  interview_questions: string[];
}

export interface OpenAIAnalysisResult {
  contactInfo: ContactInfo;
  analysis?: JobAnalysis;
}

export interface ParsedResumeData {
  contactInfo: ContactInfo;
  extractedText: string;
  method: string;
  hasJobAnalysis: boolean;
  analysis?: JobAnalysis;
}

export interface DocumentParserOptions {
  openaiApiKey?: string;
  jobDescription?: string;
}

export interface TextExtractionResult {
  text: string;
  success: boolean;
  error?: string;
}

export interface DocumentParserError extends Error {
  code: string;
  details?: any;
}

/**
 * Supported file types for document parsing
 */
export type SupportedFileType = 
  | 'application/pdf'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/msword'
  | 'text/plain';

/**
 * Phone number patterns used for extraction
 */
export const PHONE_PATTERNS = [
  /\+\d{1,3}[\s\-]?\d{3,4}[\s\-]?\d{3,4}[\s\-]?\d{3,4}/,
  /\(\d{3}\)[\s\-]?\d{3}[\s\-]?\d{4}/,
  /\d{3}[\s\-]?\d{3}[\s\-]?\d{4}/,
  /\+\d{1,3}[\s\-]?\d{7,14}/
] as const;

/**
 * Email pattern for extraction
 */
export const EMAIL_PATTERN = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/;

/**
 * LinkedIn pattern for extraction
 */
export const LINKEDIN_PATTERN = /linkedin\.com\/in\/[\w\-]+/i;

/**
 * Location pattern for extraction (City, State format)
 */
export const LOCATION_PATTERN = /([A-Z][a-z]+,\s*[A-Z]{2})|([A-Z][a-z]+,\s*[A-Z][a-z]+)/;

/**
 * OpenAI API configuration
 */
export interface OpenAIConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

export const DEFAULT_OPENAI_CONFIG: Omit<OpenAIConfig, 'apiKey'> = {
  model: 'gpt-3.5-turbo',
  maxTokens: 1000,
  temperature: 0.3
};
