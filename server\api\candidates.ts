import { Router, Request, Response } from 'express';
import { db } from '../db';
import { candidates, candidateAvailability, jobPostings, voiceCalls, voiceCallNotes } from '@shared/schema';
import { eq, desc, and, sql, gte } from 'drizzle-orm';
import { sendAvailabilityEmail } from '../services/emailService';
import { authenticateToken } from '../auth';
import { requireOrganizationAccess, requireRole } from '../middleware/organizationAccess';
import { validateCandidateApplication, CandidateValidationError } from '../utils/candidateValidation';
import { auditLoggerMiddleware, criticalActionLogger } from '../middleware/auditLogger';

const router = Router();

// Extend Express Request to include authenticated user set by `authenticateToken`
interface AuthenticatedRequest extends Request {
  user?: {
    id?: string;
    organizationId?: string;
    role?: string;
    [key: string]: any;
  };
}

// Workaround: drizzle types are strict; use any for DB operations in this route file to keep code concise
const anyDB = db as any;

/**
 * @swagger
 * /candidates:
 *   get:
 *     summary: Get all candidates
 *     description: Retrieve all candidates ordered by most recently updated
 *     tags: [Candidates]
 *     responses:
 *       200:
 *         description: List of candidates
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Candidate'
 *       500:
 *         description: Internal server error
 */
const getCandidatesHandler = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const organizationId = req.user?.organizationId;
    const { status, q, limit } = req.query;

    // Build search condition if q provided
    const searchValue = typeof q === 'string' && q.trim() ? q.trim().toLowerCase() : null;
    const numericLimit = Number(limit) || 100;

    // Build final query without reassigning intermediate types
    let finalQuery;

    if (req.user?.role === 'super_admin') {
      // Super admin sees all candidates
      finalQuery = anyDB.select().from(candidates);
      if (status) {
        finalQuery = finalQuery.where(eq(candidates.status, status as any));
      }
      if (searchValue) {
        finalQuery = finalQuery.where(sql`(LOWER(${candidates.fullName}) LIKE ${'%' + searchValue + '%' } OR LOWER(${candidates.email}) LIKE ${'%' + searchValue + '%'} OR LOWER(${candidates.location}) LIKE ${'%' + searchValue + '%'})`);
      }
      finalQuery = finalQuery.orderBy(desc(candidates.updatedAt)).limit(numericLimit);
    } else {
      const conditions: any[] = [eq(candidates.organizationId, organizationId!)];
      if (status) {
        conditions.push(eq(candidates.status, status as any));
      }
      if (searchValue) {
        conditions.push(sql`(LOWER(${candidates.fullName}) LIKE ${'%' + searchValue + '%' } OR LOWER(${candidates.email}) LIKE ${'%' + searchValue + '%'} OR LOWER(${candidates.location}) LIKE ${'%' + searchValue + '%'})`);
      }

      finalQuery = db
        .select()
        .from(candidates)
        .where(and(...conditions))
        .orderBy(desc(candidates.updatedAt))
        .limit(numericLimit);
    }

    const allCandidates = await finalQuery;
    return res.json(allCandidates);
  } catch (error) {
    console.error('Error fetching candidates:', error);
    return res.status(500).json({ error: 'Failed to fetch candidates' });
  }
};

router.get('/', authenticateToken as any, requireOrganizationAccess as any, auditLoggerMiddleware('candidate', 'list') as any, getCandidatesHandler as any);

/**
 * @swagger
 * /candidates/{id}:
 *   get:
 *     summary: Get candidate by ID
 *     description: Retrieve a specific candidate by their ID
 *     tags: [Candidates]
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Candidate ID
 *         schema:
 *           type: string
 *           format: uuid
 *     responses:
 *       200:
 *         description: Candidate details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Candidate'
 *       404:
 *         description: Candidate not found
 *       500:
 *         description: Internal server error
 */
const getCandidateByIdHandler = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params;
    const organizationId = req.user?.organizationId;

    // Base query
    let query = db.select().from(candidates).where(eq(candidates.id, id));

    if (req.user?.role !== 'super_admin') {
      query = db
        .select()
        .from(candidates)
        .where(and(eq(candidates.id, id), eq(candidates.organizationId, organizationId!)));
    }

    const [candidate] = await query.limit(1);

    if (!candidate) {
      return res.status(404).json({ error: 'Candidate not found or access denied' });
    }

    return res.json(candidate);
  } catch (error) {
    console.error('Error fetching candidate:', error);
    return res.status(500).json({ error: 'Failed to fetch candidate' });
  }
};

router.get('/:id', authenticateToken as any, requireOrganizationAccess as any, auditLoggerMiddleware('candidate', 'view') as any, getCandidateByIdHandler as any);

/**
 * @swagger
 * /candidates:
 *   post:
 *     summary: Create or update candidate
 *     description: Create a new candidate or update existing one
 *     tags: [Candidates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               fullName:
 *                 type: string
 *               phone:
 *                 type: string
 *               location:
 *                 type: string
 *               skills:
 *                 type: array
 *                 items:
 *                   type: string
 *               experienceYears:
 *                 type: number
 *               appliedJobId:
 *                 type: string
 *                 format: uuid
 *     responses:
 *       200:
 *         description: Candidate updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Candidate'
 *       201:
 *         description: Candidate created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Candidate'
 *       500:
 *         description: Internal server error
 */
const saveCandidateHandler = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('Received candidate data - fields:', Object.keys(req.body));
    }

    const requestData = req.body as any;
    let candidateInfo: any = {};

    // Handle different data formats
    if (requestData.contactInfo) {
      // Format from resume analysis
      const { contactInfo, analysisResult, extractedText, jobPostingId } = requestData;
      candidateInfo = {
        email: contactInfo.email,
        fullName: contactInfo.name || 'Unknown',
        phone: contactInfo.phone || null,
        location: contactInfo.location || null,
        linkedinUrl: contactInfo.linkedin || null,
        resumeText: extractedText || '',
        skills: analysisResult?.key_skills || [],
        experienceYears: analysisResult?.experience_years || 0,
        status: 'pending_review',
        analysisResult: analysisResult || null,
        overallScore: analysisResult?.overall_score || null,
        matchScore: analysisResult?.match_score || null,
        recommendation: analysisResult?.recommendation || null,
        appliedJobId: jobPostingId || null,
        sourceChannel: 'resume_upload',
        sourceType: 'direct_application',
        tags: [],
        notes: null,
        aiSummary: analysisResult?.detailed_feedback || null,
      };
    } else {
      // Direct format from form submissions
      candidateInfo = {
        email: requestData.email,
        fullName: requestData.fullName,
        phone: requestData.phone || null,
        location: requestData.location || null,
        linkedinUrl: requestData.linkedinUrl || null,
        resumeText: requestData.resumeText || null,
        skills: requestData.skills || [],
        experienceYears: requestData.experienceYears || 0,
        status: requestData.status || 'pending_review',
        analysisResult: requestData.analysisResult || null,
        overallScore: requestData.overallScore || null,
        matchScore: requestData.matchScore || null,
        recommendation: requestData.recommendation || null,
        appliedJobId: requestData.appliedJobId || null,
        sourceChannel: requestData.sourceChannel || 'manual',
        sourceType: requestData.sourceType || 'direct_application',
        tags: requestData.tags || [],
        notes: requestData.notes || null,
        aiSummary: requestData.aiSummary || null,
      };
    }

    // Set organization ID from authenticated user (SECURITY: Never trust request body)
    candidateInfo.organizationId = req.user?.organizationId;

    // Validate required fields
    if (!candidateInfo.email || !candidateInfo.fullName) {
      console.error('Missing required fields:', { 
        email: candidateInfo.email, 
        fullName: candidateInfo.fullName 
      });
      return res.status(400).json({ 
        error: 'Email and full name are required',
        errorCode: 'MISSING_REQUIRED_FIELDS',
        received: { email: candidateInfo.email, fullName: candidateInfo.fullName }
      });
    }

    // BUSINESS VALIDATION: Check candidate application rules
    if (candidateInfo.appliedJobId) {
      const validationResult = await validateCandidateApplication(
        candidateInfo.email, 
        candidateInfo.appliedJobId, 
        candidateInfo.organizationId
      );

      if (!validationResult.isValid) {
        console.log(`🚫 Business validation failed for ${candidateInfo.email}:`, validationResult);
        return res.status(409).json({ 
          error: validationResult.message,
          errorCode: validationResult.errorCode,
          data: validationResult.data,
          isBusinessRule: true
        });
      }
    }

    // Check if candidate already exists for this specific job posting and organization
    const existingCandidate = await anyDB
      .select()
      .from(candidates)
      .where(and(
        eq(candidates.email, candidateInfo.email),
        candidateInfo.appliedJobId ? 
          eq(candidates.appliedJobId, candidateInfo.appliedJobId) : 
          sql`applied_job_id IS NULL`,
        eq(candidates.organizationId, candidateInfo.organizationId)
      ))
      .limit(1);

    let savedCandidate: any;
    
    if (existingCandidate.length > 0) {
      console.log('Updating existing candidate for specific job:', candidateInfo.email, 'Job:', candidateInfo.appliedJobId);
      // Update existing candidate for same job posting
      [savedCandidate] = await anyDB
        .update(candidates)
        .set({
          ...candidateInfo,
          updatedAt: new Date()
        } as any)
        .where(and(
          eq(candidates.email, candidateInfo.email),
          candidateInfo.appliedJobId ? 
            eq(candidates.appliedJobId, candidateInfo.appliedJobId) : 
            sql`applied_job_id IS NULL`,
          eq(candidates.organizationId, candidateInfo.organizationId)
        ))
        .returning();
    } else {
      console.log('Creating new candidate:', candidateInfo.email);
      // Create new candidate
      [savedCandidate] = await anyDB
        .insert(candidates)
        .values(candidateInfo as any)
        .returning();
    }

    console.log('Candidate saved successfully:', savedCandidate.id);
    return res.status(existingCandidate.length > 0 ? 200 : 201).json(savedCandidate);
    
  } catch (error) {
    console.error('Error saving candidate:', error);
    
    // Handle specific database constraint errors
    if (error instanceof Error) {
      // Handle unique constraint violations
      if (error.message.includes('duplicate key value violates unique constraint')) {
        return res.status(409).json({ 
          error: 'A candidate with this email has already applied for this position.',
          errorCode: 'DUPLICATE_APPLICATION',
          isBusinessRule: true
        });
      }
      
      // Handle validation errors
      if (error instanceof CandidateValidationError) {
        return res.status(409).json({ 
          error: error.message,
          errorCode: error.errorCode,
          data: error.data,
          isBusinessRule: true
        });
      }
    }
    
    return res.status(500).json({ 
      error: 'Failed to save candidate',
      errorCode: 'INTERNAL_ERROR'
    });
  }
};

router.post('/', authenticateToken as any, requireOrganizationAccess as any, saveCandidateHandler as any);

// Approve candidate for interview
router.post('/:id/approve', async (req, res) => {
  try {
    const { id } = req.params;
    
    // Update candidate status
    const [updatedCandidate] = await db
      .update(candidates)
      .set({ 
        status: 'approved_for_interview',
        updatedAt: new Date()
      })
      .where(eq(candidates.id, id))
      .returning();

    if (!updatedCandidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Send availability request email
    try {
      await sendAvailabilityEmail(updatedCandidate);
      
      // Create availability request record
      await db.insert(candidateAvailability).values({
        candidateId: id,
        status: 'requested'
      });

    } catch (emailError) {
      console.error('Email sending failed:', emailError);
      // Continue even if email fails - candidate is still approved
    }

    res.json({ 
      message: 'Candidate approved and availability email sent',
      candidate: updatedCandidate 
    });

  } catch (error) {
    console.error('Error approving candidate:', error);
    res.status(500).json({ error: 'Failed to approve candidate' });
  }
});

// Reject candidate
router.post('/:id/reject', async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const [updatedCandidate] = await db
      .update(candidates)
      .set({ 
        status: 'rejected',
        notes: reason || null,
        updatedAt: new Date()
      })
      .where(eq(candidates.id, id))
      .returning();

    if (!updatedCandidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    res.json({ 
      message: 'Candidate rejected',
      candidate: updatedCandidate 
    });

  } catch (error) {
    console.error('Error rejecting candidate:', error);
    res.status(500).json({ error: 'Failed to reject candidate' });
  }
});

// Dismiss candidate
router.post('/:id/dismiss', async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const [updatedCandidate] = await db
      .update(candidates)
      .set({ 
        status: 'dismissed',
        notes: reason || null,
        updatedAt: new Date()
      })
      .where(eq(candidates.id, id))
      .returning();

    if (!updatedCandidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    res.json({ 
      message: 'Candidate dismissed',
      candidate: updatedCandidate 
    });

  } catch (error) {
    console.error('Error dismissing candidate:', error);
    res.status(500).json({ error: 'Failed to dismiss candidate' });
  }
});

// Reinstate dismissed candidate
router.post('/:id/reinstate', async (req, res) => {
  try {
    const { id } = req.params;

    // Get candidate to check current status
    const [candidate] = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, id))
      .limit(1);

    if (!candidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    if (candidate.status !== 'dismissed' && candidate.status !== 'rejected') {
      return res.status(400).json({ error: 'Only dismissed or rejected candidates can be reinstated' });
    }

    const [updatedCandidate] = await db
      .update(candidates)
      .set({ 
        status: 'pending_review',
        updatedAt: new Date()
      })
      .where(eq(candidates.id, id))
      .returning();

    res.json({ 
      message: 'Candidate reinstated',
      candidate: updatedCandidate 
    });

  } catch (error) {
    console.error('Error reinstating candidate:', error);
    res.status(500).json({ error: 'Failed to reinstate candidate' });
  }
});

// Get candidates approved for interview
router.get('/approved', async (req, res) => {
  try {
    const approvedCandidates = await db
      .select()
      .from(candidates)
      .where(eq(candidates.status, 'approved_for_interview'))
      .orderBy(desc(candidates.updatedAt));
    
    res.json(approvedCandidates);
  } catch (error) {
    console.error('Error fetching approved candidates:', error);
    res.status(500).json({ error: 'Failed to fetch approved candidates' });
  }
});

// Receive availability from candidate
router.post('/:id/availability', async (req, res) => {
  try {
    const { id } = req.params;
    const { availableSlots } = req.body;

    // Update availability record
    const [availability] = await db
      .update(candidateAvailability)
      .set({ 
        availableSlots,
        respondedAt: new Date(),
        status: 'received',
        updatedAt: new Date()
      })
      .where(eq(candidateAvailability.candidateId, id))
      .returning();

    if (!availability) {
      return res.status(404).json({ error: 'Availability request not found' });
    }

    res.json({ 
      message: 'Availability received',
      availability 
    });

  } catch (error) {
    console.error('Error updating availability:', error);
    res.status(500).json({ error: 'Failed to update availability' });
  }
});

// Clear all candidates from scheduling (bulk remove)
router.post('/clear-scheduling', async (req, res) => {
  try {
    console.log('Clearing all candidates from scheduling...');

    // Get all approved candidates
    const approvedCandidates = await db
      .select()
      .from(candidates)
      .where(eq(candidates.status, 'approved_for_interview'));

    // Update all approved candidates back to pending review status
    await db
      .update(candidates)
      .set({ 
        status: 'pending_review',
        updatedAt: new Date()
      })
      .where(eq(candidates.status, 'approved_for_interview'));

    // Remove all availability records for these candidates
    for (const candidate of approvedCandidates) {
      await db
        .delete(candidateAvailability)
        .where(eq(candidateAvailability.candidateId, candidate.id));
    }

    console.log(`Cleared ${approvedCandidates.length} candidates from scheduling`);

    res.json({ 
      message: `Successfully cleared ${approvedCandidates.length} candidates from scheduling`,
      count: approvedCandidates.length
    });

  } catch (error) {
    console.error('Error clearing scheduling:', error);
    res.status(500).json({ error: 'Failed to clear scheduling' });
  }
});

// Delete individual candidate
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Deleting candidate:', id);

    // Delete all related records in the correct order to avoid foreign key violations
    
    // 1. Delete voice call notes first (references voice_calls)
    const voiceCallIds = await db
      .select({ id: voiceCalls.id })
      .from(voiceCalls)
      .where(eq(voiceCalls.candidateId, id));
    
    for (const call of voiceCallIds) {
      await db
        .delete(voiceCallNotes)
        .where(eq(voiceCallNotes.callId, call.id));
    }

    // 2. Delete voice calls (references candidate)
    await db
      .delete(voiceCalls)
      .where(eq(voiceCalls.candidateId, id));

    // 3. Delete availability records (references candidate)
    await db
      .delete(candidateAvailability)
      .where(eq(candidateAvailability.candidateId, id));

    // 4. Finally delete the candidate
    const [deletedCandidate] = await db
      .delete(candidates)
      .where(eq(candidates.id, id))
      .returning();

    if (!deletedCandidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    console.log('Candidate deleted successfully with all related records:', id);
    res.json({ 
      message: 'Candidate deleted successfully',
      candidate: deletedCandidate 
    });

  } catch (error) {
    console.error('Error deleting candidate:', error);
    res.status(500).json({ error: 'Failed to delete candidate' });
  }
});

// Bulk delete candidates
router.post('/bulk-delete', async (req, res) => {
  try {
    const { candidateIds } = req.body;
    console.log('Bulk deleting candidates:', candidateIds);

    if (!candidateIds || !Array.isArray(candidateIds)) {
      return res.status(400).json({ error: 'candidateIds array is required' });
    }

    // Delete all related records in the correct order for each candidate
    let deletedCount = 0;
    
    for (const id of candidateIds) {
      try {
        // 1. Delete voice call notes first
        const voiceCallIds = await db
          .select({ id: voiceCalls.id })
          .from(voiceCalls)
          .where(eq(voiceCalls.candidateId, id));
        
        for (const call of voiceCallIds) {
          await db
            .delete(voiceCallNotes)
            .where(eq(voiceCallNotes.callId, call.id));
        }

        // 2. Delete voice calls
        await db
          .delete(voiceCalls)
          .where(eq(voiceCalls.candidateId, id));

        // 3. Delete availability records
        await db
          .delete(candidateAvailability)
          .where(eq(candidateAvailability.candidateId, id));

        // 4. Delete the candidate
        const result = await db
          .delete(candidates)
          .where(eq(candidates.id, id))
          .returning();
        
        if (result.length > 0) deletedCount++;
      } catch (candidateError) {
        console.error(`Failed to delete candidate ${id}:`, candidateError);
      }
    }

    console.log(`Bulk deleted ${deletedCount} candidates with all related records`);
    res.json({ 
      message: `Successfully deleted ${deletedCount} candidates`,
      count: deletedCount
    });

  } catch (error) {
    console.error('Error bulk deleting candidates:', error);
    res.status(500).json({ error: 'Failed to bulk delete candidates' });
  }
});

// Map candidates to a specific job posting
const matchJobHandler = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { jobId } = req.params;
    const userOrgId = req.user?.organizationId;

    // Ensure we have an organization context for scoped queries
    if (!userOrgId) {
      return res.status(403).json({ error: 'Organization context not found for this user' });
    }

    // Get the job posting details with organization scoping
    const [jobPosting] = await anyDB
      .select()
      .from(jobPostings)
      .where(and(
        eq(jobPostings.id, jobId),
        eq(jobPostings.organizationId, userOrgId)
      ))
      .limit(1);

    if (!jobPosting) {
      return res.status(404).json({ error: 'Job posting not found' });
    }

    // Get all candidates that are linked to this specific job posting AND organization
    let matchedCandidates = await anyDB
      .select()
      .from(candidates)
      .where(and(
        eq(candidates.appliedJobId, jobId),
        eq(candidates.organizationId, userOrgId)
      ))
      .orderBy(desc(candidates.overallScore), desc(candidates.matchScore));

    // Calculate match scores based on various factors
    const enhancedCandidates = matchedCandidates.map((candidate: any) => {
      let matchScore = candidate.matchScore || candidate.overallScore || 0;

      // Boost score based on skill matches
      if (jobPosting.skillsRequired && candidate.skills) {
        const candidateSkills = candidate.skills.map((skill: string) => (skill || '').toLowerCase());
        const requiredSkills = jobPosting.skillsRequired.map((skill: string) => (skill || '').toLowerCase());

        const matchingSkills = requiredSkills.filter((skill: string) => 
          candidateSkills.some((candidateSkill: string) => 
            candidateSkill.includes(skill) || skill.includes(candidateSkill)
          )
        );

        const skillMatchPercentage = requiredSkills.length ? (matchingSkills.length / requiredSkills.length) * 100 : 0;
        matchScore = Math.min(100, matchScore + (skillMatchPercentage * 0.2));
      }

      // Boost score based on experience level match
      if (jobPosting.experienceLevel && candidate.experienceYears) {
        const experienceMatch = getExperienceMatch(jobPosting.experienceLevel, candidate.experienceYears);
        matchScore = Math.min(100, matchScore + (experienceMatch * 0.1));
      }

      return {
        ...candidate,
        matchScore: Math.round(matchScore)
      };
    });

    // Sort by match score and limit to top 50
    enhancedCandidates.sort((a: any, b: any) => b.matchScore - a.matchScore);
    const topCandidates = enhancedCandidates.slice(0, 50);

    return res.json(topCandidates);

  } catch (error) {
    console.error('Error mapping candidates to job:', error);
    return res.status(500).json({ error: 'Failed to map candidates to job posting' });
  }
};

router.post('/match-job/:jobId', authenticateToken as any, requireOrganizationAccess as any, matchJobHandler as any);

// Helper function to calculate experience level match
function getExperienceMatch(requiredLevel: string, candidateYears: number): number {
  const levelMap: Record<string, { min: number; max: number }> = {
    entry: { min: 0, max: 2 },
    mid: { min: 2, max: 5 },
    senior: { min: 5, max: 10 },
    executive: { min: 10, max: 100 }
  };

  const level = levelMap[requiredLevel.toLowerCase()];
  if (!level) return 0;

  if (candidateYears >= level.min && candidateYears <= level.max) {
    return 20; // Perfect match
  } else if (candidateYears >= level.min - 1 && candidateYears <= level.max + 2) {
    return 10; // Close match
  }

  return 0; // No match
}

// Auto-save candidate after resume analysis
const autoSaveHandler = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Auto-save endpoint called - fields:', Object.keys(req.body));
    }
    const { result, jobId } = req.body;
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Extracted jobId:', jobId);
    }

    if (!result || !result.contactInfo) {
      if (process.env.NODE_ENV === 'development') {
        console.error('🔧 Auto-save validation failed. Received data:', { result: !!result, contactInfo: !!result?.contactInfo });
      }
      return res.status(400).json({ error: 'Invalid analysis result - missing result or contactInfo' });
    }

    const candidateInfo: any = {
      fullName: result.contactInfo.name || 'Unknown',
      email: result.contactInfo.email || '',
      phone: result.contactInfo.phone || '',
      location: result.contactInfo.location || '',
      linkedinUrl: result.contactInfo.linkedin || '',
      skills: result.analysis?.key_skills || [],
      resumeText: result.extractedText || '',
      appliedJobId: jobId || null,
      status: 'pending_review',
      analysisResult: result.analysis || null, // Save the full analysis result
      matchScore: result.analysis?.match_score || 0,
      recommendation: result.analysis?.recommendation || '',
      experienceYears: result.analysis?.experience_years || 0,
      sourceType: 'direct_application',
      sourceChannel: 'resume_screening',
      organizationId: req.user?.organizationId,
      overallScore: result.analysis?.overall_score || 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Check if candidate already exists for this specific job posting and organization
    const existingCandidate = await anyDB
      .select()
      .from(candidates)
      .where(and(
        eq(candidates.email, candidateInfo.email),
        eq(candidates.appliedJobId, candidateInfo.appliedJobId || ''),
        eq(candidates.organizationId, candidateInfo.organizationId)
      ))
      .limit(1);

    let savedCandidate: any;

    if (existingCandidate.length > 0) {
      // Update existing candidate for same job posting
      [savedCandidate] = await anyDB
        .update(candidates)
        .set({
          ...candidateInfo,
          updatedAt: new Date()
        } as any)
        .where(and(
          eq(candidates.email, candidateInfo.email),
          eq(candidates.appliedJobId, candidateInfo.appliedJobId || ''),
          eq(candidates.organizationId, candidateInfo.organizationId)
        ))
        .returning();
    } else {
      // Create new candidate record for this job posting
      [savedCandidate] = await anyDB
        .insert(candidates)
        .values(candidateInfo as any)
        .returning();
    }

    console.log('Candidate auto-saved successfully:', savedCandidate.id);
    return res.status(existingCandidate.length > 0 ? 200 : 201).json(savedCandidate);

  } catch (error) {
    console.error('Error auto-saving candidate:', error);
    return res.status(500).json({ error: 'Failed to auto-save candidate' });
  }
};

router.post('/auto-save', authenticateToken as any, autoSaveHandler as any);

// Save candidate decision after manual review
router.post('/save', authenticateToken as any, async (req: any, res: Response) => {
  try {
    const { decision, analysisResult, jobId } = req.body;

    if (!analysisResult || !analysisResult.contactInfo) {
      return res.status(400).json({ error: 'Invalid analysis result' });
    }

    if (!decision || !['approve', 'reject'].includes(decision)) {
      return res.status(400).json({ error: 'Invalid decision. Must be "approve" or "reject"' });
    }

    const candidateInfo: any = {
      fullName: analysisResult.contactInfo.name || 'Unknown',
      email: analysisResult.contactInfo.email || '',
      phone: analysisResult.contactInfo.phone || '',
      location: analysisResult.contactInfo.location || '',
      linkedinUrl: analysisResult.contactInfo.linkedin || '',
      skills: analysisResult.analysis?.key_skills || [],
      resumeText: analysisResult.extractedText || '',
      appliedJobId: jobId || null,
      status: decision === 'approve' ? 'approved_for_interview' : 'rejected',
      analysisResult: analysisResult.analysis || null, // Save the full analysis result
      matchScore: analysisResult.analysis?.match_score || 0,
      recommendation: analysisResult.analysis?.recommendation || '',
      experienceYears: analysisResult.analysis?.experience_years || 0,
      sourceType: 'direct_application',
      sourceChannel: 'resume_screening',
      organizationId: (req as AuthenticatedRequest).user?.organizationId, // Set from authenticated user
      overallScore: analysisResult.analysis?.overall_score || 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Check if candidate already exists for this specific job posting and organization
    const existingCandidate = await anyDB
      .select()
      .from(candidates)
      .where(and(
        eq(candidates.email, candidateInfo.email),
        eq(candidates.appliedJobId, candidateInfo.appliedJobId || ''),
        eq(candidates.organizationId, candidateInfo.organizationId)
      ))
      .limit(1);

    let savedCandidate: any;

    if (existingCandidate.length > 0) {
      // Update existing candidate for same job posting
      [savedCandidate] = await anyDB
        .update(candidates)
        .set({
          ...candidateInfo,
          updatedAt: new Date()
        } as any)
        .where(and(
          eq(candidates.email, candidateInfo.email),
          eq(candidates.appliedJobId, candidateInfo.appliedJobId || ''),
          eq(candidates.organizationId, candidateInfo.organizationId)
        ))
        .returning();
    } else {
      // Create new candidate record for this job posting
      [savedCandidate] = await anyDB
        .insert(candidates)
        .values(candidateInfo as any)
        .returning();
    }

    console.log('Candidate decision saved successfully:', savedCandidate.id, decision);
    return res.status(existingCandidate.length > 0 ? 200 : 201).json({
      message: `Candidate ${decision}d successfully`,
      candidate: savedCandidate
    });

  } catch (error) {
    console.error('Error saving candidate decision:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      requestBody: req.body
    });
    return res.status(500).json({ error: 'Failed to save candidate decision' });
  }
});

export default router;