import { Router } from 'express';
import { calendarService, InterviewSlot } from '../services/calendarService';
import { db } from '../db';
import { candidates, candidateAvailability, interviews, applications } from '@shared/schema';
import { eq, sql } from 'drizzle-orm';

const router = Router();

/**
 * @swagger
 * /calendar/status:
 *   get:
 *     summary: Check calendar authentication status
 *     description: Verify if Calendar API is properly authenticated and ready for use
 *     tags: [Calendar]
 *     responses:
 *       200:
 *         description: Calendar authentication status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 authenticated:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       500:
 *         description: Failed to check calendar status
 */
router.get('/status', async (req, res) => {
  try {
    const isAuthenticated = calendarService.isAuthenticated();
    res.json({
      authenticated: isAuthenticated,
      message: isAuthenticated ? 'Calendar API is ready' : 'Calendar authentication required'
    });
  } catch (error) {
    console.error('Calendar status check error:', error);
    res.status(500).json({ error: 'Failed to check calendar status' });
  }
});

// Create calendar event for interview
router.post('/schedule-interview', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('Schedule interview request received - candidateId:', req.body.candidateId);
    }
    const { candidateId, selectedSlot, interviewerEmail, notes } = req.body;

    if (!candidateId || !selectedSlot) {
      console.log('Missing required fields:', { candidateId: !!candidateId, selectedSlot: !!selectedSlot });
      return res.status(400).json({ error: 'Candidate ID and selected slot are required' });
    }

    // Get candidate details
    const [candidate] = await db
      .select()
      .from(candidates)
      .where(eq(candidates.id, candidateId));

    if (!candidate) {
      return res.status(404).json({ error: 'Candidate not found' });
    }

    // Parse the selected slot
    let interviewDateTime: string;
    let slotNotes = notes || '';

    if (typeof selectedSlot === 'string') {
      try {
        const parsed = JSON.parse(selectedSlot);
        interviewDateTime = parsed.time || parsed.slots?.[0] || selectedSlot;
        slotNotes += parsed.notes ? `\n\nCandidate Notes: ${parsed.notes}` : '';
      } catch {
        interviewDateTime = selectedSlot;
      }
    } else {
      interviewDateTime = selectedSlot.time || selectedSlot.slots?.[0] || selectedSlot;
      slotNotes += selectedSlot.notes ? `\n\nCandidate Notes: ${selectedSlot.notes}` : '';
    }

    // Use the specific selected slot, not the first available slot
    const finalInterviewDateTime = selectedSlot.time || selectedSlot.slots?.[0] || selectedSlot || interviewDateTime;
    
    console.log('Selected slot from request:', selectedSlot);
    console.log('Final interview date/time to use:', finalInterviewDateTime);

    // Create interview slot object
    const interviewSlot: InterviewSlot = {
      candidateName: candidate.fullName,
      candidateEmail: candidate.email,
      interviewDateTime: finalInterviewDateTime, // Use the specific selected slot
      duration: 60, // Default 1 hour
      interviewerEmail: interviewerEmail || '<EMAIL>', // Default interviewer
      notes: slotNotes,
    };

    // Get candidate's timezone from their availability response
    let candidateTimezone = '';
    const availabilityResponse = await db
      .select()
      .from(candidateAvailability)
      .where(eq(candidateAvailability.candidateId, candidateId))
      .limit(1);
    
    if (availabilityResponse.length > 0 && availabilityResponse[0].selectedSlot) {
      try {
        const selectedSlotData = typeof availabilityResponse[0].selectedSlot === 'string' 
          ? JSON.parse(availabilityResponse[0].selectedSlot)
          : availabilityResponse[0].selectedSlot;
        candidateTimezone = selectedSlotData.timezone || '';
        console.log('Found candidate timezone from availability:', candidateTimezone);
        console.log('Full availability data:', selectedSlotData);
      } catch (e) {
        console.log('Could not parse timezone from availability data:', e.message);
        console.log('Raw selectedSlot data:', availabilityResponse[0].selectedSlot);
        console.log('Type of selectedSlot:', typeof availabilityResponse[0].selectedSlot);
      }
    }

    // Create calendar event with timezone
    const eventId = await calendarService.createInterviewEvent(interviewSlot, candidateTimezone);

    if (!eventId) {
      console.error('Failed to create calendar event for candidate:', candidateId);
      return res.status(500).json({ error: 'Failed to create calendar event. Calendar service may not be authenticated.' });
    }

    // Parse the date properly using the same logic as calendar service
    const { start } = calendarService.parseInterviewDateTime(finalInterviewDateTime, candidateTimezone);
    
    console.log('Parsed interview start time:', start);
    console.log('Start time is valid date:', start instanceof Date && !isNaN(start.getTime()));
    
    // Convert Date to ISO string for PostgreSQL
    const startTimeString = start.toISOString();
    console.log('Converted to ISO string:', startTimeString);
    
    // Since the database schema is different from the Drizzle schema,
    // let's use raw SQL to create the interview record
    console.log('Creating interview record in database...');
    
    try {
      // Create interview record directly in the database using raw SQL
      const interviewResult = await db.execute(sql`
        INSERT INTO interviews (id, application_id, scheduled_at, status, notes, created_at, updated_at)
        VALUES (gen_random_uuid(), 
                (SELECT id FROM applications WHERE candidate_id = ${candidateId} LIMIT 1), 
                ${startTimeString}, 'scheduled', ${slotNotes}, NOW(), NOW())
        RETURNING id, application_id;
      `);
      
      console.log('Interview record created successfully');
      
    } catch (dbError) {
      console.error('Database insert error:', dbError);
      // If application doesn't exist, create one first
      await db.execute(sql`
        INSERT INTO applications (id, candidate_id, status, applied_at, organization_id)
        SELECT gen_random_uuid(), ${candidateId}, 'interview_scheduled', NOW(), 
               (SELECT organization_id FROM candidates WHERE id = ${candidateId})
        WHERE NOT EXISTS (SELECT 1 FROM applications WHERE candidate_id = ${candidateId});
      `);
      
      // Now create the interview record
      const interviewResult = await db.execute(sql`
        INSERT INTO interviews (id, application_id, scheduled_at, status, notes, created_at, updated_at)
        VALUES (gen_random_uuid(), 
                (SELECT id FROM applications WHERE candidate_id = ${candidateId} LIMIT 1), 
                ${startTimeString}, 'scheduled', ${slotNotes}, NOW(), NOW())
        RETURNING id;
      `);
      
      console.log('Interview record created after creating application');
    }

    // Update candidate status
    await db
      .update(candidates)
      .set({ 
        status: 'interview_scheduled',
      })
      .where(eq(candidates.id, candidateId));

    res.json({
      success: true,
      eventId: eventId,
      calendarEventCreated: true,
      scheduledTime: interviewDateTime,
      message: 'Interview scheduled successfully and calendar event created'
    });

  } catch (error) {
    console.error('Error scheduling interview:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : 'Unknown'
    });
    res.status(500).json({ 
      error: 'Failed to schedule interview',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get upcoming interviews
router.get('/upcoming', async (req, res) => {
  try {
    const upcomingEvents = await calendarService.listUpcomingInterviews(20);
    
    // Get interview records from database
    const interviewRecords = await db
      .select({
        interview: interviews,
        candidate: candidates,
      })
      .from(interviews)
      .leftJoin(candidates, eq(interviews.candidateId, candidates.id))
      .where(eq(interviews.status, 'scheduled'));

    // Transform the data for easier consumption
    const transformedInterviews = interviewRecords.map(record => ({
      ...record.interview,
      candidate: record.candidate
    }));

    res.json({
      calendarEvents: upcomingEvents,
      interviews: transformedInterviews
    });

  } catch (error) {
    console.error('Error fetching upcoming interviews:', error);
    res.status(500).json({ error: 'Failed to fetch upcoming interviews' });
  }
});

// Update interview
router.patch('/interview/:interviewId', async (req, res) => {
  try {
    const { interviewId } = req.params;
    const { scheduledAt, status, notes } = req.body;

    // Get interview record
    const [interview] = await db
      .select()
      .from(interviews)
      .where(eq(interviews.id, interviewId));

    if (!interview) {
      return res.status(404).json({ error: 'Interview not found' });
    }

    // Update interview record
    const updateData: any = {};

    if (scheduledAt) updateData.scheduledAt = new Date(scheduledAt);
    if (status) updateData.status = status;
    if (notes !== undefined) updateData.notes = notes;

    await db
      .update(interviews)
      .set(updateData)
      .where(eq(interviews.id, interviewId));

    // If rescheduling and has calendar event, update calendar
    if (scheduledAt && interview.calendarEventId) {
      const [candidate] = await db
        .select()
        .from(candidates)
        .where(eq(candidates.id, interview.candidateId));

      if (candidate) {
        const interviewSlot: InterviewSlot = {
          candidateName: candidate.fullName,
          candidateEmail: candidate.email,
          interviewDateTime: scheduledAt,
          notes: notes || interview.notes || '',
        };

        await calendarService.updateInterviewEvent(interview.calendarEventId, interviewSlot);
      }
    }

    res.json({
      success: true,
      message: 'Interview updated successfully'
    });

  } catch (error) {
    console.error('Error updating interview:', error);
    res.status(500).json({ error: 'Failed to update interview' });
  }
});

// Cancel interview
router.delete('/interview/:interviewId', async (req, res) => {
  try {
    const { interviewId } = req.params;

    // Get interview record
    const [interview] = await db
      .select()
      .from(interviews)
      .where(eq(interviews.id, interviewId));

    if (!interview) {
      return res.status(404).json({ error: 'Interview not found' });
    }

    // Delete calendar event if exists
    if (interview.calendarEventId) {
      await calendarService.deleteInterviewEvent(interview.calendarEventId);
    }

    // Update interview status to cancelled
    await db
      .update(interviews)
      .set({
        status: 'cancelled',
      })
      .where(eq(interviews.id, interviewId));

    // Update candidate status back to approved
    await db
      .update(candidates)
      .set({ 
        status: 'approved_for_interview',
      })
      .where(eq(candidates.id, interview.candidateId));

    res.json({
      success: true,
      message: 'Interview cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling interview:', error);
    res.status(500).json({ error: 'Failed to cancel interview' });
  }
});

export default router;