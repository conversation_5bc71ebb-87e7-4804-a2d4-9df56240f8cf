import { IncomingMessage } from 'http';
import { WebSocket } from 'ws';

export function handleTwilioStream(ws: WebSocket, req: IncomingMessage) {
  console.log('🎉🎉🎉 TWILIO STREAM CONNECTED! 🎉🎉🎉');
  console.log('📞 Connection URL:', req.url);
  console.log('📞 Connection headers:', JSON.stringify(req.headers, null, 2));
  
  // Send immediate acknowledgment
  ws.send(JSON.stringify({ 
    event: 'connected', 
    protocol: 'Twilio Media Stream',
    version: '1.0'
  }));
  
  let streamSid: string | null = null;
  let elevenLabsWs: WebSocket | null = null;
  
  ws.on('message', async (data: Buffer) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('📨 Twilio message type:', message.event);
      
      switch (message.event) {
        case 'connected':
          console.log('✅ Twilio confirmed connection');
          break;
          
        case 'start':
          console.log('🎬 Stream started:', message.start);
          streamSid = message.start.streamSid;
          
          // Extract parameters
          const params = message.start.customParameters || {};
          const agentId = params.agent_id || process.env.ELEVENLABS_AGENT_ID;
          const apiKey = process.env.ELEVENLABS_API_KEY;
          
          console.log('🤖 Connecting to ElevenLabs with agent:', agentId);
          
          if (!agentId || !apiKey) {
            console.error('❌ Missing ElevenLabs credentials');
            return;
          }
          
          // Connect to ElevenLabs
          try {
            // Request signed URL
            const response = await fetch('https://api.elevenlabs.io/v1/convai/conversation/get_signed_url', {
              method: 'POST',
              headers: {
                'xi-api-key': apiKey,
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({ agent_id: agentId })
            });
            
            if (!response.ok) {
              throw new Error(`Failed to get signed URL: ${response.statusText}`);
            }
            
            const { signed_url } = await response.json();
            console.log('🔗 Got ElevenLabs signed URL');
            
            // Connect to ElevenLabs
            elevenLabsWs = new WebSocket(signed_url);
            
            elevenLabsWs.on('open', () => {
              console.log('✅ Connected to ElevenLabs Conversational AI');
              
              // Send initial configuration
              const config = {
                type: 'conversation_initiation_client_data',
                conversation_initiation_client_data: {
                  conversation_config_override: {
                    agent: {
                      prompt: {
                        prompt: `You are Sarah, a warm HR recruiter calling about the ${params.job_title || 'position'} at ${params.company_name || 'our company'}.`
                      }
                    }
                  }
                }
              };
              
              elevenLabsWs.send(JSON.stringify(config));
              console.log('📤 Sent ElevenLabs configuration');
            });
            
            elevenLabsWs.on('message', (data) => {
              const response = JSON.parse(data.toString());
              
              if (response.type === 'audio' && response.audio_event?.audio_base_64) {
                // Forward audio to Twilio
                ws.send(JSON.stringify({
                  event: 'media',
                  streamSid: streamSid,
                  media: {
                    payload: response.audio_event.audio_base_64
                  }
                }));
              }
            });
            
            elevenLabsWs.on('error', (error) => {
              console.error('❌ ElevenLabs error:', error);
            });
            
            elevenLabsWs.on('close', () => {
              console.log('📞 ElevenLabs connection closed');
            });
            
          } catch (error) {
            console.error('❌ Failed to connect to ElevenLabs:', error);
          }
          break;
          
        case 'media':
          // Forward audio to ElevenLabs
          if (elevenLabsWs?.readyState === WebSocket.OPEN && message.media?.payload) {
            elevenLabsWs.send(JSON.stringify({
              type: 'audio',
              audio: {
                chunk: message.media.payload,
                encoding: 'pcm_mulaw',
                sample_rate: 8000
              }
            }));
          }
          break;
          
        case 'stop':
          console.log('🛑 Stream stopped');
          if (elevenLabsWs) {
            elevenLabsWs.close();
          }
          break;
      }
    } catch (error) {
      console.error('❌ Error processing message:', error);
    }
  });
  
  ws.on('close', () => {
    console.log('📞 Twilio stream disconnected');
    if (elevenLabsWs) {
      elevenLabsWs.close();
    }
  });
  
  ws.on('error', (error) => {
    console.error('❌ Twilio stream error:', error);
  });
}