import express from 'express';
import { db } from './db';
import { users, organizations } from '@shared/schema';
import { eq, count } from 'drizzle-orm';
import { authenticateToken } from './auth';
import type { AuthenticatedRequest } from './auth';

const router = express.Router();

/**
 * @swagger
 * /organizations/me:
 *   get:
 *     summary: Get current organization
 *     description: Retrieve details of the current user's organization
 *     tags: [Organizations]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Organization details with user count
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Organization'
 *                 - type: object
 *                   properties:
 *                     user_count:
 *                       type: number
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Organization not found
 *       500:
 *         description: Internal server error
 */
router.get('/me', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const organization = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, req.user!.organizationId))
      .limit(1);

    if (!organization.length) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    // Get user count
    const userCount = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.organizationId, req.user!.organizationId));

    const orgWithCount = {
      ...organization[0],
      user_count: userCount[0]?.count || 0,
    };

    res.json(orgWithCount);
  } catch (error) {
    console.error('Error fetching organization:', error);
    res.status(500).json({ error: 'Failed to fetch organization' });
  }
});

// Get organization statistics
router.get('/stats', authenticateToken, async (req: AuthenticatedRequest, res) => {
  try {
    const organizationId = req.user!.organizationId;

    // Get user statistics
    const totalUsers = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.organizationId, organizationId));

    const activeUsers = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.organizationId, organizationId))
      .where(eq(users.isActive, true))
      .where(eq(users.isApproved, true));

    const pendingUsers = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.organizationId, organizationId))
      .where(eq(users.isApproved, false));

    const adminUsers = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.organizationId, organizationId))
      .where(eq(users.role, 'admin'));

    const memberUsers = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.organizationId, organizationId))
      .where(eq(users.role, 'member'));

    const viewerUsers = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.organizationId, organizationId))
      .where(eq(users.role, 'viewer'));

    const stats = {
      totalUsers: totalUsers[0]?.count || 0,
      activeUsers: activeUsers[0]?.count || 0,
      pendingUsers: pendingUsers[0]?.count || 0,
      adminUsers: adminUsers[0]?.count || 0,
      memberUsers: memberUsers[0]?.count || 0,
      viewerUsers: viewerUsers[0]?.count || 0,
    };

    res.json(stats);
  } catch (error) {
    console.error('Error fetching organization stats:', error);
    res.status(500).json({ error: 'Failed to fetch organization statistics' });
  }
});

export default router;