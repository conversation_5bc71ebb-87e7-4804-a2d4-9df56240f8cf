import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import {
  FileText,
  Calendar,
  Users,
  UserCheck,
  Building2,
  Search,
  LogOut,
  Settings,
  Home,
  Inbox,
  BarChart3,
  CreditCard,
  Shield,
  Phone,
  Mic,
  Bot
} from 'lucide-react';
import { useAuth } from "../contexts/AuthContext";
import { 
  ARIA_LABELS, 
  announceToScreenReader, 
  focusManagement, 
  createKeyboardNavigationHandler, 
  KEYBOARD_SHORTCUTS 
} from '@/utils/accessibility';

interface SidebarProps {
  currentPage: string;
  onNavigate: (page: string) => void;
}

export default function Sidebar({ currentPage, onNavigate }: SidebarProps) {
  const { user, logout } = useAuth();
  const [focusedIndex, setFocusedIndex] = useState(0);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const navItemsRef = useRef<HTMLButtonElement[]>([]);
  
  // Reorganized navigation items according to user workflow
  const navItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      path: '/dashboard',
      section: 'main',
      description: 'Executive overview & metrics'
    },
    {
      id: 'job-postings',
      label: 'Job Postings',
      icon: Building2,
      path: '/job-postings',
      section: 'main',
      description: 'Create & publish job postings'
    },
    {
      id: 'career-site',
      label: 'Candidate Search',
      icon: Search,
      path: '/candidate-search',
      section: 'main',
      description: 'Search candidates on job sites'
    },
    {
      id: 'screening',
      label: 'Resume Screening',
      icon: FileText,
      path: '/resume-screening',
      section: 'main',
      description: 'Upload & screen resumes'
    },
    {
      id: 'candidates',
      label: 'Applications',
      icon: Users,
      path: '/tracker',
      section: 'main',
      description: 'Review candidate applications'
    },
    {
      id: 'interview-automation',
      label: 'Zoom Interview',
      hidden: true, // temporarily hide this entry from the nav
      icon: Bot,
      path: '/interview-automation',
      section: 'main',
      description: 'View & schedule interviews'
    },
    {
      id: 'calendar',
      label: 'Interview Schedule',
      icon: Calendar,
      path: '/scheduling',
      section: 'main',
      description: 'Scheduled interview calendar'
    },
    {
      id: 'users',
      label: 'User Management',
      icon: UserCheck,
      path: '/users',
      section: 'settings',
      description: 'Manage team members'
    },
    {
      id: 'subscription',
      label: 'Subscription',
      icon: CreditCard,
      path: '/subscription',
      section: 'settings',
      description: 'Billing & plan details'
    }
  ];

  const superAdminNavItems = [
    { 
      id: 'super-admin', 
      label: 'Platform Management', 
      icon: Settings, 
      path: '/super-admin',
      section: 'admin'
    }
  ];

  const handleLogout = () => {
    announceToScreenReader('Signing out', 'assertive');
    logout();
  };

  // Focus management for keyboard navigation
  useEffect(() => {
    if (focusedIndex >= 0 && navItemsRef.current[focusedIndex]) {
      navItemsRef.current[focusedIndex].focus();
    }
  }, [focusedIndex]);

  // Keyboard navigation handler
  const handleKeyDown = (event: React.KeyboardEvent) => {
    const allItems = [...navItems, ...(user?.role === 'super_admin' ? superAdminNavItems : [])];
    
    switch (event.key) {
      case KEYBOARD_SHORTCUTS.ARROW_DOWN:
        event.preventDefault();
        setFocusedIndex((prev) => Math.min(prev + 1, allItems.length - 1));
        break;
      case KEYBOARD_SHORTCUTS.ARROW_UP:
        event.preventDefault();
        setFocusedIndex((prev) => Math.max(prev - 1, 0));
        break;
      case KEYBOARD_SHORTCUTS.HOME:
        event.preventDefault();
        setFocusedIndex(0);
        break;
      case KEYBOARD_SHORTCUTS.END:
        event.preventDefault();
        setFocusedIndex(allItems.length - 1);
        break;
      case KEYBOARD_SHORTCUTS.ENTER:
      case KEYBOARD_SHORTCUTS.SPACE:
        event.preventDefault();
        const currentItem = allItems[focusedIndex];
        if (currentItem) {
          announceToScreenReader(`Navigating to ${currentItem.label}`, 'polite');
          onNavigate(currentItem.path);
        }
        break;
    }
  };

  const renderNavItem = (item: any, isActive: boolean, index: number) => {
    const Icon = item.icon;

    return (
      <Button
        key={item.id}
        ref={(el) => {
          if (el) navItemsRef.current[index] = el;
        }}
        variant="ghost"
        onClick={() => {
          announceToScreenReader(`Navigating to ${item.label}`, 'polite');
          onNavigate(item.path);
        }}
        onKeyDown={handleKeyDown}
        className={`w-full justify-start mb-0.5 px-3 py-2 h-auto rounded-lg transition-all duration-300 group ${
          isActive
            ? 'bg-white/70 shadow-lg scale-[1.02]'
            : 'hover:bg-white/60 hover:shadow-md hover:scale-[1.01]'
        }`}
        style={{
          color: isActive ? '#0152FF' : '#2f2f2f',
          backgroundColor: isActive ? 'rgba(255, 255, 255, 0.7)' : 'transparent'
        }}
        aria-label={`${item.label} navigation item`}
        aria-current={isActive ? 'page' : undefined}
        tabIndex={index === focusedIndex ? 0 : -1}
        role="menuitem"
      >
        <Icon
          className="w-5 h-5 mr-3 flex-shrink-0 transition-all duration-300"
          style={{ color: isActive ? '#0152FF' : '#2f2f2f' }}
          aria-hidden="true"
        />
        <span className="text-sm font-medium">{item.label}</span>
      </Button>
    );
  };

  const renderSection = (title: string, items: any[]) => {
    if (items.length === 0) return null;

    const visibleItems = items.filter(i => !i.hidden);
    if (visibleItems.length === 0) return null;

    return (
      <div className="mb-3">
        {title && (
          <h3 className="text-[10px] font-bold uppercase tracking-tight mb-1 px-3 opacity-85" style={{ color: '#2f2f2f' }}>
            {title}
          </h3>
        )}
        <div className="space-y-0.5">
          {visibleItems.map((item, idx) => {
            const isActive = currentPage === item.id;
            return renderNavItem(item, isActive, idx);
          })}
        </div>
      </div>
    );
  };

  return (
    <div
      ref={sidebarRef}
      className="w-64 border-r border-gray-200 flex flex-col fixed left-0 top-0 h-screen shadow-xl z-40"
      style={{
        background: 'linear-gradient(180deg, #FFF5F0 0%, #FFE8E0 30%, #F0E8FF 70%, #E8F0FF 100%)'
      }}
      role="navigation"
      aria-label={ARIA_LABELS.mainNavigation}
    >
      {/* Logo/Brand */}
      <div className="p-4 border-b border-gray-200 flex-shrink-0">
        <div className="flex items-center gap-3">
          <img
            src="/Hero/Steorra.svg"
            alt="Steorra Logo"
            className="w-8 h-8"
          />
          <span className="text-xl font-semibold text-[#0152FF]">
            Steorra
          </span>
        </div>
      </div>

      {/* Navigation - No scrollbar, all items visible */}
      <div className="flex-1 px-3 py-4 overflow-hidden flex flex-col">
        {user?.role === 'super_admin' ? (
          // Super Admin Navigation: show ADMIN header, then show main items without the WORKFLOW header
          <>
            {renderSection('ADMIN', superAdminNavItems)}
            {renderSection('', navItems.filter(item => item.section === 'main'))}
          </>
        ) : (
          // Regular User Navigation - render main items without the WORKFLOW header
          <>
            {renderSection('', navItems.filter(item => item.section === 'main'))}
            {navItems.filter(item => item.section === 'settings').length > 0 && (
              renderSection('SETTINGS', navItems.filter(item => item.section === 'settings'))
            )}
          </>
        )}
      </div>

      {/* User Profile & Logout */}
      <div className="border-t p-3 flex-shrink-0" style={{ background: 'linear-gradient(180deg, #FFF5F0 0%, #FFE8E0 30%, #F0E8FF 70%, #E8F0FF 100%)', borderColor: 'rgba(0,0,0,0.04)' }}>
        <div className="flex items-center mb-2">
          <div className="w-9 h-9 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-full flex items-center justify-center shadow-lg">
            <span className="text-sm font-bold text-white">
              {user?.fullName?.charAt(0)?.toUpperCase() || 'U'}
            </span>
          </div>
          <div className="ml-2 flex-1 min-w-0">
            <p className="text-sm font-semibold text-slate-800 truncate">
              {user?.fullName || 'User'}
            </p>
            <p className="text-xs text-slate-600 capitalize font-medium">
              {user?.role?.replace('_', ' ') || 'Member'}
            </p>
          </div>
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          className="w-full justify-start text-rose-600 hover:text-white hover:bg-gradient-to-r hover:from-rose-500 hover:to-pink-600 rounded-lg transition-all duration-200 hover:shadow-md py-2"
        >
          <LogOut className="w-4 h-4 mr-2" />
          <span className="text-sm font-medium">Sign Out</span>
        </Button>
      </div>
    </div>
  );
}