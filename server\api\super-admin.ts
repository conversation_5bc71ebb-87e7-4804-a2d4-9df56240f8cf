import { Router } from 'express';
import { z } from 'zod';
import { db } from '../db';
import { authUsers, organizations, insertAuthUserSchema, insertOrganizationSchema } from '@shared/schema';
import { eq, isNull, count, desc, and, ne } from 'drizzle-orm';
import { authenticateToken, AuthenticatedRequest, hashPassword } from '../auth';
import { setupInitialSuperAdmin, isSuperAdminBootstrapped } from '../bootstrap-super-admin';

const router = Router();

// Middleware to check super admin access
const requireSuperAdmin = (req: AuthenticatedRequest, res: any, next: any) => {
  if (req.user?.role !== 'super_admin') {
    return res.status(403).json({ error: 'Super admin access required' });
  }
  next();
};

// Validation schemas
const createOrganizationSchema = z.object({
  name: z.string().min(2, 'Organization name must be at least 2 characters'),
  domain: z.string().optional(),
  adminEmail: z.string().email('Valid email required'),
  adminName: z.string().min(2, 'Admin name must be at least 2 characters'),
  adminPassword: z.string().min(8, 'Password must be at least 8 characters'),
});

const createSuperAdminSchema = z.object({
  email: z.string().email('Valid email required'),
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

const updateUserRoleSchema = z.object({
  role: z.enum(['super_admin', 'admin', 'hr_manager', 'recruiter', 'hiring_manager']),
});

const updateOrganizationStatusSchema = z.object({
  isActive: z.boolean(),
});

/**
 * @swagger
 * /api/super-admin/organizations:
 *   get:
 *     summary: Get all organizations (Super Admin)
 *     description: Retrieve all organizations with user statistics - super admin only
 *     tags: [Super Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of organizations with stats
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 allOf:
 *                   - $ref: '#/components/schemas/Organization'
 *                   - type: object
 *                     properties:
 *                       userCount:
 *                         type: number
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Super admin access required
 *       500:
 *         description: Failed to fetch organizations
 */
router.get('/organizations', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    const organizationsWithStats = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        domain: organizations.domain,
        isActive: organizations.isActive,
        createdAt: organizations.createdAt,
        updatedAt: organizations.updatedAt,
        userCount: count(authUsers.id),
      })
      .from(organizations)
      .leftJoin(authUsers, eq(organizations.id, authUsers.organizationId))
      .groupBy(organizations.id, organizations.name, organizations.domain, organizations.isActive, organizations.createdAt, organizations.updatedAt)
      .orderBy(desc(organizations.createdAt));

    res.json(organizationsWithStats);
  } catch (error) {
    console.error('Error fetching organizations:', error);
    res.status(500).json({ error: 'Failed to fetch organizations' });
  }
});

// Get organization details with users
router.get('/organizations/:orgId', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    const { orgId } = req.params;

    // Get organization details
    const [organization] = await db
      .select()
      .from(organizations)
      .where(eq(organizations.id, orgId))
      .limit(1);

    if (!organization) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    // Get organization users
    const users = await db
      .select({
        id: authUsers.id,
        email: authUsers.email,
        fullName: authUsers.fullName,
        role: authUsers.role,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
        createdAt: authUsers.createdAt,
      })
      .from(authUsers)
      .where(eq(authUsers.organizationId, orgId))
      .orderBy(desc(authUsers.createdAt));

    res.json({
      organization,
      users,
      stats: {
        totalUsers: users.length,
        activeUsers: users.filter(u => u.isActive).length,
        pendingApproval: users.filter(u => !u.isApproved).length,
        adminUsers: users.filter(u => u.role === 'admin').length,
      }
    });
  } catch (error) {
    console.error('Error fetching organization details:', error);
    res.status(500).json({ error: 'Failed to fetch organization details' });
  }
});

// Update organization status
router.patch('/organizations/:orgId', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    const { orgId } = req.params;
    const { isActive, name, domain } = req.body;

    const updateData: any = {};
    if (typeof isActive === 'boolean') updateData.isActive = isActive;
    if (name) updateData.name = name;
    if (domain !== undefined) updateData.domain = domain;

    const [updatedOrg] = await db
      .update(organizations)
      .set(updateData)
      .where(eq(organizations.id, orgId))
      .returning();

    if (!updatedOrg) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    res.json(updatedOrg);
  } catch (error) {
    console.error('Error updating organization:', error);
    res.status(500).json({ error: 'Failed to update organization' });
  }
});

// Get platform statistics
router.get('/stats', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    // Get organization count
    const [orgCount] = await db
      .select({ count: count() })
      .from(organizations);

    // Get total user count
    const [userCount] = await db
      .select({ count: count() })
      .from(authUsers)
      .where(isNull(authUsers.organizationId)); // Exclude super admins

    // Get active organizations
    const [activeOrgCount] = await db
      .select({ count: count() })
      .from(organizations)
      .where(eq(organizations.isActive, true));

    // Get users by role
    const roleStats = await db
      .select({
        role: authUsers.role,
        count: count()
      })
      .from(authUsers)
      .groupBy(authUsers.role);

    res.json({
      totalOrganizations: orgCount.count,
      activeOrganizations: activeOrgCount.count,
      totalUsers: userCount.count,
      roleDistribution: roleStats,
    });
  } catch (error) {
    console.error('Error fetching platform stats:', error);
    res.status(500).json({ error: 'Failed to fetch platform statistics' });
  }
});

// Create super admin user
router.post('/create-super-admin', async (req, res) => {
  try {
    const { email, fullName, password } = req.body;

    // Check if super admin already exists
    const existingSuperAdmin = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.role, 'super_admin'))
      .limit(1);

    if (existingSuperAdmin.length > 0) {
      return res.status(400).json({ error: 'Super admin already exists' });
    }

    // Check if user email already exists
    const existingUser = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.email, email))
      .limit(1);

    if (existingUser.length > 0) {
      return res.status(400).json({ error: 'User with this email already exists' });
    }

    const bcrypt = require('bcrypt');
    const hashedPassword = await bcrypt.hash(password, 10);

    const [superAdmin] = await db
      .insert(authUsers)
      .values({
        email,
        fullName,
        hashedPassword,
        role: 'super_admin',
        organizationId: null, // Super admin doesn't belong to any organization
        isActive: true,
        isApproved: true,
      })
      .returning();

    res.status(201).json({
      message: 'Super admin created successfully',
      user: {
        id: superAdmin.id,
        email: superAdmin.email,
        fullName: superAdmin.fullName,
        role: superAdmin.role,
      }
    });
  } catch (error) {
    console.error('Error creating super admin:', error);
    res.status(500).json({ error: 'Failed to create super admin' });
  }
});

/**
 * @swagger
 * /api/super-admin/setup:
 *   post:
 *     summary: Initial super admin setup
 *     description: One-time setup endpoint to create the first super admin (only works if no super admin exists)
 *     tags: [Super Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - fullName
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *               fullName:
 *                 type: string
 *                 minLength: 2
 *               password:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       201:
 *         description: Super admin created successfully
 *       400:
 *         description: System already bootstrapped or invalid input
 *       500:
 *         description: Internal server error
 */
router.post('/setup', async (req, res) => {
  try {
    const { email, fullName, password } = createSuperAdminSchema.parse(req.body);
    
    const superAdmin = await setupInitialSuperAdmin(email, password, fullName);
    
    res.status(201).json({
      message: 'Super admin created successfully',
      user: {
        id: superAdmin.id,
        email: superAdmin.email,
        full_name: superAdmin.fullName,
        role: superAdmin.role,
      },
    });
  } catch (error: any) {
    console.error('Super admin setup error:', error);
    if (error?.message?.includes('already has a super admin')) {
      return res.status(400).json({ error: error.message });
    }
    res.status(500).json({ error: 'Failed to create super admin' });
  }
});

/**
 * @swagger
 * /api/super-admin/organizations:
 *   get:
 *     summary: Get all organizations (Super Admin)
 *     description: Retrieve all organizations in the system - super admin only
 *     tags: [Super Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of organizations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 organizations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       name:
 *                         type: string
 *                       domain:
 *                         type: string
 *                       isActive:
 *                         type: boolean
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       userCount:
 *                         type: number
 *       403:
 *         description: Super admin access required
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Create organization (Super Admin)
 *     description: Create a new organization with admin user - super admin only
 *     tags: [Super Admin]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - adminEmail
 *               - adminName
 *               - adminPassword
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *               domain:
 *                 type: string
 *               adminEmail:
 *                 type: string
 *                 format: email
 *               adminName:
 *                 type: string
 *                 minLength: 2
 *               adminPassword:
 *                 type: string
 *                 minLength: 8
 *     responses:
 *       201:
 *         description: Organization created successfully
 *       400:
 *         description: Validation error or conflicts
 *       403:
 *         description: Super admin access required
 *       500:
 *         description: Internal server error
 */
router.get('/organizations', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    // Get all organizations with user count
    const orgs = await db
      .select({
        id: organizations.id,
        name: organizations.name,
        domain: organizations.domain,
        isActive: organizations.isActive,
        createdAt: organizations.createdAt,
        updatedAt: organizations.updatedAt,
        userCount: count(authUsers.id),
      })
      .from(organizations)
      .leftJoin(authUsers, eq(organizations.id, authUsers.organizationId))
      .groupBy(organizations.id, organizations.name, organizations.domain, organizations.isActive, organizations.createdAt, organizations.updatedAt)
      .orderBy(desc(organizations.createdAt));

    res.json({ organizations: orgs });
  } catch (error) {
    console.error('Fetch organizations error:', error);
    res.status(500).json({ error: 'Failed to fetch organizations' });
  }
});

router.post('/organizations', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    const { name, domain, adminEmail, adminName, adminPassword } = createOrganizationSchema.parse(req.body);

    // Check for conflicts
    const existingOrg = domain ? await db
      .select()
      .from(organizations)
      .where(eq(organizations.domain, domain))
      .limit(1) : [];

    const existingUser = await db
      .select()
      .from(authUsers)
      .where(eq(authUsers.email, adminEmail))
      .limit(1);

    if (existingOrg.length) {
      return res.status(400).json({ error: 'Organization domain already exists' });
    }

    if (existingUser.length) {
      return res.status(400).json({ error: 'Admin email already in use' });
    }

    // Create organization
    const newOrg = await db
      .insert(organizations)
      .values({
        name,
        domain,
        isActive: true,
      })
      .returning();

    // Create admin user
    const hashedPassword = hashPassword(adminPassword);
    const adminUser = await db
      .insert(authUsers)
      .values({
        email: adminEmail,
        fullName: adminName,
        hashedPassword,
        organizationId: newOrg[0].id,
        role: 'admin',
        isApproved: true,
        isActive: true,
      })
      .returning();

    res.status(201).json({
      message: 'Organization created successfully',
      organization: {
        id: newOrg[0].id,
        name: newOrg[0].name,
        domain: newOrg[0].domain,
        isActive: newOrg[0].isActive,
      },
      admin: {
        id: adminUser[0].id,
        email: adminUser[0].email,
        full_name: adminUser[0].fullName,
        role: adminUser[0].role,
      },
    });
  } catch (error) {
    console.error('Organization creation error:', error);
    res.status(500).json({ error: 'Failed to create organization' });
  }
});

/**
 * @swagger
 * /api/super-admin/organizations/{organizationId}/status:
 *   put:
 *     summary: Update organization status (Super Admin)
 *     description: Activate or deactivate an organization - super admin only
 *     tags: [Super Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: organizationId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - isActive
 *             properties:
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Organization status updated
 *       403:
 *         description: Super admin access required
 *       404:
 *         description: Organization not found
 *       500:
 *         description: Internal server error
 */
router.put('/organizations/:organizationId/status', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    const { organizationId } = req.params;
    const { isActive } = updateOrganizationStatusSchema.parse(req.body);

    const result = await db
      .update(organizations)
      .set({ 
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(organizations.id, organizationId))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'Organization not found' });
    }

    // Also update all users in the organization
    await db
      .update(authUsers)
      .set({ 
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(authUsers.organizationId, organizationId));

    res.json({
      message: `Organization ${isActive ? 'activated' : 'deactivated'} successfully`,
      organization: result[0],
    });
  } catch (error) {
    console.error('Organization status update error:', error);
    res.status(500).json({ error: 'Failed to update organization status' });
  }
});

/**
 * @swagger
 * /api/super-admin/users:
 *   get:
 *     summary: Get all users across organizations (Super Admin)
 *     description: Retrieve all users from all organizations - super admin only
 *     tags: [Super Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: organizationId
 *         in: query
 *         description: Filter by organization ID
 *         schema:
 *           type: string
 *           format: uuid
 *       - name: role
 *         in: query
 *         description: Filter by role
 *         schema:
 *           type: string
 *           enum: [super_admin, admin, hr_manager, recruiter, hiring_manager]
 *     responses:
 *       200:
 *         description: List of users
 *       403:
 *         description: Super admin access required
 *       500:
 *         description: Internal server error
 */
router.get('/users', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    const { organizationId, role } = req.query;

    let query = db
      .select({
        id: authUsers.id,
        email: authUsers.email,
        fullName: authUsers.fullName,
        role: authUsers.role,
        organizationId: authUsers.organizationId,
        organizationName: organizations.name,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
        createdAt: authUsers.createdAt,
        updatedAt: authUsers.updatedAt,
      })
      .from(authUsers)
      .leftJoin(organizations, eq(authUsers.organizationId, organizations.id));

    // Apply filters
    const conditions = [];
    if (organizationId) {
      conditions.push(eq(authUsers.organizationId, organizationId as string));
    }
    if (role) {
      conditions.push(eq(authUsers.role, role as any));
    }
    
    let baseQuery = db
      .select({
        id: authUsers.id,
        email: authUsers.email,
        fullName: authUsers.fullName,
        role: authUsers.role,
        organizationId: authUsers.organizationId,
        organizationName: organizations.name,
        isActive: authUsers.isActive,
        isApproved: authUsers.isApproved,
        createdAt: authUsers.createdAt,
        updatedAt: authUsers.updatedAt,
      })
      .from(authUsers)
      .leftJoin(organizations, eq(authUsers.organizationId, organizations.id));

    if (conditions.length > 0) {
      baseQuery = baseQuery.where(conditions.length === 1 ? conditions[0] : and(...conditions));
    }

    const users = await baseQuery.orderBy(desc(authUsers.createdAt));

    res.json({ users });
  } catch (error) {
    console.error('Fetch users error:', error);
    res.status(500).json({ error: 'Failed to fetch users' });
  }
});

/**
 * @swagger
 * /api/super-admin/users/{userId}/role:
 *   put:
 *     summary: Update user role (Super Admin)
 *     description: Change user role across organizations - super admin only
 *     tags: [Super Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - role
 *             properties:
 *               role:
 *                 type: string
 *                 enum: [super_admin, admin, hr_manager, recruiter, hiring_manager]
 *     responses:
 *       200:
 *         description: User role updated
 *       403:
 *         description: Super admin access required
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.put('/users/:userId/role', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    const { userId } = req.params;
    const { role } = updateUserRoleSchema.parse(req.body);

    // Prevent changing own role to avoid lockout
    if (userId === req.user?.id && role !== 'super_admin') {
      return res.status(400).json({ error: 'Cannot change your own super admin role' });
    }

    const result = await db
      .update(authUsers)
      .set({ 
        role,
        updatedAt: new Date(),
      })
      .where(eq(authUsers.id, userId))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: 'User role updated successfully',
      user: {
        id: result[0].id,
        email: result[0].email,
        full_name: result[0].fullName,
        role: result[0].role,
        organization_id: result[0].organizationId,
      },
    });
  } catch (error) {
    console.error('User role update error:', error);
    res.status(500).json({ error: 'Failed to update user role' });
  }
});

/**
 * @swagger
 * /api/super-admin/users/{userId}/status:
 *   put:
 *     summary: Update user status (Super Admin)
 *     description: Activate/deactivate user across organizations - super admin only
 *     tags: [Super Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: userId
 *         in: path
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               isActive:
 *                 type: boolean
 *               isApproved:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: User status updated
 *       403:
 *         description: Super admin access required
 *       404:
 *         description: User not found
 *       500:
 *         description: Internal server error
 */
router.put('/users/:userId/status', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    const { userId } = req.params;
    const { isActive, isApproved } = req.body;

    // Prevent deactivating own account
    if (userId === req.user?.id && isActive === false) {
      return res.status(400).json({ error: 'Cannot deactivate your own account' });
    }

    const updateData: any = { updatedAt: new Date() };
    if (typeof isActive === 'boolean') updateData.isActive = isActive;
    if (typeof isApproved === 'boolean') updateData.isApproved = isApproved;

    const result = await db
      .update(authUsers)
      .set(updateData)
      .where(eq(authUsers.id, userId))
      .returning();

    if (!result.length) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json({
      message: 'User status updated successfully',
      user: {
        id: result[0].id,
        email: result[0].email,
        is_active: result[0].isActive,
        is_approved: result[0].isApproved,
      },
    });
  } catch (error) {
    console.error('User status update error:', error);
    res.status(500).json({ error: 'Failed to update user status' });
  }
});

/**
 * @swagger
 * /api/super-admin/stats:
 *   get:
 *     summary: Get system-wide statistics (Super Admin)
 *     description: Retrieve comprehensive system statistics - super admin only
 *     tags: [Super Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: System statistics
 *       403:
 *         description: Super admin access required
 *       500:
 *         description: Internal server error
 */
router.get('/stats', authenticateToken, requireSuperAdmin, async (req: AuthenticatedRequest, res) => {
  try {
    // Get organization counts
    const [totalOrgs, activeOrgs] = await Promise.all([
      db.select({ count: count() }).from(organizations),
      db.select({ count: count() }).from(organizations).where(eq(organizations.isActive, true)),
    ]);

    // Get user counts by role
    const userStats = await db
      .select({
        role: authUsers.role,
        count: count(),
      })
      .from(authUsers)
      .groupBy(authUsers.role);

    // Get active vs inactive users
    const [totalUsers, activeUsers, approvedUsers] = await Promise.all([
      db.select({ count: count() }).from(authUsers),
      db.select({ count: count() }).from(authUsers).where(eq(authUsers.isActive, true)),
      db.select({ count: count() }).from(authUsers).where(eq(authUsers.isApproved, true)),
    ]);

    res.json({
      organizations: {
        total: totalOrgs[0].count,
        active: activeOrgs[0].count,
        inactive: totalOrgs[0].count - activeOrgs[0].count,
      },
      users: {
        total: totalUsers[0].count,
        active: activeUsers[0].count,
        approved: approvedUsers[0].count,
        pending_approval: totalUsers[0].count - approvedUsers[0].count,
        by_role: userStats.reduce((acc, stat) => {
          if (stat.role) {
            acc[stat.role] = stat.count;
          }
          return acc;
        }, {} as Record<string, number>),
      },
    });
  } catch (error) {
    console.error('Stats fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch statistics' });
  }
});

export default router;