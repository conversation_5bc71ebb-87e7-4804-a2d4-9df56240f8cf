import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate, Link } from 'react-router-dom';
import AuthLayout from '../components/AuthLayout';

export default function CreateOrganizationPage() {
  const { createOrganization, isLoading, error, isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);

  const [orgForm, setOrgForm] = useState({
    name: '',
    domain: '',
    adminEmail: '',
    adminName: '',
    adminPassword: '',
  });

  // Redirect to dashboard if already authenticated
  useEffect(() => {
    if (isAuthenticated && user && !isLoading) {
      navigate('/dashboard');
    }
  }, [isAuthenticated, user, isLoading, navigate]);

  const handleCreateOrg = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createOrganization(orgForm);
    } catch (error) {
      console.error('Organization creation failed:', error);
    }
  };

  return (
    <AuthLayout>
      {/* Logo */}
      <div className="mb-5">
        <img src="/Login/Vector.svg" alt="Steorra Logo" className="w-10 h-10 mb-3" />
        <h1 className="text-2xl font-bold text-[#151518] mb-1">
          Create Organization
        </h1>
        <p className="text-[#4B4B52] text-sm">
          Set up your ATS tenant
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-xl text-red-600 text-sm">
          {error}
        </div>
      )}

      {/* Organization Form */}
      <form onSubmit={handleCreateOrg} className="space-y-3.5">
        {/* Organization Name */}
        <div>
          <label htmlFor="org-name" className="block text-sm font-medium text-[#151518] mb-1.5">
            Organization Name <span className="text-red-500">*</span>
          </label>
          <input
            id="org-name"
            type="text"
            required
            className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
            placeholder="Enter the organization name"
            value={orgForm.name}
            onChange={(e) => setOrgForm({ ...orgForm, name: e.target.value })}
          />
        </div>

        {/* Domain (Optional) */}
        <div>
          <label htmlFor="domain" className="block text-sm font-medium text-[#151518] mb-1.5">
            Domain <span className="text-[#9CA3AF] text-xs">(optional)</span>
          </label>
          <input
            id="domain"
            type="text"
            className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
            placeholder="Company.com"
            value={orgForm.domain}
            onChange={(e) => setOrgForm({ ...orgForm, domain: e.target.value })}
          />
        </div>

        {/* Admin User Section */}
        <div className="pt-3 border-t border-[#E5E7EB]">
          <h3 className="text-base font-semibold text-[#151518] mb-3">Admin User</h3>

          {/* Admin Full Name */}
          <div className="mb-3.5">
            <label htmlFor="admin-name" className="block text-sm font-medium text-[#151518] mb-1.5">
              Admin Full Name
            </label>
            <input
              id="admin-name"
              type="text"
              required
              className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
              placeholder="Enter admin name"
              value={orgForm.adminName}
              onChange={(e) => setOrgForm({ ...orgForm, adminName: e.target.value })}
            />
          </div>

          {/* Admin Email */}
          <div className="mb-3.5">
            <label htmlFor="admin-email" className="block text-sm font-medium text-[#151518] mb-1.5">
              Admin Email
            </label>
            <input
              id="admin-email"
              type="email"
              required
              className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all"
              placeholder="<EMAIL>"
              value={orgForm.adminEmail}
              onChange={(e) => setOrgForm({ ...orgForm, adminEmail: e.target.value })}
            />
          </div>

          {/* Admin Password */}
          <div>
            <label htmlFor="admin-password" className="block text-sm font-medium text-[#151518] mb-1.5">
              Admin Password
            </label>
            <div className="relative">
              <input
                id="admin-password"
                type={showPassword ? 'text' : 'password'}
                required
                minLength={8}
                className="w-full px-4 py-2.5 bg-white border border-[#E5E7EB] rounded-xl text-sm text-[#151518] placeholder-[#9CA3AF] focus:outline-none focus:ring-2 focus:ring-[#0152FF] focus:border-transparent transition-all pr-12"
                placeholder="Min 8 characters"
                value={orgForm.adminPassword}
                onChange={(e) => setOrgForm({ ...orgForm, adminPassword: e.target.value })}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-[#9CA3AF] hover:text-[#4B4B52] transition-colors"
              >
                <img src="/Login/eye-off.svg" alt="Toggle password" className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Create Organization Button */}
        <button
          type="submit"
          disabled={isLoading}
          className="w-full py-2.5 px-6 bg-[#0152FF] text-white font-semibold rounded-full hover:bg-[#0142CC] transition-colors flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>{isLoading ? 'Creating...' : 'Create Organization'}</span>
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7.5 15L12.5 10L7.5 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </form>

      {/* Footer Links */}
      <div className="mt-4 text-center text-sm">
        <Link to="/login" className="text-[#0152FF] font-semibold hover:underline">
          Back to login
        </Link>
      </div>
    </AuthLayout>
  );
}

