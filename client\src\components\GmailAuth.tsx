import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Mail, CheckCircle, AlertTriangle, ExternalLink } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface GmailAuthProps {
  className?: string;
}

const GmailAuth: React.FC<GmailAuthProps> = ({ className = '' }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authUrl, setAuthUrl] = useState<string | null>(null);
  const { toast } = useToast();

  // Check Gmail authentication status
  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/gmail/status');
      const data = await response.json();
      setIsAuthenticated(data.authenticated);
    } catch (error) {
      console.error('Error checking Gmail auth status:', error);
      setIsAuthenticated(false);
    }
  };

  // Get Gmail authorization URL
  const getAuthUrl = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/gmail/auth-url');
      const data = await response.json();
      setAuthUrl(data.authUrl);
    } catch (error) {
      console.error('Error getting auth URL:', error);
      toast({
        title: "Error",
        description: "Failed to get Gmail authorization URL",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Gmail authorization
  const handleAuthorize = () => {
    if (authUrl) {
      window.open(authUrl, '_blank');
      // Check status after a delay
      setTimeout(() => {
        checkAuthStatus();
      }, 3000);
    }
  };

  useEffect(() => {
    checkAuthStatus();
  }, []);

  return (
    <Card className={`${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="w-5 h-5" />
          Gmail Email Integration
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isAuthenticated ? (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium">Gmail Connected</span>
              <Badge variant="default" className="bg-green-100 text-green-800">
                Active
              </Badge>
            </div>
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Gmail is connected and working. The system can now send interview availability requests to candidates and monitor for responses.
              </AlertDescription>
            </Alert>
            <Button
              onClick={checkAuthStatus}
              variant="outline"
              size="sm"
              className="w-full"
            >
              Refresh Status
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-500" />
              <span className="text-sm font-medium">Gmail Not Connected</span>
              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                Requires Setup
              </Badge>
            </div>
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Gmail authentication is required to send interview availability requests to candidates and monitor their responses.
              </AlertDescription>
            </Alert>
            <div className="space-y-2">
              {!authUrl && (
                <Button
                  onClick={getAuthUrl}
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? 'Getting Authorization...' : 'Get Gmail Authorization'}
                </Button>
              )}
              {authUrl && (
                <Button
                  onClick={handleAuthorize}
                  className="w-full flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  Authorize Gmail Access
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GmailAuth;