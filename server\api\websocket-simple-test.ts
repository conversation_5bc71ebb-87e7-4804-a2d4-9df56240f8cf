import express from 'express';

const router = express.Router();

// Simple WebSocket connection test
router.get('/test-simple-ws', (req, res) => {
  res.send(`
<!DOCTYPE html>
<html>
<head>
    <title>Simple WebSocket Test</title>
</head>
<body>
    <h1>Simple WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    
    <script>
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        // Test connection to our unified WebSocket server
        const wsUrl = 'wss://d06491fe-31d9-4c0f-a305-1580bfb51dce-00-1r2x4e11g1xfi.spock.replit.dev/test-simple';
        console.log('Connecting to:', wsUrl);
        
        const ws = new WebSocket(wsUrl);
        
        ws.onopen = function(event) {
            statusDiv.innerHTML = '✅ WebSocket Connected!';
            statusDiv.style.color = 'green';
            console.log('WebSocket connected:', event);
        };
        
        ws.onmessage = function(event) {
            messagesDiv.innerHTML += '<div>Received: ' + event.data + '</div>';
            console.log('Message received:', event.data);
        };
        
        ws.onerror = function(error) {
            statusDiv.innerHTML = '❌ WebSocket Error: ' + error;
            statusDiv.style.color = 'red';
            console.error('WebSocket error:', error);
        };
        
        ws.onclose = function(event) {
            statusDiv.innerHTML = '📞 WebSocket Closed (Code: ' + event.code + ')';
            statusDiv.style.color = 'orange';
            console.log('WebSocket closed:', event);
        };
        
        // Send test message after 1 second
        setTimeout(() => {
            if (ws.readyState === WebSocket.OPEN) {
                ws.send('Test message from browser');
            }
        }, 1000);
    </script>
</body>
</html>
  `);
});

export default router;