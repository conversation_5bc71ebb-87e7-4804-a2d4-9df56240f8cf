/**
 * Integration tests for resume parsing API endpoints
 * Tests the complete flow from API request to TypeScript document parser
 */

import request from 'supertest';
import express from 'express';
import multer from 'multer';
import { parseResumeWithTypeScript } from '../../server/api/resume';

// Mock the document parser
jest.mock('../../server/utils/documentParser', () => ({
  parseDocument: jest.fn()
}));

import { parseDocument } from '../../server/utils/documentParser';

const mockParseDocument = parseDocument as jest.MockedFunction<typeof parseDocument>;

describe('Resume Parser Integration Tests', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    
    // Mock multer for file uploads
    const upload = multer({ storage: multer.memoryStorage() });
    
    // Test endpoint that uses the TypeScript parser
    app.post('/test-parse', upload.single('resume'), async (req, res) => {
      try {
        if (!req.file) {
          return res.status(400).json({ error: 'No file uploaded' });
        }

        const fileContent = req.file.buffer.toString('base64');
        const fileName = req.file.originalname;
        const fileType = req.file.mimetype;
        const jobDescription = req.body.jobDescription;

        const result = await parseResumeWithTypeScript(fileContent, fileName, fileType, jobDescription);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    jest.clearAllMocks();
  });

  describe('POST /test-parse', () => {
    it('should successfully parse a resume with contact info only', async () => {
      const mockResult = {
        contactInfo: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '************',
          location: 'San Francisco, CA',
          linkedin: 'linkedin.com/in/johndoe'
        },
        extractedText: 'John Doe\nSoftware Engineer\<EMAIL>\n************',
        method: 'TypeScript regex parsing',
        hasJobAnalysis: false
      };

      mockParseDocument.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/test-parse')
        .attach('resume', Buffer.from('John Doe\nSoftware Engineer\<EMAIL>\n************'), 'resume.txt')
        .expect(200);

      expect(response.body).toEqual(mockResult);
      expect(mockParseDocument).toHaveBeenCalledWith(
        expect.any(String), // base64 content
        'resume.txt',
        'text/plain',
        'none',
        undefined
      );
    });

    it('should successfully parse a resume with job analysis', async () => {
      const mockResult = {
        contactInfo: {
          name: 'Jane Smith',
          email: '<EMAIL>',
          phone: '************',
          location: 'New York, NY',
          linkedin: 'linkedin.com/in/janesmith'
        },
        analysis: {
          overall_score: 88,
          match_score: 82,
          experience_years: 7,
          key_skills: ['JavaScript', 'React', 'Node.js', 'Python'],
          matched_skills: ['JavaScript', 'React', 'Node.js'],
          missing_skills: ['Docker'],
          strengths: [
            'Strong full-stack development experience',
            'Proven track record with React and Node.js',
            'Leadership experience managing development teams'
          ],
          concerns: [
            'Limited DevOps experience',
            'No direct experience with containerization'
          ],
          recommendation: 'INTERVIEW',
          detailed_feedback: 'This candidate demonstrates strong qualifications for the role. Their experience with JavaScript, React, and Node.js aligns well with our requirements.',
          interview_questions: [
            'Can you walk me through your experience with React and state management?',
            'How did you handle scaling challenges in your previous Node.js projects?',
            'Tell me about a time you led a development team through a complex project?'
          ]
        },
        extractedText: 'Jane Smith\nSenior Software Engineer...',
        method: 'OpenAI + TypeScript full analysis',
        hasJobAnalysis: true
      };

      mockParseDocument.mockResolvedValue(mockResult);

      const jobDescription = 'We are looking for a Senior Full Stack Developer with experience in JavaScript, React, Node.js, and Docker.';

      const response = await request(app)
        .post('/test-parse')
        .field('jobDescription', jobDescription)
        .attach('resume', Buffer.from('Jane Smith\nSenior Software Engineer\<EMAIL>'), 'resume.txt')
        .expect(200);

      expect(response.body).toEqual(mockResult);
      expect(mockParseDocument).toHaveBeenCalledWith(
        expect.any(String),
        'resume.txt',
        'text/plain',
        'none',
        jobDescription
      );
    });

    it('should handle PDF files correctly', async () => {
      const mockResult = {
        contactInfo: {
          name: 'Bob Johnson',
          email: '<EMAIL>',
          phone: null,
          location: null,
          linkedin: null
        },
        extractedText: 'Bob Johnson\nData Scientist\<EMAIL>',
        method: 'TypeScript regex parsing',
        hasJobAnalysis: false
      };

      mockParseDocument.mockResolvedValue(mockResult);

      // Create a minimal PDF-like buffer (not a real PDF, just for testing)
      const pdfBuffer = Buffer.from('%PDF-1.4\nBob Johnson\nData Scientist\<EMAIL>');

      const response = await request(app)
        .post('/test-parse')
        .attach('resume', pdfBuffer, 'resume.pdf')
        .set('Content-Type', 'multipart/form-data')
        .expect(200);

      expect(response.body).toEqual(mockResult);
      expect(mockParseDocument).toHaveBeenCalledWith(
        expect.any(String),
        'resume.pdf',
        'application/pdf',
        'none',
        undefined
      );
    });

    it('should handle DOCX files correctly', async () => {
      const mockResult = {
        contactInfo: {
          name: 'Alice Wilson',
          email: '<EMAIL>',
          phone: '************',
          location: 'Boston, MA',
          linkedin: 'linkedin.com/in/alicewilson'
        },
        extractedText: 'Alice Wilson\nProduct Manager\<EMAIL>\n************\nBoston, MA',
        method: 'TypeScript regex parsing',
        hasJobAnalysis: false
      };

      mockParseDocument.mockResolvedValue(mockResult);

      // Create a minimal DOCX-like buffer (not a real DOCX, just for testing)
      const docxBuffer = Buffer.from('PK\x03\x04Alice Wilson\nProduct Manager\<EMAIL>');

      const response = await request(app)
        .post('/test-parse')
        .attach('resume', docxBuffer, 'resume.docx')
        .expect(200);

      expect(response.body).toEqual(mockResult);
      expect(mockParseDocument).toHaveBeenCalledWith(
        expect.any(String),
        'resume.docx',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'none',
        undefined
      );
    });

    it('should return 400 when no file is uploaded', async () => {
      const response = await request(app)
        .post('/test-parse')
        .expect(400);

      expect(response.body).toEqual({ error: 'No file uploaded' });
    });

    it('should handle parser errors gracefully', async () => {
      mockParseDocument.mockRejectedValue(new Error('Parser failed'));

      const response = await request(app)
        .post('/test-parse')
        .attach('resume', Buffer.from('test content'), 'resume.txt')
        .expect(500);

      expect(response.body).toEqual({ error: 'Parser failed' });
    });

    it('should use fallback when parser returns minimal data', async () => {
      const mockResult = {
        contactInfo: {
          name: null,
          email: null,
          phone: null,
          location: null,
          linkedin: null
        },
        extractedText: 'Failed to extract text from corrupted.pdf',
        method: 'Failed extraction',
        hasJobAnalysis: false
      };

      mockParseDocument.mockResolvedValue(mockResult);

      const response = await request(app)
        .post('/test-parse')
        .attach('resume', Buffer.from('corrupted data'), 'corrupted.pdf')
        .expect(200);

      expect(response.body).toEqual(mockResult);
    });

    it('should handle large files efficiently', async () => {
      const mockResult = {
        contactInfo: {
          name: 'Large File User',
          email: '<EMAIL>',
          phone: null,
          location: null,
          linkedin: null
        },
        extractedText: 'Large file content truncated...',
        method: 'TypeScript regex parsing',
        hasJobAnalysis: false
      };

      mockParseDocument.mockResolvedValue(mockResult);

      // Create a large buffer (1MB)
      const largeBuffer = Buffer.alloc(1024 * 1024, 'Large File User\<EMAIL>\n');

      const response = await request(app)
        .post('/test-parse')
        .attach('resume', largeBuffer, 'large-resume.txt')
        .expect(200);

      expect(response.body).toEqual(mockResult);
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed base64 content', async () => {
      mockParseDocument.mockResolvedValue({
        contactInfo: {
          name: null,
          email: null,
          phone: null,
          location: null,
          linkedin: null
        },
        extractedText: 'Error processing malformed.txt',
        method: 'TypeScript error fallback',
        hasJobAnalysis: false
      });

      const response = await request(app)
        .post('/test-parse')
        .attach('resume', Buffer.from('malformed content'), 'malformed.txt')
        .expect(200);

      expect(response.body.method).toBe('TypeScript error fallback');
    });

    it('should handle network timeouts gracefully', async () => {
      mockParseDocument.mockImplementation(() => 
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), 100)
        )
      );

      const response = await request(app)
        .post('/test-parse')
        .attach('resume', Buffer.from('test content'), 'resume.txt')
        .expect(500);

      expect(response.body.error).toBe('Request timeout');
    });
  });
});
