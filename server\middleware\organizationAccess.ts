import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../auth';

/**
 * Organization Access Control Middleware
 * Ensures users can only access data from their own organization
 */
export interface OrganizationScopedRequest extends AuthenticatedRequest {
  organizationId: string;
}

/**
 * Middleware to enforce organization-scoped access control
 * Only allows access to data within user's organization
 */
export function requireOrganizationAccess(req: OrganizationScopedRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // Super admin can access all organizations
  if (req.user.role === 'super_admin') {
    // For super admin, allow access but log the access
    console.log(`Super admin ${req.user.email} accessing organization data`);
    return next();
  }

  // Regular users must have an organization
  if (!req.user.organizationId) {
    return res.status(403).json({ error: 'User must be associated with an organization' });
  }

  // Set organizationId on request for downstream use
  req.organizationId = req.user.organizationId;
  
  next();
}

/**
 * Middleware to validate organization access for specific resource
 * Checks if the requested resource belongs to user's organization
 */
export function validateResourceAccess(resourceOrgIdField: string = 'organizationId') {
  return (req: OrganizationScopedRequest, res: Response, next: NextFunction) => {
    // Skip for super admin
    if (req.user?.role === 'super_admin') {
      return next();
    }

    // For regular users, ensure they can only access their org's resources
    const requestedOrgId = req.body[resourceOrgIdField] || req.query[resourceOrgIdField];
    
    if (requestedOrgId && requestedOrgId !== req.user?.organizationId) {
      return res.status(403).json({ 
        error: 'Access denied: Cannot access resources from other organizations',
        details: 'You can only access data within your organization'
      });
    }
    
    next();
  };
}

/**
 * Role-based permission middleware
 * Restricts access based on user roles within organization
 */
export function requireRole(allowedRoles: string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Super admin bypasses all role restrictions
    if (req.user.role === 'super_admin') {
      return next();
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        details: `This action requires one of the following roles: ${allowedRoles.join(', ')}`
      });
    }

    next();
  };
}

/**
 * Admin-only access middleware
 * Only allows organization admins and super admins
 */
export function requireAdmin(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  if (!req.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  const adminRoles = ['super_admin', 'admin'];
  if (!adminRoles.includes(req.user.role)) {
    return res.status(403).json({ 
      error: 'Admin access required',
      details: 'This action requires administrator privileges'
    });
  }

  next();
}