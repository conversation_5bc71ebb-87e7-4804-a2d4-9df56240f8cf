import WebSocket, { WebSocketServer } from 'ws';
import { Server } from 'http';

export function setupElevenLabsMediaStream(server: Server) {
  console.log('🔧 Setting up ElevenLabs WebSocket server...');
  
  const wss = new WebSocketServer({ 
    server, 
    path: '/elevenlabs-stream',
    perMessageDeflate: false,
    clientTracking: true
    // Removed verifyClient to allow default handshake behavior
  });

  console.log('🎧 ElevenLabs Media Stream WebSocket server initialized at /elevenlabs-stream');
  
  // Log WebSocket server events
  wss.on('error', (error) => {
    console.error('❌ WebSocket Server Error:', error);
  });
  
  wss.on('headers', (headers, request) => {
    console.log('📋 WebSocket upgrade headers being sent');
    console.log('📋 Headers:', headers.join('\r\n'));
  });
  
  wss.on('error', (error) => {
    console.error('❌ WebSocket Server Error during connection:', error);
  });
  
  // Add connection debugging
  wss.on('listening', () => {
    console.log('🎧 WebSocket server is actively listening');
  });
  
  // Monitor client count
  setInterval(() => {
    const clientCount = wss.clients.size;
    if (clientCount > 0) {
      console.log(`📊 Active WebSocket connections: ${clientCount}`);
    }
  }, 5000);
  
  console.log('🔊 WebSocket server is listening for connections on path /elevenlabs-stream');

  wss.on('connection', (ws: WebSocket, req) => {
    console.log('🎉🎉🎉 ELEVENLABS WEBSOCKET CONNECTION SUCCESS! 🎉🎉🎉');
    console.log('📞 Connected from:', req.url);
    console.log('📞 Headers:', req.headers);
    console.log('👥 Active connections:', wss.clients.size);
    
    // Send immediate test message
    try {
      ws.send(JSON.stringify({ event: 'connected', message: 'WebSocket ready' }));
      console.log('✅ Initial message sent to Twilio');
    } catch (error) {
      console.error('❌ Error sending initial message:', error);
    }
    
    let elevenLabsWs: WebSocket | null = null;
    let streamSid: string | null = null;

    // Handle messages from Twilio
    ws.on('message', async (message: Buffer) => {
      try {
        const data = JSON.parse(message.toString());
        
        switch (data.event) {
          case 'connected':
            console.log('🔗 Twilio stream connected');
            break;
            
          case 'start':
            console.log('🎬 Twilio stream started with params:', data.start);
            streamSid = data.start.streamSid;
            
            // Extract call context from custom parameters
            const customParams = data.start.customParameters || {};
            const candidateName = customParams.candidate_name || 'candidate';
            const jobTitle = customParams.job_title || 'the position';
            const companyName = customParams.company_name || 'our company';
            
            console.log(`🤖 Connecting to ElevenLabs Conversational AI...`);
            console.log(`Context: ${candidateName} for ${jobTitle} at ${companyName}`);
            
            // CORRECT ElevenLabs WebSocket URL with signed authentication
            const agentId = customParams.agent_id || process.env.ELEVENLABS_AGENT_ID;
            const apiKey = process.env.ELEVENLABS_API_KEY;
            
            if (!agentId || !apiKey) {
              console.error('❌ Missing ELEVENLABS_AGENT_ID or ELEVENLABS_API_KEY');
              return;
            }
            
            // Create signed URL for ElevenLabs WebSocket
            const signedUrl = await getSignedElevenLabsUrl(agentId, apiKey);
            
            // Connect to ElevenLabs Conversational AI WebSocket
            elevenLabsWs = new WebSocket(signedUrl);
            
            elevenLabsWs.on('open', () => {
              console.log('✅ Connected to ElevenLabs Conversational AI');
              
              // Send initial configuration
              const initMessage = {
                type: 'conversation_initiation_client_data',
                conversation_initiation_client_data: {
                  conversation_config_override: {
                    agent: {
                      prompt: {
                        prompt: `You are Sarah, a warm and professional HR recruiter. You're calling ${candidateName} about the ${jobTitle} position at ${companyName}.
                        
Your goals:
1. Greet warmly and confirm you're speaking with the right person
2. Gauge their interest in the role
3. Ask about their relevant experience
4. Discuss availability for next steps
5. Answer any questions they have

Keep responses natural, conversational, and under 30 words. Be warm, professional, and respectful of their time.`
                      }
                      // Remove first_message to prevent duplicate - let ElevenLabs agent handle introduction
                    }
                  }
                }
              };
              elevenLabsWs.send(JSON.stringify(initMessage));
            });
            
            elevenLabsWs.on('message', (elevenLabsMessage: Buffer) => {
              try {
                const elevenLabsData = JSON.parse(elevenLabsMessage.toString());
                
                // Handle different message types from ElevenLabs
                switch (elevenLabsData.type) {
                  case 'audio':
                    if (elevenLabsData.audio?.chunk) {
                      // Forward ElevenLabs audio to Twilio
                      const twilioMessage = {
                        event: 'media',
                        streamSid: streamSid,
                        media: {
                          payload: elevenLabsData.audio.chunk
                        }
                      };
                      ws.send(JSON.stringify(twilioMessage));
                    }
                    break;
                    
                  case 'conversation_initiation_metadata':
                    console.log('🎯 ElevenLabs conversation metadata:', elevenLabsData);
                    break;
                    
                  case 'interruption':
                    console.log('🔊 User interrupted, clearing audio buffer');
                    // Send clear message to Twilio
                    ws.send(JSON.stringify({ event: 'clear', streamSid }));
                    break;
                    
                  case 'ping':
                    // Respond to ping to keep connection alive
                    elevenLabsWs.send(JSON.stringify({ type: 'pong' }));
                    break;
                    
                  default:
                    console.log('📨 ElevenLabs message:', elevenLabsData.type);
                }
              } catch (error) {
                console.error('❌ Error processing ElevenLabs message:', error);
              }
            });
            
            elevenLabsWs.on('error', (error) => {
              console.error('❌ ElevenLabs WebSocket error:', error);
            });
            
            elevenLabsWs.on('close', () => {
              console.log('🔌 ElevenLabs WebSocket closed');
            });
            break;
            
          case 'media':
            // Forward Twilio audio to ElevenLabs
            if (elevenLabsWs && elevenLabsWs.readyState === WebSocket.OPEN && data.media?.payload) {
              const elevenLabsMessage = {
                type: 'audio',
                audio: {
                  chunk: data.media.payload,
                  encoding: 'pcm_mulaw',
                  sample_rate: 8000
                }
              };
              elevenLabsWs.send(JSON.stringify(elevenLabsMessage));
            }
            break;
            
          case 'stop':
            console.log('🛑 Twilio stream stopped');
            if (elevenLabsWs) {
              elevenLabsWs.close();
            }
            break;
        }
      } catch (error) {
        console.error('❌ Error processing Twilio message:', error);
      }
    });

    ws.on('close', () => {
      console.log('📞 Twilio media stream connection closed');
      if (elevenLabsWs) {
        elevenLabsWs.close();
      }
    });

    ws.on('error', (error) => {
      console.error('❌ Twilio WebSocket error:', error);
      if (elevenLabsWs) {
        elevenLabsWs.close();
      }
    });
  });

  return wss;
}

// Helper function to create signed URL for ElevenLabs WebSocket
async function getSignedElevenLabsUrl(agentId: string, apiKey: string): Promise<string> {
  try {
    // Create signed URL request
    const response = await fetch(`https://api.elevenlabs.io/v1/convai/conversation/get_signed_url`, {
      method: 'POST',
      headers: {
        'xi-api-key': apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        agent_id: agentId
      })
    });
    
    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to get signed URL: ${error}`);
    }
    
    const data = await response.json();
    return data.signed_url;
  } catch (error) {
    console.error('❌ Failed to get signed ElevenLabs URL:', error);
    // Fallback to direct WebSocket URL
    return `wss://api.elevenlabs.io/v1/convai/conversation?agent_id=${agentId}`;
  }
}