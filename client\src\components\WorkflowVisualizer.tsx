import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FileText, 
  Search, 
  CheckCircle, 
  Calendar, 
  UserCheck, 
  Users,
  ArrowRight,
  Clock,
  TrendingUp
} from 'lucide-react';

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  count: number;
  isActive: boolean;
}

interface CandidateFlow {
  id: string;
  name: string;
  avatar: string;
  position: string;
  currentStep: number;
  progress: number;
}

export default function WorkflowVisualizer() {
  const [activeStep, setActiveStep] = useState(0);
  const [candidateFlows, setCandidateFlows] = useState<CandidateFlow[]>([
    {
      id: '1',
      name: '<PERSON>',
      avatar: 'SJ',
      position: 'Senior Developer',
      currentStep: 2,
      progress: 40
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: 'MC',
      position: 'Product Manager',
      currentStep: 4,
      progress: 80
    },
    {
      id: '3',
      name: '<PERSON>',
      avatar: 'ER',
      position: 'UX Designer',
      currentStep: 1,
      progress: 20
    }
  ]);

  const workflowSteps: WorkflowStep[] = [
    {
      id: 'screening',
      title: 'Resume Screening',
      description: 'AI-powered resume analysis',
      icon: <FileText className="w-5 h-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      count: 47,
      isActive: activeStep === 0
    },
    {
      id: 'review',
      title: 'Initial Review',
      description: 'HR team evaluation',
      icon: <Search className="w-5 h-5" />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      count: 23,
      isActive: activeStep === 1
    },
    {
      id: 'approved',
      title: 'Approved',
      description: 'Candidate approved for interview',
      icon: <CheckCircle className="w-5 h-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      count: 12,
      isActive: activeStep === 2
    },
    {
      id: 'interview',
      title: 'Interview',
      description: 'Scheduled interviews',
      icon: <Calendar className="w-5 h-5" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      count: 8,
      isActive: activeStep === 3
    },
    {
      id: 'final',
      title: 'Final Decision',
      description: 'Hiring decision made',
      icon: <UserCheck className="w-5 h-5" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      count: 5,
      isActive: activeStep === 4
    },
    {
      id: 'hired',
      title: 'Hired',
      description: 'Successfully onboarded',
      icon: <Users className="w-5 h-5" />,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50',
      count: 3,
      isActive: activeStep === 5
    }
  ];

  // Auto-advance through workflow steps
  useEffect(() => {
    const interval = setInterval(() => {
      setActiveStep((prev) => (prev + 1) % workflowSteps.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [workflowSteps.length]);

  // Animate candidate flows with looping progress
  useEffect(() => {
    const interval = setInterval(() => {
      setCandidateFlows(prev => 
        prev.map(flow => {
          const increment = Math.random() * 8 + 2; // 2-10% increment
          const newProgress = flow.progress + increment;
          
          // Reset to 0 when reaching 100% and move to next step
          if (newProgress >= 100) {
            return {
              ...flow,
              progress: 0,
              currentStep: (flow.currentStep + 1) % workflowSteps.length
            };
          }
          
          return {
            ...flow,
            progress: newProgress
          };
        })
      );
    }, 1500);
    return () => clearInterval(interval);
  }, [workflowSteps.length]);

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-[#7b5cff]" />
            Recruitment Workflow
          </h3>
          <p className="text-sm text-gray-500">Real-time candidate journey visualization</p>
        </div>
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-xs text-gray-500">Live</span>
          </div>
        </div>
      </div>

      {/* Workflow Steps */}
      <div className="relative mb-8">
        <div className="flex items-center justify-between">
          {workflowSteps.map((step, index) => (
            <div key={step.id} className="flex flex-col items-center relative">
              {/* Step Circle with enhanced graphics */}
              <motion.div
                className={`relative w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 ${
                  step.isActive 
                    ? `${step.bgColor} ${step.color} shadow-lg scale-110` 
                    : 'bg-gray-100 text-gray-400'
                }`}
                animate={{
                  scale: step.isActive ? 1.15 : 1,
                  boxShadow: step.isActive ? '0 12px 30px rgba(123, 92, 255, 0.4)' : '0 0 0 rgba(0,0,0,0)'
                }}
                transition={{ duration: 0.3 }}
              >
                {/* Animated ring for active step */}
                {step.isActive && (
                  <motion.div
                    className="absolute inset-0 rounded-full border-2 border-[#7b5cff]"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [1, 0.3, 1]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                )}
                
                {/* Pulsing dot for active step */}
                {step.isActive && (
                  <motion.div
                    className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [1, 0.7, 1]
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                )}
                
                {/* Icon with gradient background */}
                <div className={`relative z-10 ${step.isActive ? 'text-white' : ''}`}>
                  {step.icon}
                </div>
                
                {/* Background gradient for active step */}
                {step.isActive && (
                  <div className="absolute inset-1 rounded-full bg-gradient-to-br from-[#7b5cff] to-[#a259e6] -z-10" />
                )}
              </motion.div>
              
              {/* Step Info */}
              <div className="mt-3 text-center">
                <div className={`text-sm font-medium transition-colors ${
                  step.isActive ? step.color : 'text-gray-600'
                }`}>
                  {step.title}
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {step.description}
                </div>
                <motion.div 
                  className={`text-xs font-bold mt-1 ${step.color}`}
                  animate={{ scale: step.isActive ? 1.1 : 1 }}
                >
                  {step.count}
                </motion.div>
              </div>
              
              {/* Enhanced Connection Line */}
              {index < workflowSteps.length - 1 && (
                <div className="absolute top-8 left-16 w-full h-1 bg-gray-200 rounded-full -z-10">
                  <motion.div
                    className="h-full bg-gradient-to-r from-[#7b5cff] to-[#a259e6] rounded-full relative"
                    initial={{ width: '0%' }}
                    animate={{ 
                      width: step.isActive ? '100%' : '0%'
                    }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                  >
                    {/* Animated dots moving along the line */}
                    {step.isActive && (
                      <motion.div
                        className="absolute top-0 w-2 h-1 bg-white rounded-full"
                        animate={{
                          x: ['-8px', '100%']
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: "linear"
                        }}
                      />
                    )}
                  </motion.div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Animated Candidates */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-700 mb-3">Active Candidates</h4>
        <AnimatePresence>
          {candidateFlows.map((candidate, index) => (
            <motion.div
              key={candidate.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gray-50 rounded-lg p-4 border border-gray-100"
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-[#7b5cff] text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {candidate.avatar}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{candidate.name}</div>
                    <div className="text-xs text-gray-500">{candidate.position}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-xs text-gray-500">
                    {workflowSteps[candidate.currentStep]?.title}
                  </span>
                </div>
              </div>
              
              {/* Enhanced Progress Bar */}
              <div className="w-full bg-gray-200 rounded-full h-3 relative overflow-hidden">
                <motion.div
                  className="bg-gradient-to-r from-[#7b5cff] to-[#a259e6] h-full rounded-full relative"
                  initial={{ width: '0%' }}
                  animate={{ width: `${candidate.progress}%` }}
                  transition={{ duration: 0.5 }}
                >
                  {/* Animated shimmer effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                    animate={{
                      x: ['-100%', '100%']
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "linear"
                    }}
                  />
                </motion.div>
                
                {/* Progress completion celebration */}
                {candidate.progress >= 95 && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                )}
              </div>
              <div className="text-xs text-gray-500 mt-1 text-right">
                {Math.round(candidate.progress)}% complete
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Workflow Stats */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-lg font-bold text-gray-900">
              {workflowSteps.reduce((sum, step) => sum + step.count, 0)}
            </div>
            <div className="text-xs text-gray-500">Total Candidates</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-600">
              {Math.round((workflowSteps.slice(-2).reduce((sum, step) => sum + step.count, 0) / 
                workflowSteps.reduce((sum, step) => sum + step.count, 0)) * 100)}%
            </div>
            <div className="text-xs text-gray-500">Success Rate</div>
          </div>
          <div>
            <div className="text-lg font-bold text-[#7b5cff]">
              {Math.round(workflowSteps.reduce((sum, step) => sum + step.count, 0) / 6)}
            </div>
            <div className="text-xs text-gray-500">Avg. per Stage</div>
          </div>
        </div>
      </div>
    </div>
  );
}