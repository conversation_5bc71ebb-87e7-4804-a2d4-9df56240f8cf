/**
 * Accessibility utilities for screen reader support and keyboard navigation
 */

export const ARIA_LABELS = {
  // Navigation
  mainNavigation: 'Main navigation',
  userMenu: 'User menu',
  skipToContent: 'Skip to main content',
  
  // Buttons
  closeDialog: 'Close dialog',
  openMenu: 'Open menu',
  search: 'Search',
  filter: 'Filter',
  sort: 'Sort',
  refresh: 'Refresh',
  
  // Forms
  required: 'Required field',
  optional: 'Optional field',
  showPassword: 'Show password',
  hidePassword: 'Hide password',
  
  // Status
  loading: 'Loading',
  error: 'Error',
  success: 'Success',
  warning: 'Warning',
  
  // Tables
  sortAscending: 'Sort ascending',
  sortDescending: 'Sort descending',
  
  // Candidates
  candidateCard: 'Candidate card',
  candidateDetails: 'Candidate details',
  approveCandidate: 'Approve candidate',
  rejectCandidate: 'Reject candidate',
  scheduleInterview: 'Schedule interview',
  
  // Job postings
  jobPostingCard: 'Job posting card',
  jobDetails: 'Job details',
  viewCandidates: 'View candidates',
  
  // Pagination
  pagination: 'Pagination',
  previousPage: 'Previous page',
  nextPage: 'Next page',
  pageNumber: 'Page {0}',
  currentPage: 'Current page, page {0}',
} as const;

export const ARIA_DESCRIPTIONS = {
  // Navigation
  sidebarNavigation: 'Use arrow keys to navigate between menu items',
  tabNavigation: 'Use arrow keys to navigate between tabs',
  
  // Forms
  passwordRequirements: 'Password must be at least 8 characters with uppercase, lowercase, number, and special character',
  emailFormat: 'Enter a valid email address',
  
  // Status indicators
  loadingSpinner: 'Content is loading, please wait',
  errorMessage: 'An error occurred, please try again',
  
  // Tables
  sortableColumn: 'Column header, click to sort',
  candidateTable: 'Table containing candidate information. Use arrow keys to navigate.',
  
  // Dialogs
  confirmationDialog: 'Confirmation dialog. Press Escape to cancel.',
  
  // File uploads
  fileUpload: 'Drop files here or click to browse. Supported formats: PDF, DOC, DOCX',
} as const;

export const KEYBOARD_SHORTCUTS = {
  ESCAPE: 'Escape',
  ENTER: 'Enter',
  SPACE: ' ',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  TAB: 'Tab',
  HOME: 'Home',
  END: 'End',
} as const;

/**
 * Announce message to screen readers
 */
export const announceToScreenReader = (message: string, priority: 'polite' | 'assertive' = 'polite') => {
  const announcement = document.createElement('div');
  announcement.setAttribute('aria-live', priority);
  announcement.setAttribute('aria-atomic', 'true');
  announcement.setAttribute('class', 'sr-only');
  announcement.textContent = message;
  
  document.body.appendChild(announcement);
  
  setTimeout(() => {
    document.body.removeChild(announcement);
  }, 1000);
};

/**
 * Focus management utilities
 */
export const focusManagement = {
  /**
   * Focus the first focusable element in a container
   */
  focusFirst: (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    if (firstElement) {
      firstElement.focus();
    }
  },

  /**
   * Focus the last focusable element in a container
   */
  focusLast: (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    if (lastElement) {
      lastElement.focus();
    }
  },

  /**
   * Trap focus within a container (for modals/dialogs)
   */
  trapFocus: (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        if (e.shiftKey) {
          if (document.activeElement === firstElement) {
            e.preventDefault();
            lastElement.focus();
          }
        } else {
          if (document.activeElement === lastElement) {
            e.preventDefault();
            firstElement.focus();
          }
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    
    return () => {
      container.removeEventListener('keydown', handleKeyDown);
    };
  },
};

/**
 * Generate accessible unique IDs
 */
export const generateAccessibleId = (prefix: string = 'accessible'): string => {
  return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * Format status for screen readers
 */
export const formatStatusForScreenReader = (status: string): string => {
  const statusMap: Record<string, string> = {
    'pending_review': 'Pending review',
    'approved_for_interview': 'Approved for interview',
    'rejected': 'Rejected',
    'hired': 'Hired',
    'dismissed': 'Dismissed',
    'active': 'Active',
    'inactive': 'Inactive',
  };
  
  return statusMap[status] || status;
};

/**
 * Format date for screen readers
 */
export const formatDateForScreenReader = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

/**
 * Create accessible table headers
 */
export const createAccessibleTableHeaders = (columns: Array<{
  key: string;
  label: string;
  sortable?: boolean;
  sortDirection?: 'asc' | 'desc' | null;
}>) => {
  return columns.map(column => ({
    ...column,
    id: generateAccessibleId(`column-${column.key}`),
    ariaLabel: column.sortable 
      ? `${column.label}, sortable column`
      : column.label,
    ariaSort: column.sortable 
      ? (column.sortDirection || 'none') 
      : undefined,
  }));
};

/**
 * Keyboard navigation handler for lists/grids
 */
export const createKeyboardNavigationHandler = (
  items: HTMLElement[],
  currentIndex: number,
  setCurrentIndex: (index: number) => void,
  onSelect?: (index: number) => void
) => {
  return (e: KeyboardEvent) => {
    switch (e.key) {
      case KEYBOARD_SHORTCUTS.ARROW_DOWN:
        e.preventDefault();
        setCurrentIndex(Math.min(currentIndex + 1, items.length - 1));
        break;
      case KEYBOARD_SHORTCUTS.ARROW_UP:
        e.preventDefault();
        setCurrentIndex(Math.max(currentIndex - 1, 0));
        break;
      case KEYBOARD_SHORTCUTS.HOME:
        e.preventDefault();
        setCurrentIndex(0);
        break;
      case KEYBOARD_SHORTCUTS.END:
        e.preventDefault();
        setCurrentIndex(items.length - 1);
        break;
      case KEYBOARD_SHORTCUTS.ENTER:
      case KEYBOARD_SHORTCUTS.SPACE:
        e.preventDefault();
        onSelect?.(currentIndex);
        break;
    }
  };
};