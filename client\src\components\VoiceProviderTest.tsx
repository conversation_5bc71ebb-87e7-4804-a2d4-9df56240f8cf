import React from 'react';
import { Button } from "@/components/ui/button";
import { useQuery } from '@tanstack/react-query';

export default function VoiceProviderTest() {
  const { data: providers, isLoading, error } = useQuery({
    queryKey: ['/api/voice-providers/providers'],
    queryFn: async () => {
      const response = await fetch('/api/voice-providers/providers', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('Response status:', response.status);
      console.log('Response headers:', response.headers);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`Failed to fetch providers: ${response.status} ${errorText}`);
      }
      
      const data = await response.json();
      console.log('Providers data:', data);
      return data;
    }
  });

  const testAuth = async () => {
    try {
      const response = await fetch('/api/auth/user', {
        credentials: 'include'
      });
      
      console.log('Auth test response:', response.status);
      
      if (response.ok) {
        const user = await response.json();
        console.log('Current user:', user);
      } else {
        const error = await response.text();
        console.error('Auth error:', error);
      }
    } catch (error) {
      console.error('Auth test failed:', error);
    }
  };

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="font-medium mb-4">Voice Provider Test</h3>
      
      <div className="space-y-2 mb-4">
        <Button onClick={testAuth} variant="outline">Test Authentication</Button>
      </div>
      
      <div>
        <h4 className="font-medium mb-2">Providers:</h4>
        {isLoading && <p>Loading...</p>}
        {error && <p className="text-red-600">Error: {error.message}</p>}
        {providers && (
          <div className="space-y-2">
            {providers.map((provider: any) => (
              <div key={provider.name} className="p-2 bg-gray-50 rounded">
                <strong>{provider.name}</strong> - {provider.available ? 'Available' : 'Unavailable'}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}