import { Router } from 'express';
import { linkedinService } from '../services/linkedinService';
import axios from 'axios';

const router = Router();

/**
 * @swagger
 * /linkedin-test/profile:
 *   get:
 *     summary: Test LinkedIn profile access
 *     description: Test LinkedIn API access with current token to verify authentication
 *     tags: [LinkedIn Test]
 *     responses:
 *       200:
 *         description: LinkedIn API access successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 profile:
 *                   type: object
 *                 message:
 *                   type: string
 *       400:
 *         description: LinkedIn access token not configured
 *       500:
 *         description: LinkedIn API test failed
 */
router.get('/profile', async (req, res) => {
  try {
    const accessToken = process.env.LINKEDIN_ACCESS_TOKEN;
    
    if (!accessToken) {
      return res.status(400).json({ error: 'LinkedIn access token not configured' });
    }

    console.log('Testing LinkedIn profile access...');
    
    const response = await axios.get('https://api.linkedin.com/v2/me', {
      headers: {
        'Authorization': `Bear<PERSON> ${accessToken}`,
        'Content-Type': 'application/json',
        'X-Restli-Protocol-Version': '2.0.0'
      }
    });

    console.log('LinkedIn profile response:', response.data);
    
    res.json({
      success: true,
      profile: response.data,
      message: 'LinkedIn API access successful'
    });
    
  } catch (error: any) {
    console.error('LinkedIn profile test failed:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    
    res.status(500).json({
      error: 'LinkedIn API test failed',
      details: error.response?.data || error.message,
      status: error.response?.status
    });
  }
});

// Test people search with current scopes
router.get('/search-test', async (req, res) => {
  try {
    const searchResult = await linkedinService.searchPeople({
      keywords: 'software engineer',
      count: 5
    });
    
    res.json({
      success: true,
      profiles: searchResult,
      count: searchResult.length
    });
    
  } catch (error: any) {
    console.error('LinkedIn search test failed:', error);
    
    res.status(500).json({
      error: 'LinkedIn search test failed',
      details: error.message
    });
  }
});

// Test different LinkedIn API endpoints
router.get('/endpoints', async (req, res) => {
  const accessToken = process.env.LINKEDIN_ACCESS_TOKEN;
  const endpoints = [
    { name: 'Profile', url: 'https://api.linkedin.com/v2/me' },
    { name: 'Profile with projection', url: 'https://api.linkedin.com/v2/me?projection=(id,firstName,lastName,headline)' },
    { name: 'Email addresses', url: 'https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))' },
    { name: 'People search', url: 'https://api.linkedin.com/v2/people?q=firstName&firstName=John' }
  ];
  
  const results = [];
  
  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(endpoint.url, {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'X-Restli-Protocol-Version': '2.0.0'
        }
      });
      
      results.push({
        name: endpoint.name,
        status: 'success',
        data: response.data
      });
      
    } catch (error: any) {
      results.push({
        name: endpoint.name,
        status: 'error',
        error: error.response?.data || error.message,
        statusCode: error.response?.status
      });
    }
  }
  
  res.json({ results });
});

export { router as linkedinTestRouter };