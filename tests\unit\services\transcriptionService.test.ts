import { transcriptionService } from '../../../server/services/transcriptionService';
import fs from 'fs';
import path from 'path';

// Mock fs and OpenAI
jest.mock('fs');
jest.mock('openai');

const mockFs = fs as jest.Mocked<typeof fs>;

describe('TranscriptionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFs.existsSync.mockReturnValue(true);
    mockFs.statSync.mockReturnValue({
      size: 1024 * 1024, // 1MB
      mtime: new Date()
    } as any);
  });

  describe('transcribeAudio', () => {
    it('should transcribe audio file successfully', async () => {
      const mockAudioPath = '/path/to/audio.wav';
      
      const result = await transcriptionService.transcribeAudio(mockAudioPath);

      expect(result).toHaveProperty('text');
      expect(result).toHaveProperty('segments');
      expect(result).toHaveProperty('language');
      expect(result).toHaveProperty('duration');
      expect(result.text).toBe('Mock transcription text');
    });

    it('should handle non-existent files', async () => {
      mockFs.existsSync.mockReturnValue(false);
      
      await expect(
        transcriptionService.transcribeAudio('/nonexistent/file.wav')
      ).rejects.toThrow('Audio file not found');
    });

    it('should handle large files by splitting', async () => {
      // Mock large file (30MB)
      mockFs.statSync.mockReturnValue({
        size: 30 * 1024 * 1024,
        mtime: new Date()
      } as any);

      const result = await transcriptionService.transcribeAudio('/path/to/large-audio.wav');

      expect(result).toHaveProperty('text');
      expect(result.text).toBe('Mock transcription text');
    });
  });

  describe('generateInterviewSummary', () => {
    it('should generate summary for software engineer role', async () => {
      const mockTranscription = {
        text: 'This is a mock interview transcript discussing technical skills and problem solving.',
        language: 'en',
        duration: 1800
      };

      const result = await transcriptionService.generateInterviewSummary(
        mockTranscription,
        'Software Engineer'
      );

      expect(result).toHaveProperty('summary');
      expect(result).toHaveProperty('keyPoints');
      expect(result).toHaveProperty('competencyScores');
      expect(result).toHaveProperty('overallRating');
      expect(result).toHaveProperty('recommendations');
      
      expect(result.competencyScores).toHaveProperty('Communication');
      expect(result.overallRating).toBeGreaterThanOrEqual(0);
      expect(result.overallRating).toBeLessThanOrEqual(10);
    });

    it('should generate summary for manager role', async () => {
      const mockTranscription = {
        text: 'This is a mock interview transcript discussing leadership and strategic thinking.',
        language: 'en',
        duration: 1800
      };

      const result = await transcriptionService.generateInterviewSummary(
        mockTranscription,
        'Engineering Manager'
      );

      expect(result).toHaveProperty('summary');
      expect(result.competencyScores).toHaveProperty('Communication');
      expect(result.keyPoints).toBeInstanceOf(Array);
      expect(result.recommendations).toBeInstanceOf(Array);
    });

    it('should handle empty transcription', async () => {
      const mockTranscription = {
        text: '',
        language: 'en',
        duration: 0
      };

      await expect(
        transcriptionService.generateInterviewSummary(mockTranscription, 'Software Engineer')
      ).rejects.toThrow();
    });
  });

  describe('startRecording', () => {
    it('should start recording from audio stream', async () => {
      const mockStream = {
        pipe: jest.fn(),
        on: jest.fn()
      } as any;

      const recordingPath = await transcriptionService.startRecording('test-session', mockStream);

      expect(recordingPath).toContain('test-session');
      expect(recordingPath).toContain('.wav');
      expect(mockStream.pipe).toHaveBeenCalled();
    });
  });

  describe('cleanupOldRecordings', () => {
    it('should clean up old recording files', async () => {
      mockFs.readdirSync.mockReturnValue(['old-recording.wav', 'new-recording.wav'] as any);
      mockFs.statSync.mockImplementation((filePath) => {
        if (filePath.toString().includes('old-recording')) {
          return {
            mtime: new Date(Date.now() - 48 * 60 * 60 * 1000) // 48 hours ago
          } as any;
        }
        return {
          mtime: new Date() // Recent file
        } as any;
      });

      mockFs.unlinkSync.mockImplementation(() => {});

      await transcriptionService.cleanupOldRecordings(24);

      expect(mockFs.unlinkSync).toHaveBeenCalledWith(
        expect.stringContaining('old-recording.wav')
      );
    });

    it('should not delete recent files', async () => {
      mockFs.readdirSync.mockReturnValue(['recent-recording.wav'] as any);
      mockFs.statSync.mockReturnValue({
        mtime: new Date() // Recent file
      } as any);

      await transcriptionService.cleanupOldRecordings(24);

      expect(mockFs.unlinkSync).not.toHaveBeenCalled();
    });
  });

  describe('getCompetenciesForRole', () => {
    it('should return appropriate competencies for software engineer', () => {
      const service = transcriptionService as any;
      const competencies = service.getCompetenciesForRole('Software Engineer');

      expect(competencies).toBeInstanceOf(Array);
      expect(competencies.length).toBeGreaterThan(0);
      
      const competencyNames = competencies.map((c: any) => c.name);
      expect(competencyNames).toContain('Communication');
      expect(competencyNames).toContain('Technical Knowledge');
    });

    it('should return appropriate competencies for manager', () => {
      const service = transcriptionService as any;
      const competencies = service.getCompetenciesForRole('Engineering Manager');

      expect(competencies).toBeInstanceOf(Array);
      
      const competencyNames = competencies.map((c: any) => c.name);
      expect(competencyNames).toContain('Leadership');
      expect(competencyNames).toContain('Strategic Thinking');
    });

    it('should return base competencies for unknown roles', () => {
      const service = transcriptionService as any;
      const competencies = service.getCompetenciesForRole('Unknown Role');

      expect(competencies).toBeInstanceOf(Array);
      expect(competencies.length).toBeGreaterThan(0);
      
      const competencyNames = competencies.map((c: any) => c.name);
      expect(competencyNames).toContain('Communication');
      expect(competencyNames).toContain('Problem Solving');
    });
  });
});
