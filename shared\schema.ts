import { pgTable, text, serial, integer, boolean, uuid, timestamp, pgEnum, jsonb, json, real, unique } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Enums
export const applicationStatusEnum = pgEnum('application_status', [
  'pending', 'screening', 'interview_scheduled', 'interviewed', 'offered', 'hired', 'rejected'
]);

export const interviewStatusEnum = pgEnum('interview_status', [
  'scheduled', 'completed', 'cancelled', 'rescheduled'
]);

// Interview automation enums
export const sdkTypeEnum = pgEnum('sdk_type', [
  'video', 'meeting'
]);

export const interviewRunStatusEnum = pgEnum('interview_run_status', [
  'pending', 'starting', 'in_progress', 'completed', 'failed', 'cancelled'
]);

export const artifactTypeEnum = pgEnum('artifact_type', [
  'audio', 'transcript', 'summary', 'scoring', 'recording'
]);

export const onboardingStatusEnum = pgEnum('onboarding_status', [
  'pending', 'in_progress', 'completed'
]);

export const userRoleEnum = pgEnum('user_role', [
  'super_admin', 'admin', 'hr_manager', 'recruiter', 'hiring_manager'
]);

// Multi-tenant tables
export const organizations = pgTable("organizations", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull(),
  domain: text("domain"),
  isActive: boolean("is_active").default(true),
  // Enhanced organization settings for Phase 2
  allowSelfRegistration: boolean("allow_self_registration").default(true),
  requireApprovalForNewUsers: boolean("require_approval_for_new_users").default(true),
  defaultUserRole: userRoleEnum("default_user_role").default('recruiter'),
  emailDomainRestriction: text("email_domain_restriction"),
  maxUsers: integer("max_users").default(100),
  customBranding: jsonb("custom_branding"), // Store logo, colors, company name
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

// Authentication users table (multi-tenant)
export const authUsers = pgTable("auth_users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: text("email").notNull().unique(),
  fullName: text("full_name").notNull(),
  hashedPassword: text("hashed_password").notNull(),
  role: userRoleEnum("role").default('recruiter'),
  organizationId: uuid("organization_id").references(() => organizations.id), // Nullable for super_admin
  isActive: boolean("is_active").default(true),
  isApproved: boolean("is_approved").default(false),
  lastLogin: timestamp("last_login", { withTimezone: true }), // Track user activity
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

// Voice call status enum
export const voiceCallStatusEnum = pgEnum('voice_call_status', [
  'scheduled', 'in_progress', 'completed', 'failed', 'cancelled'
]);

// User profiles table (existing ATS structure)
export const users = pgTable("users", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: text("email").notNull().unique(),
  fullName: text("full_name").notNull(),
  hashedPassword: text("hashed_password").notNull(),
  role: userRoleEnum("role").default('recruiter'),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  isActive: boolean("is_active").default(true),
  isApproved: boolean("is_approved").default(false),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

export const profiles = pgTable("profiles", {
  id: uuid("id").primaryKey(),
  authUserId: uuid("auth_user_id").references(() => authUsers.id),
  email: text("email").notNull(),
  fullName: text("full_name"),
  role: userRoleEnum("role").default('recruiter'),
  department: text("department"),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

export const jobPostings = pgTable("job_postings", {
  id: uuid("id").primaryKey().defaultRandom(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  
  // Enhanced detailed job description fields
  detailedDescription: text("detailed_description"), // Comprehensive role description
  responsibilities: text("responsibilities"), // Key duties and responsibilities
  requirements: text("requirements"),
  preferredQualifications: text("preferred_qualifications"), // Nice-to-have qualifications
  benefits: text("benefits"), // Benefits and perks
  companyOverview: text("company_overview"), // About the company
  workEnvironment: text("work_environment"), // Remote/hybrid/onsite details
  growthOpportunities: text("growth_opportunities"), // Career development opportunities
  
  department: text("department"),
  location: text("location"),
  salaryRange: text("salary_range"),
  employmentType: text("employment_type").default('full-time'),
  postedBy: uuid("posted_by").references(() => authUsers.id),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  isActive: boolean("is_active").default(true),
  // External source integration
  externalJobId: text("external_job_id"),
  sourceUrl: text("source_url"),
  sourcePlatform: text("source_platform"), // 'company_website', 'linkedin', 'indeed', 'greenhouse', etc.
  lastSynced: timestamp("last_synced", { withTimezone: true }),
  // Enhanced job details
  skillsRequired: text("skills_required").array(),
  experienceLevel: text("experience_level"), // 'entry', 'mid', 'senior', 'executive'
  educationRequirement: text("education_requirement"),
  keywords: text("keywords").array(),
  // AI-powered matching
  aiGeneratedSummary: text("ai_generated_summary"),
  matchingCriteria: text("matching_criteria"), // JSON string of criteria weights
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

export const candidateStatusEnum = pgEnum('candidate_status', [
  'pending_review',
  'approved_for_interview', 
  'rejected',
  'dismissed',
  'interview_scheduled',
  'interview_completed',
  'hired',
  'not_selected'
]);

export const candidateSourceTypeEnum = pgEnum('candidate_source_type', [
  'direct_application',
  'internal_search',
  'referral',
  'headhunter',
  'job_board'
]);

export const candidates = pgTable("candidates", {
  id: uuid("id").primaryKey().defaultRandom(),
  email: text("email").notNull(),
  fullName: text("full_name").notNull(),
  phone: text("phone"),
  location: text("location"),
  linkedinUrl: text("linkedin_url"),
  resumeUrl: text("resume_url"),
  resumeText: text("resume_text"),
  resumeFileName: text("resume_file_name"),
  resumeFileSize: integer("resume_file_size"),
  skills: text("skills").array(),
  experienceYears: real("experience_years"),
  education: text("education"),
  currentCompany: text("current_company"),
  currentPosition: text("current_position"),
  status: candidateStatusEnum("status").default('pending_review'),
  analysisResult: jsonb("analysis_result"),
  overallScore: integer("overall_score"),
  matchScore: integer("match_score"),
  recommendation: text("recommendation"),
  // Enhanced candidate tracking
  sourceChannel: text("source_channel"), // 'website', 'linkedin', 'referral', 'job_board'
  sourceType: candidateSourceTypeEnum("source_type").default('direct_application'),
  appliedJobId: uuid("applied_job_id").references(() => jobPostings.id),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  tags: text("tags").array(),
  notes: text("notes"),
  aiSummary: text("ai_summary"),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  // CRITICAL FIX: Ensure one candidate record per job application
  uniqueCandidatePerJobApplication: unique("unique_candidate_job_org").on(table.email, table.appliedJobId, table.organizationId),
}));

export const applications = pgTable("applications", {
  id: uuid("id").primaryKey().defaultRandom(),
  candidateId: uuid("candidate_id").references(() => candidates.id, { onDelete: 'cascade' }),
  jobPostingId: uuid("job_posting_id").references(() => jobPostings.id, { onDelete: 'cascade' }),
  status: applicationStatusEnum("status").default('pending'),
  resumeAnalysis: jsonb("resume_analysis"),
  matchScore: integer("match_score"),
  screeningNotes: text("screening_notes"),
  appliedAt: timestamp("applied_at", { withTimezone: true }).defaultNow(),
  reviewedBy: uuid("reviewed_by").references(() => profiles.id),
  reviewedAt: timestamp("reviewed_at", { withTimezone: true }),
});

export const availabilityStatusEnum = pgEnum('availability_status', [
  'requested',
  'received', 
  'confirmed',
  'expired'
]);

export const candidateAvailability = pgTable("candidate_availability", {
  id: uuid("id").primaryKey().defaultRandom(),
  candidateId: uuid("candidate_id").references(() => candidates.id),
  requestedAt: timestamp("requested_at").defaultNow(),
  respondedAt: timestamp("responded_at"),
  status: availabilityStatusEnum("status").default('requested'),
  availableSlots: jsonb("available_slots"),
  selectedSlot: jsonb("selected_slot"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const interviews = pgTable("interviews", {
  id: uuid("id").primaryKey().defaultRandom(),
  candidateId: uuid("candidate_id").references(() => candidates.id),
  availabilityId: uuid("availability_id").references(() => candidateAvailability.id),
  interviewerId: uuid("interviewer_id").references(() => profiles.id),
  scheduledAt: timestamp("scheduled_at", { withTimezone: true }).notNull(),
  durationMinutes: integer("duration_minutes").default(60),
  meetingLink: text("meeting_link"),
  location: text("location"),
  status: interviewStatusEnum("status").default('scheduled'),
  notes: text("notes"),
  calendarEventId: text("calendar_event_id"), // Google Calendar event ID
  feedback: jsonb("feedback"),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

// Interview Automation System Tables
export const agentProfiles = pgTable("agent_profiles", {
  id: uuid("id").primaryKey().defaultRandom(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  name: text("name").notNull(),
  scriptVersion: text("script_version").default('v1.0'),
  rubricJson: jsonb("rubric_json"), // Scoring criteria and competencies
  safetyJson: jsonb("safety_json"), // Safety guidelines and restrictions
  promptTemplate: text("prompt_template"), // ElevenLabs agent prompt
  voiceSettings: jsonb("voice_settings"), // Voice configuration
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

export const interviewsV2 = pgTable("interviews_v2", {
  id: uuid("id").primaryKey().defaultRandom(),
  candidateId: uuid("candidate_id").notNull().references(() => candidates.id),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  role: text("role").notNull(),
  scheduledAt: timestamp("scheduled_at", { withTimezone: true }).notNull(),
  durationMin: integer("duration_min").default(60),
  status: interviewStatusEnum("status").default('scheduled'),
  sdkType: sdkTypeEnum("sdk_type").default('video'),
  roomOrMeetingId: text("room_or_meeting_id"),
  joinUrls: jsonb("join_urls"), // {candidate: "...", host: "..."}
  hostTokenScope: text("host_token_scope"),
  agentProfileId: uuid("agent_profile_id").references(() => agentProfiles.id),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

export const interviewRuns = pgTable("interview_runs", {
  id: uuid("id").primaryKey().defaultRandom(),
  interviewId: uuid("interview_id").notNull().references(() => interviewsV2.id),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  startedAt: timestamp("started_at", { withTimezone: true }),
  endedAt: timestamp("ended_at", { withTimezone: true }),
  status: interviewRunStatusEnum("status").default('pending'),
  metricsJson: jsonb("metrics_json"), // Performance metrics, audio quality, etc.
  botSessionId: text("bot_session_id"), // Puppeteer session identifier
  zoomMeetingId: text("zoom_meeting_id"),
  elevenlabsConversationId: text("elevenlabs_conversation_id"),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
}, (table) => ({
  interviewIdUnique: unique("interview_runs_interview_id_unique").on(table.interviewId),
}));

export const interviewArtifacts = pgTable("interview_artifacts", {
  id: uuid("id").primaryKey().defaultRandom(),
  interviewId: uuid("interview_id").notNull().references(() => interviewsV2.id),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  type: artifactTypeEnum("type").notNull(),
  uri: text("uri").notNull(), // S3/GCS path or local file path
  metaJson: jsonb("meta_json"), // File size, duration, format, etc.
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

export const onboardingTasks = pgTable("onboarding_tasks", {
  id: uuid("id").primaryKey().defaultRandom(),
  applicationId: uuid("application_id").references(() => applications.id, { onDelete: 'cascade' }),
  taskName: text("task_name").notNull(),
  description: text("description"),
  status: onboardingStatusEnum("status").default('pending'),
  dueDate: timestamp("due_date", { withTimezone: true }),
  completedAt: timestamp("completed_at", { withTimezone: true }),
  assignedTo: uuid("assigned_to").references(() => profiles.id),
  workdayIntegrationData: jsonb("workday_integration_data"),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

// Voice agent tables
export const voiceCalls = pgTable("voice_calls", {
  id: uuid("id").primaryKey().defaultRandom(),
  candidateId: uuid("candidate_id").notNull().references(() => candidates.id),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  jobPostingId: uuid("job_posting_id").references(() => jobPostings.id), // CRITICAL FIX: Link calls to specific jobs
  initiatedBy: uuid("initiated_by").references(() => authUsers.id),
  phoneNumber: text("phone_number").notNull(),
  twilioCallSid: text("twilio_call_sid"),
  elevenlabsConversationId: text("elevenlabs_conversation_id"), // 🎯 CRITICAL FIX: Dedicated ElevenLabs conversation ID column for reliable webhook matching
  status: voiceCallStatusEnum("status").default('scheduled'),
  scheduledAt: timestamp("scheduled_at", { withTimezone: true }),
  startedAt: timestamp("started_at", { withTimezone: true }),
  endedAt: timestamp("ended_at", { withTimezone: true }),
  durationSeconds: integer("duration_seconds"),
  callPurpose: text("call_purpose").default('technical_interview_scheduling'),
  recordingUrl: text("recording_url"),
  transcription: text("transcription"),
  conversationNotes: text("conversation_notes"),
  candidateExperience: jsonb("candidate_experience"), // High-level experience discussed
  availabilityDiscussed: jsonb("availability_discussed"), // Availability slots discussed
  followUpRequired: boolean("follow_up_required").default(false),
  followUpNotes: text("follow_up_notes"),
  callRating: integer("call_rating"), // 1-5 rating of call quality
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

export const voiceCallNotes = pgTable("voice_call_notes", {
  id: uuid("id").primaryKey().defaultRandom(),
  callId: uuid("call_id").notNull().references(() => voiceCalls.id),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  noteType: text("note_type").notNull(), // 'experience', 'availability', 'concern', 'positive', 'follow_up'
  content: text("content").notNull(),
  timestamp: timestamp("timestamp", { withTimezone: true }).defaultNow(),
  aiGenerated: boolean("ai_generated").default(false),
  importance: integer("importance").default(3), // 1-5 importance level
  actionRequired: boolean("action_required").default(false),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
});

// Call summaries generated by OpenAI
export const callSummaries = pgTable("call_summaries", {
  id: uuid("id").primaryKey().defaultRandom(),
  callId: uuid("call_id").notNull().references(() => voiceCalls.id),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  summary: text("summary").notNull(), // Brief 4-sentence summary
  nextActions: jsonb("next_actions").notNull(), // Key-value map of actions with metadata
  scheduled: boolean("scheduled").notNull(), // Whether meeting/interview was scheduled
  scheduledTime: timestamp("scheduled_time", { withTimezone: true }), // Agreed time in UTC-4
  scheduledEvidence: text("scheduled_evidence"), // Quote confirming scheduled time
  openQuestions: text("open_questions").array(), // Outstanding questions (0-5)
  riskFlags: text("risk_flags").array(), // Potential risks/concerns (0-5)
  generatedAt: timestamp("generated_at", { withTimezone: true }).defaultNow(),
  modelUsed: text("model_used").default("gpt-5"), // Track which OpenAI model was used
  processingTimeMs: integer("processing_time_ms"), // Track processing duration
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
});

// Real-time collaboration annotations
export const annotationTypeEnum = pgEnum('annotation_type', [
  'comment',
  'highlight', 
  'note',
  'question',
  'approval',
  'concern'
]);

export const annotations = pgTable("annotations", {
  id: uuid("id").primaryKey().defaultRandom(),
  content: text("content").notNull(),
  type: annotationTypeEnum("type").default('comment'),
  entityType: text("entity_type").notNull(), // 'candidate', 'job_posting', 'application'
  entityId: uuid("entity_id").notNull(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  authorId: uuid("author_id").notNull().references(() => authUsers.id),
  parentId: uuid("parent_id"), // For threaded replies - self reference handled separately
  position: jsonb("position"), // For positioned annotations (x, y coordinates, element selector)
  metadata: jsonb("metadata"), // Additional data like selected text, context
  isResolved: boolean("is_resolved").default(false),
  resolvedBy: uuid("resolved_by").references(() => authUsers.id),
  resolvedAt: timestamp("resolved_at", { withTimezone: true }),
  mentions: text("mentions").array(), // User IDs mentioned in the annotation
  attachments: text("attachments").array(), // File URLs or references
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
});

// Real-time collaboration sessions
export const collaborationSessions = pgTable("collaboration_sessions", {
  id: uuid("id").primaryKey().defaultRandom(),
  entityType: text("entity_type").notNull(),
  entityId: uuid("entity_id").notNull(),
  organizationId: uuid("organization_id").notNull().references(() => organizations.id),
  userId: uuid("user_id").notNull().references(() => users.id),
  socketId: text("socket_id").notNull(),
  cursorPosition: jsonb("cursor_position"), // Current cursor/view position
  isActive: boolean("is_active").default(true),
  lastActivity: timestamp("last_activity", { withTimezone: true }).defaultNow(),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
});

// Audit logging for security and compliance
export const auditLogs = pgTable("audit_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  action: text("action").notNull(), // e.g., 'create_candidate', 'delete_job_posting', 'login', 'failed_access'
  resourceType: text("resource_type").notNull(), // e.g., 'candidate', 'job_posting', 'user', 'organization'
  resourceId: uuid("resource_id"), // ID of the affected resource (optional)
  organizationId: uuid("organization_id").references(() => organizations.id), // null for system-level actions
  userId: uuid("user_id").notNull(), // ID of the user performing the action
  userEmail: text("user_email").notNull(),
  userRole: text("user_role").notNull(),
  details: jsonb("details"), // Additional context (request body, query params, etc.)
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  success: boolean("success").notNull(), // Whether the action succeeded
  errorMessage: text("error_message"), // Error details if action failed
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow(),
});

// Insert schemas
export const insertOrganizationSchema = createInsertSchema(organizations);
export const insertAuthUserSchema = createInsertSchema(authUsers).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});
export const insertUserSchema = createInsertSchema(users);
export const insertProfileSchema = createInsertSchema(profiles);

// Voice agent insert schemas
export const insertVoiceCallSchema = createInsertSchema(voiceCalls).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertVoiceCallNoteSchema = createInsertSchema(voiceCallNotes).omit({
  id: true,
  createdAt: true,
});

export const insertCallSummarySchema = createInsertSchema(callSummaries).omit({
  id: true,
  generatedAt: true,
  createdAt: true,
});

export const insertAuditLogSchema = createInsertSchema(auditLogs).omit({
  id: true,
  createdAt: true,
});

// Interview automation insert schemas
export const insertInterviewV2Schema = createInsertSchema(interviewsV2).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertAgentProfileSchema = createInsertSchema(agentProfiles).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertInterviewRunSchema = createInsertSchema(interviewRuns).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertInterviewArtifactSchema = createInsertSchema(interviewArtifacts).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Types
export type Organization = typeof organizations.$inferSelect;
export type InsertOrganization = typeof organizations.$inferInsert;
export type Annotation = typeof annotations.$inferSelect;
export type InsertAnnotation = typeof annotations.$inferInsert;
export type VoiceCall = typeof voiceCalls.$inferSelect;
export type InsertVoiceCall = typeof insertVoiceCallSchema._output;
export type CallSummary = typeof callSummaries.$inferSelect;
export type InsertCallSummary = typeof callSummaries.$inferInsert;
export type CollaborationSession = typeof collaborationSessions.$inferSelect;
export type InsertCollaborationSession = typeof collaborationSessions.$inferInsert;
export type VoiceCallNote = typeof voiceCallNotes.$inferSelect;
export type InsertVoiceCallNote = typeof insertVoiceCallNoteSchema._output;
export type AuthUser = typeof authUsers.$inferSelect;
export type InsertAuthUser = z.infer<typeof insertAuthUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertUser = typeof users.$inferInsert;
export type Profile = typeof profiles.$inferSelect;
export type JobPosting = typeof jobPostings.$inferSelect;
export type Candidate = typeof candidates.$inferSelect;
export type CandidateAvailability = typeof candidateAvailability.$inferSelect;
export type Application = typeof applications.$inferSelect;
export type Interview = typeof interviews.$inferSelect;
export type OnboardingTask = typeof onboardingTasks.$inferSelect;
export type AuditLog = typeof auditLogs.$inferSelect;
export type InsertAuditLog = z.infer<typeof insertAuditLogSchema>;

// Interview automation types
export type InterviewV2 = typeof interviewsV2.$inferSelect;
export type InsertInterviewV2 = z.infer<typeof insertInterviewV2Schema>;
export type AgentProfile = typeof agentProfiles.$inferSelect;
export type InsertAgentProfile = z.infer<typeof insertAgentProfileSchema>;
export type InterviewRun = typeof interviewRuns.$inferSelect;
export type InsertInterviewRun = z.infer<typeof insertInterviewRunSchema>;
export type InterviewArtifact = typeof interviewArtifacts.$inferSelect;
export type InsertInterviewArtifact = z.infer<typeof insertInterviewArtifactSchema>;
