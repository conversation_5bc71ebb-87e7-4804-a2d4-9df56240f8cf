
import { useState, useEffect } from 'react';

interface JobPosting {
  id: string;
  title: string;
  description: string;
  requirements?: string;
  department?: string;
  location?: string;
  salaryRange?: string;
  employmentType?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export const useJobPostings = () => {
  const [jobPostings, setJobPostings] = useState<JobPosting[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchJobPostings();
  }, []);

  const fetchJobPostings = async () => {
    try {
      // For now, return mock data until we implement the full job postings API
      const mockJobPostings: JobPosting[] = [
        {
          id: '1',
          title: 'Senior Software Engineer',
          description: 'We are looking for a Senior Software Engineer to join our team...',
          requirements: 'Bachelor\'s degree in Computer Science, 5+ years experience with React, Node.js, and PostgreSQL',
          department: 'Engineering',
          location: 'Remote',
          salaryRange: '$120,000 - $160,000',
          employmentType: 'full-time',
          isActive: true
        },
        {
          id: '2',
          title: 'Product Manager',
          description: 'Seeking an experienced Product Manager to drive product strategy...',
          requirements: 'MBA preferred, 3+ years in product management, experience with SaaS products',
          department: 'Product',
          location: 'San Francisco, CA',
          salaryRange: '$130,000 - $170,000',
          employmentType: 'full-time',
          isActive: true
        }
      ];
      setJobPostings(mockJobPostings);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const createJobPosting = async (jobData: Omit<JobPosting, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      // For now, just add to local state - implement server API later
      const newJob: JobPosting = {
        ...jobData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      setJobPostings(prev => [newJob, ...prev]);
      return { data: newJob, error: null };
    } catch (err) {
      return { data: null, error: err instanceof Error ? err.message : 'An error occurred' };
    }
  };

  return {
    jobPostings,
    loading,
    error,
    fetchJobPostings,
    createJobPosting,
  };
};
