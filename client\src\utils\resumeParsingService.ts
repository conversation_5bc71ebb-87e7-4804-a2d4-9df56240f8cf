
interface ContactInfo {
  name?: string;
  email?: string;
  phone?: string;
  location?: string;
  linkedin?: string;
}

interface ExperienceInfo {
  workExperience: Array<{
    title: string;
    company: string;
    duration: string;
    description: string;
  }>;
  education: Array<{
    degree: string;
    institution: string;
    year: string;
  }>;
  skills: string[];
  summary: string;
}

interface ResumeContent {
  fullText: string;
  contactInfo: ContactInfo;
  experienceInfo: ExperienceInfo;
}

export interface SimpleParseResult {
  contactInfo: ContactInfo;
  experienceInfo: ExperienceInfo;
  content: ResumeContent;
  parseMethod: string;
  success: boolean;
}

import { api } from '@/lib/api';

// Convert file to base64 for sending to server
const fileToBase64 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      const result = reader.result as string;
      // Remove the data URL prefix (e.g., "data:application/pdf;base64,")
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = error => reject(error);
  });
};

// Parse resume with AI (contact info only or full parsing)
const parseResumeWithAI = async (file: File, extractContactOnly: boolean = true): Promise<{
  contactInfo: ContactInfo;
  experienceInfo?: ExperienceInfo;
}> => {
  try {
    console.log(`Starting AI ${extractContactOnly ? 'contact' : 'full'} extraction for:`, file.name);
    
    // Convert file to base64
    const base64Content = await fileToBase64(file);
    
    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout for full parsing
    
    const result = await api.parseResume(base64Content, file.name, file.type, extractContactOnly);

    clearTimeout(timeoutId);

    if (result.error) {
      throw new Error(result.error);
    }

    console.log("AI extraction completed:", result);
    
    return {
      contactInfo: result.contactInfo || {},
      experienceInfo: result.experienceInfo || {
        workExperience: [],
        education: [],
        skills: [],
        summary: ""
      }
    };
  } catch (error) {
    console.error('Error parsing resume with AI:', error);
    
    if (error.name === 'AbortError') {
      throw new Error('Resume parsing timed out. Please try again with a smaller file.');
    }
    
    throw error;
  }
};

// Main parsing function for contact info only
export const performSimpleAIParsing = async (file: File): Promise<SimpleParseResult> => {
  console.log("=== STARTING CONTACT INFO EXTRACTION ===");
  
  try {
    // Enhanced file validation
    if (file.size > 25 * 1024 * 1024) {
      throw new Error('File too large. Please use files smaller than 25MB.');
    }
    
    // Check if file type is supported
    const supportedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
      'application/msword', // .doc
      'text/plain'
    ];
    
    const isSupported = supportedTypes.includes(file.type) || 
                       file.name.toLowerCase().endsWith('.pdf') ||
                       file.name.toLowerCase().endsWith('.docx') ||
                       file.name.toLowerCase().endsWith('.doc') ||
                       file.name.toLowerCase().endsWith('.txt');
    
    if (!isSupported) {
      throw new Error('Unsupported file type. Please use PDF, DOCX, DOC, or TXT files.');
    }
    
    console.log(`Processing ${file.name} (${file.type}, ${file.size} bytes)`);
    
    // Parse resume for contact info only
    const { contactInfo } = await parseResumeWithAI(file, true);
    
    return {
      contactInfo,
      experienceInfo: {
        workExperience: [],
        education: [],
        skills: [],
        summary: ""
      },
      content: {
        fullText: `File processed: ${file.name}`,
        contactInfo,
        experienceInfo: {
          workExperience: [],
          education: [],
          skills: [],
          summary: ""
        }
      },
      parseMethod: 'OpenAI Vision API (Contact Info Only)',
      success: true
    };
    
  } catch (error) {
    console.error("Error in contact info extraction:", error);
    
    return {
      contactInfo: {},
      experienceInfo: {
        workExperience: [],
        education: [],
        skills: [],
        summary: ""
      },
      content: {
        fullText: `Failed to parse resume: ${error.message}`,
        contactInfo: {},
        experienceInfo: {
          workExperience: [],
          education: [],
          skills: [],
          summary: ""
        }
      },
      parseMethod: 'Processing Error',
      success: false
    };
  }
};

// Full resume parsing function (with experience, education, skills)
export const performFullResumeParsing = async (file: File): Promise<SimpleParseResult> => {
  console.log("=== STARTING FULL RESUME PARSING ===");
  
  try {
    // Same file validation as contact parsing
    if (file.size > 25 * 1024 * 1024) {
      throw new Error('File too large. Please use files smaller than 25MB.');
    }
    
    const supportedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain'
    ];
    
    const isSupported = supportedTypes.includes(file.type) || 
                       file.name.toLowerCase().endsWith('.pdf') ||
                       file.name.toLowerCase().endsWith('.docx') ||
                       file.name.toLowerCase().endsWith('.doc') ||
                       file.name.toLowerCase().endsWith('.txt');
    
    if (!isSupported) {
      throw new Error('Unsupported file type. Please use PDF, DOCX, DOC, or TXT files.');
    }
    
    console.log(`Processing ${file.name} (${file.type}, ${file.size} bytes) for full parsing`);
    
    // Parse resume for all information
    const { contactInfo, experienceInfo } = await parseResumeWithAI(file, false);
    
    return {
      contactInfo,
      experienceInfo: experienceInfo || {
        workExperience: [],
        education: [],
        skills: [],
        summary: ""
      },
      content: {
        fullText: `Full resume parsed: ${file.name}`,
        contactInfo,
        experienceInfo: experienceInfo || {
          workExperience: [],
          education: [],
          skills: [],
          summary: ""
        }
      },
      parseMethod: 'OpenAI Vision API (Full Parsing)',
      success: true
    };
    
  } catch (error) {
    console.error("Error in full resume parsing:", error);
    
    return {
      contactInfo: {},
      experienceInfo: {
        workExperience: [],
        education: [],
        skills: [],
        summary: ""
      },
      content: {
        fullText: `Failed to parse resume: ${error.message}`,
        contactInfo: {},
        experienceInfo: {
          workExperience: [],
          education: [],
          skills: [],
          summary: ""
        }
      },
      parseMethod: 'Processing Error',
      success: false
    };
  }
};

// Helper function to calculate years of experience from work history
export const calculateExperienceYears = (workExperience: ExperienceInfo['workExperience']): number => {
  if (!workExperience || workExperience.length === 0) return 0;
  
  let totalMonths = 0;
  
  workExperience.forEach(exp => {
    // Try to parse duration strings like "Jan 2020 - Dec 2022" or "2020-2022"
    const durationMatch = exp.duration.match(/(\d{4})/g);
    if (durationMatch && durationMatch.length >= 2) {
      const startYear = parseInt(durationMatch[0]);
      const endYear = parseInt(durationMatch[1]);
      totalMonths += (endYear - startYear) * 12;
    } else if (exp.duration.toLowerCase().includes('present')) {
      // If current job, estimate from start year to now
      const yearMatch = exp.duration.match(/(\d{4})/);
      if (yearMatch) {
        const startYear = parseInt(yearMatch[0]);
        const currentYear = new Date().getFullYear();
        totalMonths += (currentYear - startYear) * 12;
      }
    }
  });
  
  return Math.round(totalMonths / 12);
};
