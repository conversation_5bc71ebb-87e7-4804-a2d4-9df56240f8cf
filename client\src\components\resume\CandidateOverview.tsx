import React from 'react';
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Target, GraduationCap, Mail, Phone, MapPin, Linkedin } from "lucide-react";
import { EnhancedAnalysisResult } from "@/types/resumeAnalysis";

interface CandidateOverviewProps {
  analysisResult: EnhancedAnalysisResult;
}

const CandidateOverview: React.FC<CandidateOverviewProps> = ({ analysisResult }) => {
  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'HIRE': return 'bg-green-100 text-green-800';
      case 'INTERVIEW': return 'bg-yellow-100 text-yellow-800';
      case 'REJECT': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Provide fallback values for missing data
  const candidateName = analysisResult.name || 'Candidate Name Not Available';
  const experienceYears = analysisResult.experience_years || 0;
  const matchScore = analysisResult.match_score || 0;
  const education = analysisResult.education || 'Education information not available';

  return (
    <div className="space-y-4">
      {/* Contact Information Section */}
      <Card className="p-4">
        <div className="flex items-center mb-3">
          <User className="w-5 h-5 mr-2 text-blue-600" />
          <span className="font-medium text-lg">Contact Information</span>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-xl font-bold text-gray-900">{candidateName}</p>
            <p className="text-sm text-gray-600">{experienceYears} years experience</p>
          </div>
          
          <div className="space-y-2">
            {analysisResult.email && analysisResult.email !== 'Not Available' && (
              <div className="flex items-center text-sm text-gray-700">
                <Mail className="w-4 h-4 mr-2 text-gray-500" />
                <span>{analysisResult.email}</span>
              </div>
            )}
            
            {analysisResult.phone && analysisResult.phone !== 'Not Available' && (
              <div className="flex items-center text-sm text-gray-700">
                <Phone className="w-4 h-4 mr-2 text-gray-500" />
                <span>{analysisResult.phone}</span>
              </div>
            )}
            
            {analysisResult.location && analysisResult.location !== 'Not Available' && (
              <div className="flex items-center text-sm text-gray-700">
                <MapPin className="w-4 h-4 mr-2 text-gray-500" />
                <span>{analysisResult.location}</span>
              </div>
            )}
            
            {analysisResult.linkedin && analysisResult.linkedin !== 'Not Available' && (
              <div className="flex items-center text-sm text-gray-700">
                <Linkedin className="w-4 h-4 mr-2 text-gray-500" />
                <a href={analysisResult.linkedin} target="_blank" rel="noopener noreferrer" 
                   className="text-blue-600 hover:underline">
                  LinkedIn Profile
                </a>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Analysis Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="p-4">
          <div className="flex items-center mb-2">
            <Target className="w-4 h-4 mr-2 text-green-600" />
            <span className="font-medium">Match Score</span>
          </div>
          <p className={`text-2xl font-bold ${matchScore > 0 ? 'text-green-600' : 'text-gray-400'}`}>
            {matchScore}%
          </p>
          <p className="text-sm text-gray-600">Job compatibility</p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center mb-2">
            <GraduationCap className="w-4 h-4 mr-2 text-purple-600" />
            <span className="font-medium">Education</span>
          </div>
          <p className="text-sm">{education}</p>
        </Card>
      </div>
    </div>
  );
};

export default CandidateOverview;
