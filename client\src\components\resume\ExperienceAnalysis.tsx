
import React from 'react';
import { Card } from "@/components/ui/card";
import { EnhancedAnalysisResult } from "@/types/resumeAnalysis";

interface ExperienceAnalysisProps {
  experienceAnalysis: EnhancedAnalysisResult['experience_analysis'];
}

const ExperienceAnalysis: React.FC<ExperienceAnalysisProps> = ({ experienceAnalysis }) => {
  // Provide safe defaults if experienceAnalysis is undefined or missing properties
  const safeExperienceAnalysis = {
    relevant_experience: experienceAnalysis?.relevant_experience || 'Analysis not available',
    experience_gap: experienceAnalysis?.experience_gap || 'Analysis not available',
    career_progression: experienceAnalysis?.career_progression || 'Analysis not available'
  };

  return (
    <div className="space-y-3">
      <h4 className="font-medium text-lg">Experience Analysis</h4>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-3">
          <h5 className="font-medium text-sm text-gray-600 mb-1">Relevant Experience</h5>
          <p className="text-sm">{safeExperienceAnalysis.relevant_experience}</p>
        </Card>
        <Card className="p-3">
          <h5 className="font-medium text-sm text-gray-600 mb-1">Experience Gap</h5>
          <p className="text-sm">{safeExperienceAnalysis.experience_gap}</p>
        </Card>
        <Card className="p-3">
          <h5 className="font-medium text-sm text-gray-600 mb-1">Career Progression</h5>
          <p className="text-sm">{safeExperienceAnalysis.career_progression}</p>
        </Card>
      </div>
    </div>
  );
};

export default ExperienceAnalysis;
