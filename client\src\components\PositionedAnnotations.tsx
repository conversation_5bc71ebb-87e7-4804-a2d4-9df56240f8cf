import React, { useState, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  MessageCircle, 
  Plus, 
  X,
  AlertCircle,
  CheckCircle,
  Star,
  Eye
} from 'lucide-react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api';

interface PositionedAnnotation {
  id: string;
  content: string;
  type: 'comment' | 'highlight' | 'note' | 'question' | 'approval' | 'concern';
  position: {
    x: number;
    y: number;
    selector?: string;
    selectedText?: string;
  };
  authorName: string;
  createdAt: string;
}

interface PositionedAnnotationsProps {
  entityType: string;
  entityId: string;
  annotations: PositionedAnnotation[];
  onAnnotationSelect?: (annotation: PositionedAnnotation) => void;
}

export default function PositionedAnnotations({ 
  entityType, 
  entityId, 
  annotations,
  onAnnotationSelect 
}: PositionedAnnotationsProps) {
  const [isCreating, setIsCreating] = useState(false);
  const [newAnnotation, setNewAnnotation] = useState<{
    content: string;
    type: 'comment' | 'highlight' | 'note' | 'question' | 'approval' | 'concern';
    position: { x: number; y: number };
    selectedText: string;
  }>({
    content: '',
    type: 'comment',
    position: { x: 0, y: 0 },
    selectedText: ''
  });
  const [selectedText, setSelectedText] = useState('');
  
  const containerRef = useRef<HTMLDivElement>(null);
  // Get user from localStorage or context
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const queryClient = useQueryClient();

  // Create positioned annotation mutation
  const createAnnotationMutation = useMutation({
    mutationFn: async (annotationData: any) => {
      const response = await fetch('/api/annotations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(annotationData),
      });
      if (!response.ok) throw new Error('Failed to create annotation');
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/annotations/${entityType}/${entityId}`] });
      setIsCreating(false);
      setNewAnnotation({ content: '', type: 'comment', position: { x: 0, y: 0 }, selectedText: '' });
    },
  });

  // Handle text selection and annotation creation
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const selectedText = selection.toString().trim();
    if (!selectedText) return;

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    const containerRect = containerRef.current?.getBoundingClientRect();
    
    if (!containerRect) return;

    const x = rect.left - containerRect.left + rect.width / 2;
    const y = rect.top - containerRect.top;

    setSelectedText(selectedText);
    setNewAnnotation({
      content: '',
      type: 'comment',
      position: { x, y },
      selectedText
    });
    setIsCreating(true);

    // Clear selection
    selection.removeAllRanges();
  }, []);

  // Handle creating annotation at click position
  const handleContainerClick = useCallback((e: React.MouseEvent) => {
    if (e.target === containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      setNewAnnotation({
        content: '',
        type: 'comment',
        position: { x, y },
        selectedText: ''
      });
      setIsCreating(true);
    }
  }, []);

  const handleCreateAnnotation = async () => {
    if (!newAnnotation.content.trim()) return;

    createAnnotationMutation.mutate({
      content: newAnnotation.content,
      type: newAnnotation.type,
      entityType,
      entityId,
      position: {
        x: newAnnotation.position.x,
        y: newAnnotation.position.y,
        selectedText: newAnnotation.selectedText || undefined
      }
    });
  };

  const getTypeIcon = (type: PositionedAnnotation['type']) => {
    switch (type) {
      case 'question': return <AlertCircle className="w-3 h-3 text-yellow-500" />;
      case 'approval': return <CheckCircle className="w-3 h-3 text-green-500" />;
      case 'concern': return <AlertCircle className="w-3 h-3 text-red-500" />;
      case 'note': return <Star className="w-3 h-3 text-blue-500" />;
      case 'highlight': return <Eye className="w-3 h-3 text-purple-500" />;
      default: return <MessageCircle className="w-3 h-3 text-gray-500" />;
    }
  };

  const getTypeColor = (type: PositionedAnnotation['type']) => {
    switch (type) {
      case 'question': return 'border-yellow-500 bg-yellow-50';
      case 'approval': return 'border-green-500 bg-green-50';
      case 'concern': return 'border-red-500 bg-red-50';
      case 'note': return 'border-blue-500 bg-blue-50';
      case 'highlight': return 'border-purple-500 bg-purple-50';
      default: return 'border-gray-500 bg-gray-50';
    }
  };

  return (
    <div 
      ref={containerRef}
      className="relative min-h-[400px] p-4 border-2 border-dashed border-gray-200 rounded-lg select-text"
      onMouseUp={handleTextSelection}
      onClick={handleContainerClick}
    >
      {/* Instructions */}
      <div className="absolute top-2 left-2 text-sm text-gray-500 pointer-events-none">
        Select text or click anywhere to add annotations
      </div>

      {/* Positioned annotations */}
      {annotations.map(annotation => (
        <div
          key={annotation.id}
          className={`absolute cursor-pointer transform -translate-x-1/2 z-10 ${getTypeColor(annotation.type)}`}
          style={{
            left: annotation.position.x,
            top: annotation.position.y,
          }}
          onClick={(e) => {
            e.stopPropagation();
            onAnnotationSelect?.(annotation);
          }}
        >
          <div className="flex items-center justify-center w-8 h-8 rounded-full border-2 hover:scale-110 transition-transform">
            {getTypeIcon(annotation.type)}
          </div>
          
          {/* Tooltip on hover */}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden group-hover:block">
            <Card className="w-64 z-20">
              <CardContent className="p-3">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    {getTypeIcon(annotation.type)}
                    <Badge variant="outline" className="text-xs">
                      {annotation.type}
                    </Badge>
                  </div>
                  <p className="text-sm">{annotation.content}</p>
                  {annotation.position.selectedText && (
                    <div className="text-xs text-gray-500 italic border-l-2 border-gray-300 pl-2">
                      "{annotation.position.selectedText}"
                    </div>
                  )}
                  <div className="text-xs text-gray-400">
                    by {annotation.authorName} • {new Date(annotation.createdAt).toLocaleString()}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      ))}

      {/* New annotation form */}
      {isCreating && (
        <div
          className="absolute z-20 transform -translate-x-1/2"
          style={{
            left: newAnnotation.position.x,
            top: newAnnotation.position.y,
          }}
        >
          <Card className="w-80">
            <CardContent className="p-4 space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="font-medium">New Annotation</h4>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setIsCreating(false)}
                  className="h-6 w-6 p-0"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>

              {newAnnotation.selectedText && (
                <div className="text-sm text-gray-600 italic border-l-2 border-gray-300 pl-2">
                  Selected: "{newAnnotation.selectedText}"
                </div>
              )}

              <div className="flex gap-1 flex-wrap">
                {(['comment', 'note', 'question', 'approval', 'concern'] as const).map(type => (
                  <Button
                    key={type}
                    variant={newAnnotation.type === type ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setNewAnnotation(prev => ({ ...prev, type }))}
                    className="flex items-center gap-1 text-xs h-7"
                  >
                    {getTypeIcon(type)}
                    {type}
                  </Button>
                ))}
              </div>

              <Textarea
                value={newAnnotation.content}
                onChange={(e) => setNewAnnotation(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Add your annotation..."
                className="min-h-[80px] text-sm"
              />

              <div className="flex gap-2">
                <Button 
                  size="sm"
                  onClick={handleCreateAnnotation}
                  disabled={!newAnnotation.content.trim() || createAnnotationMutation.isPending}
                  className="flex items-center gap-1"
                >
                  <Plus className="w-3 h-3" />
                  Add
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setIsCreating(false)}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}